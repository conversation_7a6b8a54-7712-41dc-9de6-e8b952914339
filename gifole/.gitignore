node_modules
HELP.md
target/
.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### Vaadin ###
node_modules/
src/main/frontend/generated/
src/main/dev-bundle/
webpack.generated.js
tsconfig.json
types.d.ts
vite.generated.ts
vite.config.ts
package.json
package-lock.json
pnpm-lock.yaml
yarn.lock

### Frontend Build ###
src/main/resources/META-INF/VAADIN/
src/main/resources/META-INF/resources/
!src/main/resources/META-INF/resources/themes/

### Database ###
*.db
*.trace.db
*.lock.db

### Logs ###
*.log
logs/
spring.log

### Temporary files ###
*.tmp
*.temp
*.swp
*.swo
*~
.DS_Store
Thumbs.db

### Environment ###
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

### Application specific ###
application-local.properties
application-dev.properties
application-prod.properties

### Maven ###
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.properties

### Backup files ###
*.bak
*.backup
*.orig

### Coverage reports ###
coverage/
*.lcov
jacoco.xml

### Test results ###
test-results/
junit.xml
