<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.3</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>pe.com.bbva</groupId>
	<artifactId>gifole</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>war</packaging>
	<name>gifole</name>
	<description>Demo project for Spring Boot</description>
	<url/>
	<licenses>
		<license/>
	</licenses>
	<developers>
		<developer/>
	</developers>
	<scm>
		<connection/>
		<developerConnection/>
		<tag/>
		<url/>
	</scm>
	<properties>
		<java.version>21</java.version>
		<vaadin.version>24.8.2</vaadin.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.vaadin</groupId>
			<artifactId>vaadin-spring-boot-starter</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.owasp.esapi</groupId>
			<artifactId>esapi</artifactId>
			<version>2.2.2.0</version>
			<exclusions>
				<exclusion>
					<groupId>commons-io</groupId>
					<artifactId>commons-io</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.googlecode.owasp-java-html-sanitizer</groupId>
			<artifactId>owasp-java-html-sanitizer</artifactId>
			<version>r156</version>
		</dependency>

		<!-- Log4j2 dependencies -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-log4j2</artifactId>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.vaadin</groupId>
				<artifactId>vaadin-bom</artifactId>
				<version>${vaadin.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<profiles>
		<profile>
			<id>local</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<spring.profiles.active>local</spring.profiles.active>
				<profile.dir>local</profile.dir>
			</properties>
			<build>
				<plugins>
					<plugin>
						<groupId>com.vaadin</groupId>
						<artifactId>vaadin-maven-plugin</artifactId>
						<version>${vaadin.version}</version>
						<executions>
							<execution>
								<id>frontend</id>
								<phase>compile</phase>
								<goals>
									<goal>prepare-frontend</goal>
									<goal>build-frontend</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>dev</id>
			<properties>
				<spring.profiles.active>dev</spring.profiles.active>
				<profile.dir>dev</profile.dir>
			</properties>
			<build>
				<plugins>
					<plugin>
						<groupId>com.vaadin</groupId>
						<artifactId>vaadin-maven-plugin</artifactId>
						<version>${vaadin.version}</version>
						<executions>
							<execution>
								<id>frontend</id>
								<phase>compile</phase>
								<goals>
									<goal>prepare-frontend</goal>
									<goal>build-frontend</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>qa</id>
			<properties>
				<spring.profiles.active>qa</spring.profiles.active>
				<profile.dir>qa</profile.dir>
			</properties>
			<build>
				<plugins>
					<plugin>
						<groupId>com.vaadin</groupId>
						<artifactId>vaadin-maven-plugin</artifactId>
						<version>${vaadin.version}</version>
						<executions>
							<execution>
								<id>frontend</id>
								<phase>compile</phase>
								<goals>
									<goal>prepare-frontend</goal>
									<goal>build-frontend</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<spring.profiles.active>prod</spring.profiles.active>
				<profile.dir>prod</profile.dir>
			</properties>
			<build>
				<plugins>
					<plugin>
						<groupId>com.vaadin</groupId>
						<artifactId>vaadin-maven-plugin</artifactId>
						<version>${vaadin.version}</version>
						<executions>
							<execution>
								<id>frontend</id>
								<phase>compile</phase>
								<goals>
									<goal>prepare-frontend</goal>
									<goal>build-frontend</goal>
								</goals>
							</execution>
						</executions>
						<configuration>
							<productionMode>true</productionMode>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<build>
		<finalName>gifole-web-ui</finalName>
		<plugins>
			<plugin>
				<groupId>com.diffplug.spotless</groupId>
				<artifactId>spotless-maven-plugin</artifactId>
				<version>2.40.0</version>
				<executions>
					<execution>
						<id>spotless-apply</id>
						<phase>process-sources</phase>
						<goals>
							<goal>apply</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<java>
						<includes>
							<include>src/main/java/**/*.java</include>
							<include>src/test/java/**/*.java</include>
						</includes>
						<googleJavaFormat>
							<version>1.17.0</version>
							<style>GOOGLE</style>
						</googleJavaFormat>
						<importOrder />
						<removeUnusedImports />
						<trimTrailingWhitespace />
						<endWithNewline />
					</java>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
					<!-- Deshabilitar repackaging para despliegue en servidor externo -->
					<skip>true</skip>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<configuration>
					<warName>gifole-web-ui</warName>
				</configuration>
			</plugin>
		</plugins>
		<resources>
			<!-- Incluir recursos base -->
			<resource>
				<directory>src/main/resources</directory>
				<excludes>
					<exclude>profiles/**</exclude>
				</excludes>
			</resource>
			<!-- Incluir solo el perfil específico -->
			<resource>
				<directory>src/main/resources/profiles/${profile.dir}</directory>
			</resource>
		</resources>
	</build>

</project>
