package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.SeguroHogarRoyalBean;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Seguro Hogar Royal")
@Route(value = "reporte/seguros/consulta-seguro-hogar-royal", layout = MainLayout.class)
public class ConsultaSeguroHogarRoyalView extends VerticalLayout {

  public ConsultaSeguroHogarRoyalView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Seguro Hogar Royal");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    DataTable<SeguroHogarRoyalBean> dataTable =
        DataTable.<SeguroHogarRoyalBean>builder()
            .id("tabla-seguro-hogar")
            .column("fechaRegistro", "Fecha Registro", bean -> bean.getFechaRegistro(), "150px")
            .column("nombre", "Nombre", bean -> StringUtils.trimToEmpty(bean.getNombre()), "150px")
            .column(
                "apellido",
                "Apellido",
                bean -> StringUtils.trimToEmpty(bean.getApellido()),
                "150px")
            .column(
                "documento",
                "Documento",
                bean -> StringUtils.trimToEmpty(bean.getDocumento()),
                "120px")
            .column("correo", "Correo", bean -> StringUtils.trimToEmpty(bean.getCorreo()), "200px")
            .column(
                "telefono",
                "Teléfono",
                bean -> StringUtils.trimToEmpty(bean.getTelefono()),
                "120px")
            .column(
                "indicadorSiEsCliente",
                "¿Es Cliente?",
                bean -> StringUtils.trimToEmpty(bean.getIndicadorSiEsCliente()),
                "100px")
            .column(
                "propiedad",
                "Propiedad",
                bean -> StringUtils.trimToEmpty(bean.getPropiedad()),
                "100px")
            .column(
                "distrito",
                "Distrito",
                bean -> StringUtils.trimToEmpty(bean.getDistrito()),
                "120px")
            .column(
                "departamento",
                "Departamento",
                bean -> StringUtils.trimToEmpty(bean.getDepartamento()),
                "120px")
            .column(
                "provincia",
                "Provincia",
                bean -> StringUtils.trimToEmpty(bean.getProvincia()),
                "120px")
            .column("moneda", "Moneda", bean -> StringUtils.trimToEmpty(bean.getMoneda()), "100px")
            .column("monto", "Monto", bean -> StringUtils.trimToEmpty(bean.getMonto()), "100px")
            .column("uso", "Uso", bean -> StringUtils.trimToEmpty(bean.getUso()), "100px")
            .column(
                "material",
                "Material",
                bean -> StringUtils.trimToEmpty(bean.getMaterial()),
                "100px")
            .column(
                "antiguedad",
                "Antigüedad",
                bean -> StringUtils.trimToEmpty(bean.getAntiguedad()),
                "100px")
            .column(
                "numeroPisos",
                "Pisos",
                bean -> StringUtils.trimToEmpty(bean.getNumeroPisos()),
                "80px")
            .column(
                "possedor",
                "Possedor",
                bean -> StringUtils.trimToEmpty(bean.getPossedor()),
                "100px")
            .column(
                "planCuotasMoneda",
                "Plan Cuotas Moneda",
                bean -> StringUtils.trimToEmpty(bean.getPlanCuotasMoneda()),
                "120px")
            .column(
                "planCuotasMonto",
                "Plan Cuotas Monto",
                bean -> StringUtils.trimToEmpty(bean.getPlanCuotasMonto()),
                "120px")
            .column(
                "primaMoneda",
                "Prima Moneda",
                bean -> StringUtils.trimToEmpty(bean.getPrimaMontoMoneda()),
                "120px")
            .column(
                "primaMonto",
                "Prima Monto",
                bean -> StringUtils.trimToEmpty(bean.getPrimaMonto()),
                "120px")
            .column(
                "objetoCotizacion",
                "Objeto Cotización",
                bean -> StringUtils.trimToEmpty(bean.getObjetoCotizacion()),
                "150px")
            .column(
                "informacionBanco",
                "Información Banco",
                bean -> StringUtils.trimToEmpty(bean.getInformacionBanco()),
                "200px")
            .column(
                "codigoOficina",
                "Código Oficina",
                bean -> StringUtils.trimToEmpty(bean.getCodigoOficina()),
                "120px")
            .column(
                "codigoUsuario",
                "Código Usuario",
                bean -> StringUtils.trimToEmpty(bean.getCodigoUsuario()),
                "120px")
            .column("canal", "Canal", bean -> StringUtils.trimToEmpty(bean.getCanal()), "100px")
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
