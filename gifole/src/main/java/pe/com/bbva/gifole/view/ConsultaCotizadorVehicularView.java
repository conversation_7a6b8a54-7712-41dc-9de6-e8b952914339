package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.domain.VehCotizacionDetalle;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Cotizador Vehicular")
@Route(value = "reporte/cotizador-vehicular", layout = MainLayout.class)
public class ConsultaCotizadorVehicularView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<VehCotizacionDetalle> allData = new ArrayList<>();
    private DataTable<VehCotizacionDetalle> dataTable;

    public ConsultaCotizadorVehicularView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Cotizador Vehicular");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<VehCotizacionDetalle>builder()
                .id("tabla-cotizador-vehicular") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaCotizadorVehicularUI)
                // Seleccionadas según las columnas del DTO proporcionado y el análisis de la UI antigua
                .column("numPla", "Número Placa", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getNumPlaca()), "100px")
                .column("marca", "Marca", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getMarca().getNombre()), "120px")
                .column("modelo", "Modelo", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getModelo().getNombre()), "120px")
                .column("anhoFabricacion", "Año Fabricación", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getAnhoFabricacion()), "100px")
                .column("cambioGas", "Cambio Gas", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getCambioGas()), "100px")
                .column("valorComercial", "Valor Comercial", 
                    bean -> StringUtils.trimToEmpty(bean.getValor()), "120px")
                .column("nombre", "Nombre", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getNombre()), "150px")
                .column("tipoDocumento", "Tipo Documento", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getTipoDocumento()), "120px")
                .column("documento", "Documento", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getDocumento()), "100px")
                .column("correo", "Correo", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getCorreo()), "150px")
                .column("telefono", "Teléfono", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getTelefono()), "100px")
                .column("autorizacion", "Autorización", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getAutorizacion()), "100px")
                .column("lugarRegistro", "Lugar Registro", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getLugarRegistro()), "120px")
                .column("tipo", "Tipo", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getTipo().getNombre()), "120px")
                .column("horario", "Horario", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getHorario()), "100px")
                .column("nroCotizacion", "Nro. Cotización", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getNroCotizacion()), "120px")
                .column("planElegido", "Plan Elegido", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getPlanElegido()), "120px")
                .column("divisaPlanElegido", "Divisa Plan", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getDivisaPlanElegido()), "100px")
                .column("montoPlanElegido", "Monto Plan", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getMontoPlanElegido()), "120px")
                .column("canal", "Canal", 
                    bean -> StringUtils.trimToEmpty(bean.getCotizacion().getCanal()), "100px")
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}