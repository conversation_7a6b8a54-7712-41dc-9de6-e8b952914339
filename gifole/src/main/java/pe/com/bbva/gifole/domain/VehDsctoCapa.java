package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_DSCTOCAPA")
public class VehDsctoCapa implements Serializable {

  @Id
  @Column(unique = true, nullable = false, precision = 16)
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_VEH_DSCTOCAPA")
  @TableGenerator(
      name = "SEQ_VEH_DSCTOCAPA",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.VehDsctoCapa",
      allocationSize = 1)
  private Long id;

  @Column(name = "CODIGO_CAPA", length = 4)
  private String codigoCapa;

  @Column(name = "FEC_INI_VIG")
  private Date fechIniVig;

  @Column(name = "FEC_FIN_VIG")
  private Date fechFinVig;

  @Column(name = "TIPO_FACTOR", length = 1)
  private String tipoFactor;

  @Column(name = "TIPO_TASA", length = 1)
  private String tipoTasa;

  @Column(name = "VALOR_TASA", length = 8, precision = 3)
  private BigDecimal valorTasa;
}
