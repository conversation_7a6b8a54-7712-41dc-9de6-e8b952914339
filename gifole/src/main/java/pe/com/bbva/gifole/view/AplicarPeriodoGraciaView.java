package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import pe.com.bbva.gifole.common.bean.PrestamoAlToqueBean;

@PageTitle("Aplicar Período de Gracia")
@Route(value = "aplicar-periodo-gracia", layout = MainLayout.class)
public class AplicarPeriodoGraciaView extends VerticalLayout {

  // Constantes
  private static final String REGISTRADO = "REGISTRADO";
  private static final String BANCA_POR_INTERNET = "BANCA POR INTERNET";
  private static final String ZONA_PUB = "ZONA_PUB";
  private static final String PROCESAR = "Procesar";
  private static final String SEBASTIAN_ROCA = "SEBASTIAN ROCA VIZCARRA";

  private final Grid<PrestamoAlToqueBean> grid = new Grid<>(PrestamoAlToqueBean.class, false);
  private final List<PrestamoAlToqueBean> prestamosList = new ArrayList<>();

  public AplicarPeriodoGraciaView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    // Layout principal
    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Card para el Grid
    VerticalLayout gridCard = createGridCard();

    mainLayout.add(gridCard);
    mainLayout.setFlexGrow(1, gridCard);

    add(mainLayout);

    // Cargar datos de ejemplo
    loadSampleData();
  }

  private VerticalLayout createGridCard() {
    VerticalLayout gridCard = new VerticalLayout();
    gridCard.setSizeFull();
    gridCard.addClassName("bbva-grid-card");

    H2 gridTitle = new H2("Bandeja de Solicitudes de período de gracia");
    gridTitle.addClassName("bbva-grid-title");

    configureGrid();
    gridCard.add(gridTitle, grid);

    return gridCard;
  }

  private void configureGrid() {
    grid.setSizeFull();
    grid.addClassName("periodo-gracia-grid");

    // Configurar columnas según la imagen
    grid.addColumn(prestamo -> formatDate(prestamo.getFechaRegistro()))
        .setHeader("Fecha Registro")
        .setSortable(true);

    grid.addColumn(PrestamoAlToqueBean::getCodigoCentral)
        .setHeader("Cod Central")
        .setSortable(true);

    grid.addColumn(PrestamoAlToqueBean::getNombreCompleto)
        .setHeader("Nombres Titular")
        .setSortable(true);

    grid.addColumn(PrestamoAlToqueBean::getCorreo).setHeader("Correo").setSortable(true);

    grid.addColumn(PrestamoAlToqueBean::getNumeroContrato).setHeader("Contrato").setSortable(true);

    grid.addColumn(PrestamoAlToqueBean::getEstadoPeriodoGracia)
        .setHeader("Estado período de gracia")
        .setSortable(true);

    grid.addColumn(PrestamoAlToqueBean::getCanal).setHeader("Canal").setSortable(true);

    // Columna de acciones con botón Procesar
    grid.addComponentColumn(this::createProcessButton).setHeader("Acciones");

    // Configurar selección
    grid.setSelectionMode(Grid.SelectionMode.SINGLE);
  }

  private Button createProcessButton(PrestamoAlToqueBean prestamo) {
    Button processButton = new Button(PROCESAR);
    processButton.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
    processButton.addClassName("process-button");

    processButton.addClickListener(e -> procesarSolicitud(prestamo));

    return processButton;
  }

  private void procesarSolicitud(PrestamoAlToqueBean prestamo) {
    // Simular procesamiento
    prestamo.setEstadoPeriodoGracia("PROCESADO");
    prestamo.setFechaAprobacionPeriodoGracia(new Date());

    grid.getDataProvider().refreshItem(prestamo);

    Notification.show("Solicitud procesada exitosamente para: " + prestamo.getNombreCompleto())
        .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
  }

  private String formatDate(Date date) {
    if (date == null) return "";
    SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
    return sdf.format(date);
  }

  private void loadSampleData() {
    // Datos de ejemplo basados en la imagen proporcionada

    prestamosList.add(
        createPrestamoItem(
            createDate(2021, 2, 17, 1, 15, 56),
            "77780175",
            SEBASTIAN_ROCA,
            "<EMAIL>",
            "001108149600015703615",
            REGISTRADO,
            BANCA_POR_INTERNET));

    prestamosList.add(
        createPrestamoItem(
            createDate(2021, 2, 17, 3, 59, 1),
            "97167054",
            SEBASTIAN_ROCA,
            "<EMAIL>",
            "001108149600015721112",
            REGISTRADO,
            BANCA_POR_INTERNET));

    prestamosList.add(
        createPrestamoItem(
            createDate(2021, 3, 12, 11, 23, 32),
            "67167037",
            "JONATHAN LEON CRUZ",
            "***",
            "001108149600055864212",
            REGISTRADO,
            BANCA_POR_INTERNET));

    prestamosList.add(
        createPrestamoItem(
            createDate(2021, 4, 7, 3, 57, 23),
            "77781327",
            "FERNANDA UNO CASTILLO LEON",
            "<EMAIL>",
            "001108141596000262417",
            REGISTRADO,
            ZONA_PUB));

    prestamosList.add(
        createPrestamoItem(
            createDate(2021, 5, 24, 5, 38, 16),
            "87167054",
            SEBASTIAN_ROCA,
            "<EMAIL>",
            "001108149600015264811",
            REGISTRADO,
            BANCA_POR_INTERNET));

    prestamosList.add(
        createPrestamoItem(
            createDate(2021, 7, 7, 6, 7, 31),
            "88888888",
            "DIEGO REYNOSO NISHIDA",
            "<EMAIL>",
            "001108140200615889874",
            REGISTRADO,
            BANCA_POR_INTERNET));

    prestamosList.add(
        createPrestamoItem(
            createDate(2021, 7, 7, 7, 5, 14),
            "12345672",
            "MARIA PAREDES SANCHEZ",
            "<EMAIL>",
            "001108141596001554212",
            REGISTRADO,
            BANCA_POR_INTERNET));

    prestamosList.add(
        createPrestamoItem(
            createDate(2022, 1, 26, 3, 48, 2),
            "88888888",
            "DIEGO REYNOSO NISHIDA",
            "<EMAIL>",
            "001108140200158843",
            REGISTRADO,
            BANCA_POR_INTERNET));

    // Configurar grid con todos los datos
    grid.setItems(prestamosList);
  }

  private PrestamoAlToqueBean createPrestamoItem(
      Date fechaRegistro,
      String codigoCentral,
      String nombreCompleto,
      String correo,
      String numeroContrato,
      String estadoPeriodoGracia,
      String canal) {
    PrestamoAlToqueBean item = new PrestamoAlToqueBean();
    item.setFechaRegistro(fechaRegistro);
    item.setCodigoCentral(codigoCentral);
    item.setNombreCompleto(nombreCompleto);
    item.setCorreo(correo);
    item.setNumeroContrato(numeroContrato);
    item.setEstadoPeriodoGracia(estadoPeriodoGracia);
    item.setCanal(canal);
    return item;
  }

  @SuppressWarnings("deprecation")
  private Date createDate(int year, int month, int day, int hour, int minute, int second) {
    return new Date(year - 1900, month - 1, day, hour, minute, second);
  }
}
