package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.TCCruceTablonBean;

@SuppressWarnings("rawtypes")
@Component
public class TCCruceTablonMapper implements RowMapper {

  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    TCCruceTablonBean param = new TCCruceTablonBean();

    param.setIdTablon(rs.getLong("IDTABLON"));
    param.setIdReferido(rs.getLong("IDREFERIDO"));
    param.setIdReferidor(rs.getLong("IDREFERIDOR"));
    param.setCodigoCentralReferidor(rs.getString("CODIGOCENTRALREFERIDOR"));
    param.setTipoDocumentoReferidor(rs.getString("TIPODOCREFERIDOR"));
    param.setNroDocumentoReferidor(rs.getString("NRODOCREFERIDOR"));
    param.setEstadoHistorico(rs.getString("ESTADOHISTORICO"));
    param.setFechaHistorico(rs.getDate("FECHAHISTORICO"));
    param.setCantidadPremio(rs.getInt("CANTIDADPREMIO"));
    param.setNroDocumentoReferido(rs.getString("NRODOCREFERIDO"));
    param.setNroContrato(rs.getString("NROCONTRATO"));
    param.setNroTarjeta(rs.getString("NROTARJETA"));
    param.setFechaActivacion(rs.getDate("FECHAACTIVACION"));
    param.setFechaContratacion(rs.getDate("FECHACONTRATACION"));

    return param;
  }
}
