package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class PagoDiferidoCollectionBean implements Serializable {

  private static final long serialVersionUID = 8450159992169884278L;

  private List<PagoDiferidoBean> listaPagoDiferidoBMVTarjetas;
  private List<PagoDiferidoBean> listaPagoDiferidoBXITarjetas;
  private List<PagoDiferidoBean> listaPagoDiferidoIVRTarjetas;

  public List<PagoDiferidoBean> getListaPagoDiferidoBMVTarjetas() {
    return listaPagoDiferidoBMVTarjetas;
  }

  public void setListaPagoDiferidoBMVTarjetas(List<PagoDiferidoBean> listaPagoDiferidoBMVTarjetas) {
    this.listaPagoDiferidoBMVTarjetas = listaPagoDiferidoBMVTarjetas;
  }

  public List<PagoDiferidoBean> getListaPagoDiferidoBXITarjetas() {
    return listaPagoDiferidoBXITarjetas;
  }

  public void setListaPagoDiferidoBXITarjetas(List<PagoDiferidoBean> listaPagoDiferidoBXITarjetas) {
    this.listaPagoDiferidoBXITarjetas = listaPagoDiferidoBXITarjetas;
  }

  public List<PagoDiferidoBean> getListaPagoDiferidoIVRTarjetas() {
    return listaPagoDiferidoIVRTarjetas;
  }

  public void setListaPagoDiferidoIVRTarjetas(List<PagoDiferidoBean> listaPagoDiferidoIVRTarjetas) {
    this.listaPagoDiferidoIVRTarjetas = listaPagoDiferidoIVRTarjetas;
  }

  public void cargarListas(List<PagoDiferidoBean> listaPagoDiferido) {

    listaPagoDiferidoBMVTarjetas = new ArrayList<>();
    listaPagoDiferidoBXITarjetas = new ArrayList<>();
    listaPagoDiferidoIVRTarjetas = new ArrayList<>();

    for (PagoDiferidoBean obj : listaPagoDiferido) {

      if (obj.getCanal().equalsIgnoreCase("BMOVIL")) {
        listaPagoDiferidoBMVTarjetas.add(obj);
      } else if (obj.getCanal().equalsIgnoreCase("BNET")) {
        listaPagoDiferidoBXITarjetas.add(obj);
      } else if (obj.getCanal().equalsIgnoreCase("IVR")) {
        listaPagoDiferidoIVRTarjetas.add(obj);
      }
    }
  }
}
