package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.CancelacionProductoDetalleBean; // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.common.bean.CancelacionProductoBean;       // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.util.GifoleUtil; // Asumo que existe, ajusta según tu proyecto
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Cancelación Tarjetas")
@Route(value = "reporte/cancelaciones/tarjetas", layout = MainLayout.class)
public class ConsultaCancelacionTarjetaView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<CancelacionProductoDetalleBean> allData = new ArrayList<>();
    private DataTable<CancelacionProductoDetalleBean> dataTable;

    public ConsultaCancelacionTarjetaView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Cancelación Tarjetas");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<CancelacionProductoDetalleBean>builder()
                .id("tabla-cancelacion-tarjetas") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaCancelacionTarjetaUI)
                // Seleccionadas según las columnas del DTO proporcionado y el análisis de la UI antigua
                .column("codigoCentral", "Código Central",
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getCodigoCentral() : ""), "100px")
                .column("nombresApellidos", "Nombre Cliente", // Equivalente a "cliente" del DTO
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getNombreCompletoCliente() : ""), "300px")
                .column("tipoDocumento", "Tipo Documento", // Parte de "Documento Identidad"
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getTipoDocumento() : ""), "110px")
                .column("numeroDocumento", "Número de Documento", // Parte de "Documento Identidad"
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getNroDocumento() : ""), "145px")
                .column("fechaHoraRegistro", "Fecha Solicitud", // Equivalente a "fechaSolicitud" del DTO (usando creacion del detalle)
                    bean -> bean.getCreacion() != null ? FORMATO_FECHA.format(bean.getCreacion()) : "", "170px")
                // Las columnas "Motivo" y "Estado" requieren mapeo más específico.
                // La UI antigua tiene varias columnas relacionadas con el estado y motivos.
                // Usaré las más probables basadas en el DTO y la UI.
                .column("motivoRechazo", "Motivo", // Equivalente a "motivo" del DTO (puede ser motivoRechazo u otro campo)
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        // Puede ser necesario combinar motivoRechazo y otroMotivoRechazo
                        bean.getCancelacionProductoBean().getMotivoRechazo() : ""), "300px")
                .column("estado", "Estado", // Equivalente a "estado" del DTO
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getEstado() : ""), "110px")
                .column("subProducto", "Producto", // Equivalente a "producto" del DTO
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getSubProducto() : ""), "200px")
                .column("nroTarjetaTitular", "Tarjeta", // Equivalente a "tarjeta" del DTO
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getNroTarjetaTitular() : ""), "170px")
                .column("fechaInicioVigencia", "Fecha Emisión", // Asumiendo que fechaInicioVigencia se usa para la emisión
                    bean -> bean.getCancelacionProductoBean() != null && bean.getCancelacionProductoBean().getFechaRegistro1() != null ?
                            FORMATO_FECHA.format(bean.getCancelacionProductoBean().getFechaRegistro1()) : "", "170px")
                // Puedes agregar más columnas aquí si las necesitas, siguiendo el mismo patrón
                // Ejemplos de otras columnas disponibles en el bean:
                // .column("nroContrato", "Número de contrato", ...)
                // .column("correo", "Correo de Contacto", ...)
                // .column("celularCliente", "Celular", ...)
                // .column("otroMotivoRechazo", "Detalle Otros", ...)
                // .column("fechaHoraModificacion", "Fecha Modificacion", ...)
                // .column("registroModificacion", "Registro Externo", ...) // Del detalle bean
                // .column("canal", "Canal", ...)
                // Datos (inicialmente vacío)
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }
    
}