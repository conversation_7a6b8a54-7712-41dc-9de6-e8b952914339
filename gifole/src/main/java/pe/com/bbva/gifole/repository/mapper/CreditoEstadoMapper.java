package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.TCEstado;
import pe.com.bbva.gifole.domain.TCMotivo;
import pe.com.bbva.gifole.domain.TCToc;
import pe.com.bbva.gifole.domain.TCTocDetEstado;

@Component
public class CreditoEstadoMapper implements RowMapper<TCTocDetEstado> {

  @Override
  public TCTocDetEstado mapRow(ResultSet rs, int i) throws SQLException {
    TCToc tcToc = new TCToc();
    TCEstado tcEstado = new TCEstado();
    TCTocDetEstado credito = new TCTocDetEstado();
    credito.setTcToc(new TCToc());
    credito.getTcToc().setEstadoActual(new TCEstado());
    credito.setTcMotivo(new TCMotivo());
    credito.getTcMotivo().setNombre(rs.getString("MOTIVO"));
    credito.setObservacion(rs.getString("OBSERVACION"));
    credito.setCreacion(rs.getTimestamp("CREACION"));
    credito.setCreador(rs.getString("CREADOR"));
    tcToc.setId(rs.getLong("TC_TOC"));
    tcToc.setNombres(rs.getString("TITULAR"));
    tcEstado.setNombre(rs.getString("ESTADO"));
    tcToc.setEstadoActual(tcEstado);
    credito.setTcToc(tcToc);
    return credito;
  }
}
