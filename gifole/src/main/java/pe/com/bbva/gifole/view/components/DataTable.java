package pe.com.bbva.gifole.view.components;

import com.vaadin.flow.component.Component;
import com.vaadin.flow.component.Html;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.html.*;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * DataTable optimizado usando interfaces funcionales en lugar de reflection Mucho más eficiente y
 * type-safe
 *
 * @param <T> Tipo de objeto que contendrá la tabla
 */
@CssImport("./themes/bbva/components/datatable.css")
public class DataTable<T> extends VerticalLayout {

  private final Map<String, ColumnDefinition<T>> columns;
  private final transient List<T> allItems;
  private final int pageSize;

  // Componentes de la tabla
  private final Div tableContainer;
  private final Button firstPageButton;
  private final Button previousButton;
  private final Button nextButton;
  private final Button lastPageButton;
  private final Span pageInfo;

  // Estado de paginación
  private int currentPage = 0;
  private int totalPages = 0;
  private transient List<T> currentPageItems;

  /**
   * Constructor del DataTable optimizado
   *
   * @param id ID único para el componente
   * @param columns Mapa de definiciones de columna
   * @param items Lista de items a mostrar
   * @param pageSize Número de items por página
   */
  public DataTable(
      String id, Map<String, ColumnDefinition<T>> columns, List<T> items, int pageSize) {
    this.columns = columns;
    this.allItems = new ArrayList<>(items);
    this.pageSize = pageSize;
    this.currentPageItems = new ArrayList<>();

    // Configurar el contenedor principal
    setId(id);
    setWidthFull();
    setHeight("auto");
    setSpacing(false);
    setPadding(false);
    setMargin(false);
    addClassName("bbva-custom-datatable");

    // Crear componentes
    this.tableContainer = new Div();
    this.firstPageButton = new Button("«");
    this.previousButton = new Button("‹");
    this.nextButton = new Button("›");
    this.lastPageButton = new Button("»");
    this.pageInfo = new Span();

    // Inicializar componentes
    initializeComponents();
    updatePagination();

    // Agregar componentes al layout
    add(tableContainer, createPaginationLayout());
  }

  /** Inicializa los componentes de la tabla y paginación */
  private void initializeComponents() {
    // Configurar contenedor de tabla
    tableContainer.addClassName("table-container");
    tableContainer.setWidthFull();

    // Configurar botones de paginación
    firstPageButton.addClassName("pagination-button");
    firstPageButton.addClickListener(e -> firstPage());
    firstPageButton.getElement().setAttribute("title", "Primera página");

    previousButton.addClassName("pagination-button");
    previousButton.addClickListener(e -> previousPage());
    previousButton.getElement().setAttribute("title", "Página anterior");

    nextButton.addClassName("pagination-button");
    nextButton.addClickListener(e -> nextPage());
    nextButton.getElement().setAttribute("title", "Página siguiente");

    lastPageButton.addClassName("pagination-button");
    lastPageButton.addClickListener(e -> lastPage());
    lastPageButton.getElement().setAttribute("title", "Última página");

    // Configurar información de página
    pageInfo.addClassName("page-info");
  }

  /** Crea la tabla HTML con los datos actuales */
  private void createTable() {
    tableContainer.removeAll();

    if (currentPageItems.isEmpty()) {
      Div emptyMessage = new Div();
      emptyMessage.addClassName("empty-message");
      emptyMessage.setText("No hay datos disponibles");
      tableContainer.add(emptyMessage);
      return;
    }

    // Crear tabla HTML usando Html component
    StringBuilder tableHtml = new StringBuilder();
    tableHtml.append("<table class=\"data-table\">");

    // Crear header
    tableHtml.append("<thead><tr>");
    for (ColumnDefinition<T> column : columns.values()) {
      tableHtml.append("<th");
      if (column.hasWidth()) {
        tableHtml.append(" style=\"width: ").append(escapeHtml(column.getWidth())).append("\"");
      }
      tableHtml.append(">").append(escapeHtml(column.getTitle())).append("</th>");
    }
    tableHtml.append("</tr></thead>");

    // Crear body
    tableHtml.append("<tbody>");
    for (T item : currentPageItems) {
      tableHtml.append("<tr>");
      for (ColumnDefinition<T> column : columns.values()) {
        String value = column.getValue(item);
        tableHtml.append("<td>").append(escapeHtml(value)).append("</td>");
      }
      tableHtml.append("</tr>");
    }
    tableHtml.append("</tbody>");
    tableHtml.append("</table>");

    // Crear el componente Html con la tabla
    Html tableComponent = new Html(tableHtml.toString());
    tableContainer.add(tableComponent);
  }

  /** Escapa caracteres HTML para prevenir XSS */
  private String escapeHtml(String text) {
    if (text == null) {
      return "";
    }
    return text.replace("&", "&amp;")
        .replace("<", "&lt;")
        .replace(">", "&gt;")
        .replace("\"", "&quot;")
        .replace("'", "&#x27;");
  }

  /** Crea el layout de paginación */
  private Component createPaginationLayout() {
    HorizontalLayout paginationLayout = new HorizontalLayout();
    paginationLayout.addClassName("pagination-layout");
    paginationLayout.setWidthFull();
    paginationLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.CENTER);
    paginationLayout.setAlignItems(FlexComponent.Alignment.CENTER);
    paginationLayout.setPadding(true);
    paginationLayout.setSpacing(true);

    // Botones de navegación izquierda
    HorizontalLayout leftButtons = new HorizontalLayout();
    leftButtons.add(firstPageButton, previousButton);
    leftButtons.setAlignItems(FlexComponent.Alignment.CENTER);
    leftButtons.setSpacing(true);
    leftButtons.addClassName("pagination-left-buttons");

    // Información de página en el centro
    HorizontalLayout centerSection = new HorizontalLayout();
    centerSection.add(pageInfo);
    centerSection.setAlignItems(FlexComponent.Alignment.CENTER);
    centerSection.addClassName("pagination-center-info");

    // Botones de navegación derecha
    HorizontalLayout rightButtons = new HorizontalLayout();
    rightButtons.add(nextButton, lastPageButton);
    rightButtons.setAlignItems(FlexComponent.Alignment.CENTER);
    rightButtons.setSpacing(true);
    rightButtons.addClassName("pagination-right-buttons");

    paginationLayout.add(leftButtons, centerSection, rightButtons);
    return paginationLayout;
  }

  /** Navega a la primera página */
  private void firstPage() {
    if (currentPage > 0) {
      currentPage = 0;
      updatePagination();
    }
  }

  /** Navega a la página anterior */
  private void previousPage() {
    if (currentPage > 0) {
      currentPage--;
      updatePagination();
    }
  }

  /** Navega a la página siguiente */
  private void nextPage() {
    if (currentPage < totalPages - 1) {
      currentPage++;
      updatePagination();
    }
  }

  /** Navega a la última página */
  private void lastPage() {
    if (currentPage < totalPages - 1) {
      currentPage = totalPages - 1;
      updatePagination();
    }
  }

  /** Actualiza la paginación y los datos mostrados */
  private void updatePagination() {
    // Calcular páginas totales
    totalPages = (int) Math.ceil((double) allItems.size() / pageSize);

    // Calcular índices de inicio y fin
    int startIndex = currentPage * pageSize;
    int endIndex = Math.min(startIndex + pageSize, allItems.size());

    // Actualizar items de la página actual
    currentPageItems.clear();
    if (startIndex < allItems.size()) {
      currentPageItems.addAll(allItems.subList(startIndex, endIndex));
    }

    // Crear la tabla con los nuevos datos
    createTable();

    // Actualizar controles de paginación
    firstPageButton.setEnabled(currentPage > 0);
    previousButton.setEnabled(currentPage > 0);
    nextButton.setEnabled(currentPage < totalPages - 1);
    lastPageButton.setEnabled(currentPage < totalPages - 1);

    // Actualizar información de página
    if (totalPages > 0 && !allItems.isEmpty()) {
      pageInfo.setText(String.format("Página %d de %d", currentPage + 1, totalPages));
    } else {
      pageInfo.setText("Sin páginas");
    }
  }

  // Métodos públicos para interactuar con el componente

  /** Actualiza los datos de la tabla */
  public void setItems(List<T> newItems) {
    allItems.clear();
    allItems.addAll(newItems);
    currentPage = 0;
    updatePagination();
  }

  /** Obtiene la página actual */
  public int getCurrentPage() {
    return currentPage;
  }

  /** Obtiene el número total de páginas */
  public int getTotalPages() {
    return totalPages;
  }

  /** Navega a una página específica */
  public void goToPage(int page) {
    if (page >= 0 && page < totalPages) {
      currentPage = page;
      updatePagination();
    }
  }

  /** Refresca los datos de la tabla */
  public void refresh() {
    updatePagination();
  }

  /** Obtiene todos los items */
  public List<T> getAllItems() {
    return new ArrayList<>(allItems);
  }

  /** Obtiene los items de la página actual */
  public List<T> getCurrentPageItems() {
    return new ArrayList<>(currentPageItems);
  }

  /** Definición de columna optimizada usando interfaces funcionales */
  public static class ColumnDefinition<T> {
    private final String title;
    private final Function<T, String> valueExtractor;
    private final String width;

    public ColumnDefinition(String title, Function<T, String> valueExtractor) {
      this(title, valueExtractor, null);
    }

    public ColumnDefinition(String title, Function<T, String> valueExtractor, String width) {
      this.title = title;
      this.valueExtractor = valueExtractor;
      this.width = width;
    }

    public String getTitle() {
      return title;
    }

    public String getValue(T item) {
      return valueExtractor.apply(item);
    }

    public String getWidth() {
      return width;
    }

    public boolean hasWidth() {
      return width != null && !width.trim().isEmpty();
    }
  }

  /** Builder para crear DataTable de forma fluida */
  public static class Builder<T> {
    private String id;
    private final Map<String, ColumnDefinition<T>> columns = new LinkedHashMap<>();
    private List<T> items = new ArrayList<>();
    private int pageSize = 10;

    public Builder<T> id(String id) {
      this.id = id;
      return this;
    }

    public Builder<T> column(String key, String title, Function<T, String> valueExtractor) {
      columns.put(key, new ColumnDefinition<>(title, valueExtractor));
      return this;
    }

    public Builder<T> column(
        String key, String title, Function<T, String> valueExtractor, String width) {
      columns.put(key, new ColumnDefinition<>(title, valueExtractor, width));
      return this;
    }

    public Builder<T> items(List<T> items) {
      this.items = items;
      return this;
    }

    public Builder<T> pageSize(int pageSize) {
      this.pageSize = pageSize;
      return this;
    }

    public DataTable<T> build() {
      return new DataTable<>(id, columns, items, pageSize);
    }
  }

  /** Crea un nuevo builder */
  public static <T> Builder<T> builder() {
    return new Builder<>();
  }
}
