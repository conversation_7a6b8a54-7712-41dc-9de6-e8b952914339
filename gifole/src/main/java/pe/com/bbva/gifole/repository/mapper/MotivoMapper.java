package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.TCMotivo;

@Component
public class MotivoMapper implements RowMapper<TCMotivo> {

  @Override
  public TCMotivo mapRow(ResultSet rs, int i) throws SQLException {
    TCMotivo motivo = new TCMotivo();
    motivo.setId(rs.getLong("ID"));
    motivo.setNombre(rs.getString("NOMBRE"));
    motivo.setTipo(rs.getString("TIPO"));
    motivo.setOrden(rs.getInt("ORDEN"));
    return motivo;
  }
}
