package pe.com.bbva.gifole.repository.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pe.com.bbva.gifole.domain.AfiliacionEECCFFMM;
import pe.com.bbva.gifole.repository.AfiliacionEECCFFMMRepository;

@Repository
public class AfiliacionEECCFFMMRepositoryImpl implements AfiliacionEECCFFMMRepository {

  private static final Logger logger = LogManager.getLogger(AfiliacionEECCFFMMRepositoryImpl.class);

  @PersistenceContext private EntityManager entityManager;

  @Autowired private JdbcTemplate jdbcTemplate;

  @Override
  public void actualizarAfiliacionEECCFFMM() {
    String sql = "update GIFOLE.AFILIACION_EECC_FFMM set PROCESADO='1' where PROCESADO='0'";
    jdbcTemplate.update(sql);
  }

  @Override
  @Transactional(readOnly = false)
  public void crear(AfiliacionEECCFFMM afiliacionEECCFFMM) {
    entityManager.persist(afiliacionEECCFFMM);
  }
}
