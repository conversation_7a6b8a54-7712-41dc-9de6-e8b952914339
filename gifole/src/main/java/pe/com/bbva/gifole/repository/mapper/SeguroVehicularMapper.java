package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.SeguroVehicular;

@Component
public class SeguroVehicularMapper implements RowMapper<SeguroVehicular> {

  @Override
  public SeguroVehicular mapRow(ResultSet rs, int i) throws SQLException {
    SeguroVehicular seguroVehicular = new SeguroVehicular();
    seguroVehicular.setId(rs.getLong("ID"));
    seguroVehicular.setNombre(rs.getString("NOMBRE"));
    seguroVehicular.setApellido(rs.getString("APELLIDO"));
    seguroVehicular.setTipoDocumento(rs.getString("TIPO_DOCUMENTO"));
    seguroVehicular.setDocumento(rs.getString("DOCUMENTO"));
    seguroVehicular.setDepartamento(rs.getString("DEPARTAMENTO"));
    seguroVehicular.setCorreo(rs.getString("CORREO"));
    seguroVehicular.setTelefono(rs.getString("TELEFONO"));
    seguroVehicular.setHorario(rs.getString("HORARIO"));
    seguroVehicular.setEstadoPlaca(rs.getString("ESTADO_PLACA"));
    seguroVehicular.setPlaca(rs.getString("PLACA"));
    seguroVehicular.setAutorizacion(rs.getString("AUTORIZACION"));
    seguroVehicular.setFechaRegistro(rs.getTimestamp("FECHA_REGISTRO"));
    seguroVehicular.setProcesado(rs.getString("PROCESADO"));
    seguroVehicular.setAnhoFabricacion(rs.getString("ANHO_FABRICACION"));
    return seguroVehicular;
  }
}
