package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class SeguroVehicularRoyalBean implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;
  private String nombre;
  private String apellido;
  private String tipoDocumento;
  private String documento;
  private String departamento;
  private String correo;
  private String telefono;
  private String placa;
  private String fechaRegistro;
  private String anhoFabricacion;
  private String objetoCotizacion;
  private String informacionBanco;
  private String codigoOficina;
  private String codigoUsuario;
  private String horarioContacto;
  private Date fechaRegistroDesde;
  private Date fechaRegistroHasta;
  private String canal;
  private String indicadorSiEsCliente;
  private String marca;
  private String modelo;
  private String valorComercial;
  private String planCuotasMoneda;
  private String planCuotasMonto;
  private String primaMonto;
  private String primaMontoMoneda;
  private String primaFrecuencia;
  private String step;
  private String planNombre;
  private String indicadorConversionMotorVehiculo;
  private String urlOrigen;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public String getApellido() {
    return apellido;
  }

  public void setApellido(String apellido) {
    this.apellido = apellido;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public String getDocumento() {
    return documento;
  }

  public void setDocumento(String documento) {
    this.documento = documento;
  }

  public String getDepartamento() {
    return departamento;
  }

  public void setDepartamento(String departamento) {
    this.departamento = departamento;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public String getTelefono() {
    return telefono;
  }

  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  public String getPlaca() {
    return placa;
  }

  public void setPlaca(String placa) {
    this.placa = placa;
  }

  public String getFechaRegistro() {
    return fechaRegistro;
  }

  public void setFechaRegistro(String fechaRegistro) {
    this.fechaRegistro = fechaRegistro;
  }

  public String getAnhoFabricacion() {
    return anhoFabricacion;
  }

  public void setAnhoFabricacion(String anhoFabricacion) {
    this.anhoFabricacion = anhoFabricacion;
  }

  public String getObjetoCotizacion() {
    return objetoCotizacion;
  }

  public void setObjetoCotizacion(String objetoCotizacion) {
    this.objetoCotizacion = objetoCotizacion;
  }

  public String getInformacionBanco() {
    return informacionBanco;
  }

  public void setInformacionBanco(String informacionBanco) {
    this.informacionBanco = informacionBanco;
  }

  public String getCodigoOficina() {
    return codigoOficina;
  }

  public void setCodigoOficina(String codigoOficina) {
    this.codigoOficina = codigoOficina;
  }

  public String getCodigoUsuario() {
    return codigoUsuario;
  }

  public void setCodigoUsuario(String codigoUsuario) {
    this.codigoUsuario = codigoUsuario;
  }

  public String getHorarioContacto() {
    return horarioContacto;
  }

  public void setHorarioContacto(String horarioContacto) {
    this.horarioContacto = horarioContacto;
  }

  public Date getFechaRegistroDesde() {
    return fechaRegistroDesde;
  }

  public void setFechaRegistroDesde(Date fechaRegistroDesde) {
    this.fechaRegistroDesde = fechaRegistroDesde;
  }

  public Date getFechaRegistroHasta() {
    return fechaRegistroHasta;
  }

  public void setFechaRegistroHasta(Date fechaRegistroHasta) {
    this.fechaRegistroHasta = fechaRegistroHasta;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public String getIndicadorSiEsCliente() {
    return indicadorSiEsCliente;
  }

  public void setIndicadorSiEsCliente(String indicadorSiEsCliente) {
    this.indicadorSiEsCliente = indicadorSiEsCliente;
  }

  public String getMarca() {
    return marca;
  }

  public void setMarca(String marca) {
    this.marca = marca;
  }

  public String getModelo() {
    return modelo;
  }

  public void setModelo(String modelo) {
    this.modelo = modelo;
  }

  public String getValorComercial() {
    return valorComercial;
  }

  public void setValorComercial(String valorComercial) {
    this.valorComercial = valorComercial;
  }

  public String getPlanCuotasMoneda() {
    return planCuotasMoneda;
  }

  public void setPlanCuotasMoneda(String planCuotasMoneda) {
    this.planCuotasMoneda = planCuotasMoneda;
  }

  public String getPlanCuotasMonto() {
    return planCuotasMonto;
  }

  public void setPlanCuotasMonto(String planCuotasMonto) {
    this.planCuotasMonto = planCuotasMonto;
  }

  public String getPrimaMonto() {
    return primaMonto;
  }

  public void setPrimaMonto(String primaMonto) {
    this.primaMonto = primaMonto;
  }

  public String getPrimaMontoMoneda() {
    return primaMontoMoneda;
  }

  public void setPrimaMontoMoneda(String primaMontoMoneda) {
    this.primaMontoMoneda = primaMontoMoneda;
  }

  public String getPrimaFrecuencia() {
    return primaFrecuencia;
  }

  public void setPrimaFrecuencia(String primaFrecuencia) {
    this.primaFrecuencia = primaFrecuencia;
  }

  public String getStep() {
    return step;
  }

  public void setStep(String step) {
    this.step = step;
  }

  public String getPlanNombre() {
    return planNombre;
  }

  public void setPlanNombre(String planNombre) {
    this.planNombre = planNombre;
  }

  public String getIndicadorConversionMotorVehiculo() {
    return indicadorConversionMotorVehiculo;
  }

  public void setIndicadorConversionMotorVehiculo(String indicadorConversionMotorVehiculo) {
    this.indicadorConversionMotorVehiculo = indicadorConversionMotorVehiculo;
  }

  public String getUrlOrigen() {
    return urlOrigen;
  }

  public void setUrlOrigen(String urlOrigen) {
    this.urlOrigen = urlOrigen;
  }
}
