package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class CotizacionSeguroRoyalBean implements Serializable {
  private Long id;
  private String fechaRegistro;
  private String nombreProducto;
  private String nombre;
  private String apellido;
  private String nroDocumento;
  private String tipoDocumento;
  private String correo;
  private String telefono;
  private String esCliente;
  private String moneda;
  private String valorComercial;
  private String monedaCuotas;
  private String montoCuotas;
  private String monedaPrima;
  private String montoPrima;
  private String nroContizacion;
  private String informacionBanco;
  private String codigoOficina;
  private String codigoUsuario;
  private String canal;
  private Date fechaRegistroDesde;
  private Date fechaRegistroHasta;
  private String idProducto;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getFechaRegistro() {
    return fechaRegistro;
  }

  public void setFechaRegistro(String fechaRegistro) {
    this.fechaRegistro = fechaRegistro;
  }

  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public String getApellido() {
    return apellido;
  }

  public void setApellido(String apellido) {
    this.apellido = apellido;
  }

  public String getNroDocumento() {
    return nroDocumento;
  }

  public void setNroDocumento(String nroDocumento) {
    this.nroDocumento = nroDocumento;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public String getTelefono() {
    return telefono;
  }

  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  public String getEsCliente() {
    return esCliente;
  }

  public void setEsCliente(String esCliente) {
    this.esCliente = esCliente;
  }

  public String getMoneda() {
    return moneda;
  }

  public void setMoneda(String moneda) {
    this.moneda = moneda;
  }

  public String getValorComercial() {
    return valorComercial;
  }

  public void setValorComercial(String valorComercial) {
    this.valorComercial = valorComercial;
  }

  public String getMonedaCuotas() {
    return monedaCuotas;
  }

  public void setMonedaCuotas(String monedaCuotas) {
    this.monedaCuotas = monedaCuotas;
  }

  public String getMontoCuotas() {
    return montoCuotas;
  }

  public void setMontoCuotas(String montoCuotas) {
    this.montoCuotas = montoCuotas;
  }

  public String getMonedaPrima() {
    return monedaPrima;
  }

  public void setMonedaPrima(String monedaPrima) {
    this.monedaPrima = monedaPrima;
  }

  public String getMontoPrima() {
    return montoPrima;
  }

  public void setMontoPrima(String montoPrima) {
    this.montoPrima = montoPrima;
  }

  public String getNroContizacion() {
    return nroContizacion;
  }

  public void setNroContizacion(String nroContizacion) {
    this.nroContizacion = nroContizacion;
  }

  public String getInformacionBanco() {
    return informacionBanco;
  }

  public void setInformacionBanco(String informacionBanco) {
    this.informacionBanco = informacionBanco;
  }

  public String getCodigoOficina() {
    return codigoOficina;
  }

  public void setCodigoOficina(String codigoOficina) {
    this.codigoOficina = codigoOficina;
  }

  public String getCodigoUsuario() {
    return codigoUsuario;
  }

  public void setCodigoUsuario(String codigoUsuario) {
    this.codigoUsuario = codigoUsuario;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public Date getFechaRegistroDesde() {
    return fechaRegistroDesde;
  }

  public void setFechaRegistroDesde(Date fechaRegistroDesde) {
    this.fechaRegistroDesde = fechaRegistroDesde;
  }

  public Date getFechaRegistroHasta() {
    return fechaRegistroHasta;
  }

  public void setFechaRegistroHasta(Date fechaRegistroHasta) {
    this.fechaRegistroHasta = fechaRegistroHasta;
  }

  public String getIdProducto() {
    return idProducto;
  }

  public void setIdProducto(String idProducto) {
    this.idProducto = idProducto;
  }

  public String getNombreProducto() {
    return nombreProducto;
  }

  public void setNombreProducto(String nombreProducto) {
    this.nombreProducto = nombreProducto;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }
}
