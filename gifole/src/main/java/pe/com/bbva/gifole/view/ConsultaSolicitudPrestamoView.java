package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.PrestamoAlToqueBean; // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.util.Constante; // Asumo que existe, ajusta según tu proyecto
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Solicitud Préstamo")
@Route(value = "reporte/solicitudes/prestamo", layout = MainLayout.class)
public class ConsultaSolicitudPrestamoView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA_HORA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<PrestamoAlToqueBean> allData = new ArrayList<>();
    private DataTable<PrestamoAlToqueBean> dataTable;

    public ConsultaSolicitudPrestamoView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Solicitud Préstamo");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, basado SOLAMENTE en el fragmento proporcionado
        dataTable =
            DataTable.<PrestamoAlToqueBean>builder()
                .id("tabla-solicitud-prestamo") // ID único para la tabla
                // Mapeo de columnas basado EXCLUSIVAMENTE en el código proporcionado
                .column("codigoCentral", "Código Central", // CAMPO_CODIGO_CENTRAL
                    bean -> StringUtils.trimToEmpty(bean.getCodigoCentral()), "120px")
                .column("tipoDocumento", "Tipo Documento", // CAMPO_TIPO_DOCUMENTO
                    bean -> StringUtils.trimToEmpty(bean.getTipoDocumento()), "120px")
                .column("numeroDocumento", "Número Documento", // CAMPO_NUMERO_DOCUMENTO
                    bean -> StringUtils.trimToEmpty(bean.getNumeroDocumento()), "120px")
                .column("nombreCompleto", "Nombre Completo", // CAMPO_NOMBRE_COMPLETO
                    bean -> StringUtils.trimToEmpty(bean.getNombreCompleto()), "250px")
                .column("correo", "Correo", // CAMPO_CORREO
                    bean -> StringUtils.trimToEmpty(bean.getCorreo()), "200px") // Asumo que getCorreo() devuelve String, ajusta si es necesario
                .column("telefono", "Teléfono", // CAMPO_TELEFONO
                    bean -> StringUtils.trimToEmpty(bean.getTelefono()), "120px")
                .column("numeroContrato", "Número Contrato", // CAMPO_NUMERO_CONTRATO
                    bean -> StringUtils.trimToEmpty(bean.getNumeroContrato()), "170px")
                .column("monto", "Monto", // CAMPO_MONTO
                    bean -> StringUtils.trimToEmpty(bean.getMonto()), "120px")
                .column("cuota", "Cuota", // CAMPO_CUOTA
                    bean -> StringUtils.trimToEmpty(bean.getCuota()), "100px")
                .column("plazo", "Plazo", // CAMPO_PLAZO
                    bean -> StringUtils.trimToEmpty(bean.getPlazo()), "80px") // Asumo String, ajusta si es numérico
                .column("tea", "TEA", // CAMPO_TEA
                    bean -> StringUtils.trimToEmpty(bean.getTea()), "80px")
                .column("tcea", "TCEA", // CAMPO_TCEA
                    bean -> StringUtils.trimToEmpty(bean.getTcea()), "80px")
                .column("fechaContratacion", "Fecha Contratación", // CAMPO_FECHA_CONTRATACION
                    bean -> bean.getFechaRegistro() != null ? FORMATO_FECHA_HORA.format(bean.getFechaRegistro()) : "", "170px")
                .column("canal", "Canal", // CAMPO_CANAL (con lógica de transformación)
                    bean -> {
                        String canal = bean.getCanal();
                        if (canal == null) {
                            return Constante.CANAL.NOMBRE_BANCA_POR_INTERNET; // Asegúrate de tener esta constante
                        } else if (Constante.CANAL.BANCA_POR_INTERNET_UX.equals(canal)) { // Asegúrate de tener esta constante
                            return Constante.CANAL.NOMBRE_BANCA_POR_INTERNET;
                        } else {
                            return StringUtils.trimToEmpty(canal);
                        }
                    }, "120px")
                .column("marcaPh", "Marca PH", // CAMPO_MARCA_PH
                    bean -> StringUtils.trimToEmpty(bean.getMarcaPh()), "100px")
                // Datos (inicialmente vacío)
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}