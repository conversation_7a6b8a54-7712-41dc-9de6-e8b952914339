package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.PrestamoVehicularBean;

@SuppressWarnings("rawtypes")
@Component
public class PrestamoVehicularMapper implements RowMapper {

  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    PrestamoVehicularBean param = new PrestamoVehicularBean();

    param.setId(rs.getLong("ID"));
    param.setTipoDocumento(rs.getString("TIPO_DOCUMENTO"));
    param.setNroDocumento(rs.getString("NUM_DOCUMENTO"));
    param.setNombres(rs.getString("NOMBRES"));
    param.setApellidoPaterno(rs.getString("APE_PATERNO"));
    param.setApellidoMaterno(rs.getString("APE_MATERNO"));
    param.setTelefono(rs.getString("TELEFONO"));
    param.setCorreo(rs.getString("CORREO"));
    param.setCanal(rs.getString("CANAL"));
    param.setFechaRegistro(rs.getDate("FECHA_REGISTRO"));

    return param;
  }
}
