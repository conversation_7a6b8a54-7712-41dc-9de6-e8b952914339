package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.SisAuditoria;

@Component
public class SisAuditoriaMapper implements RowMapper<SisAuditoria> {

  @Override
  public SisAuditoria mapRow(ResultSet rs, int i) throws SQLException {

    SisAuditoria sisAuditoria = new SisAuditoria();

    sisAuditoria.setId(rs.getLong("A_ID"));
    sisAuditoria.setTabla(rs.getString("A_TABLA"));
    sisAuditoria.setTipo(rs.getString("A_TIPO"));
    sisAuditoria.setData(rs.getString("A_DATA"));
    sisAuditoria.setUsuario(rs.getString("A_USUARIO"));
    sisAuditoria.setFecha(rs.getDate("A_FECHA"));
    sisAuditoria.setId_registro(rs.getString("A_ID_REGISTRO"));

    return sisAuditoria;
  }
}
