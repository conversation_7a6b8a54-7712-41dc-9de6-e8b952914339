package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.AfiliacionPlinBean;

@Component
public class PlinMapper implements RowMapper<AfiliacionPlinBean> {

  @Override
  public AfiliacionPlinBean mapRow(ResultSet rs, int i) throws SQLException {

    AfiliacionPlinBean afiliacionPlinBean = new AfiliacionPlinBean();
    afiliacionPlinBean.setId(rs.getLong("ID"));
    afiliacionPlinBean.setRuc(rs.getString("RUC"));
    afiliacionPlinBean.setRazonSocial(rs.getString("RAZON_SOCIAL"));
    afiliacionPlinBean.setNumeroCuenta(rs.getString("NUMERO_CUENTA"));
    afiliacionPlinBean.setTipoDocumento(rs.getString("TIPO_DOCUMENTO"));
    afiliacionPlinBean.setNumeroDocumento(rs.getString("DOCUMENTO"));
    afiliacionPlinBean.setApellidosRepresentante(rs.getString("APELLIDOS_REPRESENTANTE"));
    afiliacionPlinBean.setNombreRepresentante(rs.getString("NOMBRE_REPRESENTANTE"));
    afiliacionPlinBean.setCelular(rs.getString("CELULAR_AFILIAR"));
    afiliacionPlinBean.setCorreo(rs.getString("CORREO"));
    afiliacionPlinBean.setFechaRegistro(rs.getTimestamp("FECHA_REGISTRO"));
    afiliacionPlinBean.setFechaActualizacion(rs.getTimestamp("FECHA_ACTUALIZACION"));
    afiliacionPlinBean.setMarcaLPDP(rs.getString("MARCA_LPDP"));
    afiliacionPlinBean.setEstado(rs.getString("DES_ESTADO_SOLICITUD"));
    afiliacionPlinBean.setComentario(rs.getString("COMENTARIO"));

    return afiliacionPlinBean;
  }
}
