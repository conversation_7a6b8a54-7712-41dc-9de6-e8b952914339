package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.domain.ImagenesTcCu;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Imágenes")
@Route(value = "reporte/imagenes", layout = MainLayout.class)
public class ConsultaImagenesView extends VerticalLayout {

    // Formato de fecha (aunque para esta tabla no se usa, lo defino por si acaso)
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<ImagenesTcCu> allData = new ArrayList<>();
    private DataTable<ImagenesTcCu> dataTable;

    public ConsultaImagenesView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Imágenes");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<ImagenesTcCu>builder()
                .id("tabla-imagenes") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaImagenesUI)
                .column("bin", "Bin",
                    bean -> StringUtils.trimToEmpty(bean.getBin()), "68px")
                .column("imagenId", "ID", // Mapeo del nombre usado en la UI antigua
                    bean -> StringUtils.trimToEmpty(bean.getId()), "70px")
                .column("imagenName", "Nombre de Imagen", // Mapeo del nombre usado en la UI antigua
                    bean -> StringUtils.trimToEmpty(bean.getNombreImagen()), "1088px")
                // Datos (inicialmente vacío)
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}