package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_TARIFA")
public class VehTarifa implements Serializable {
  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_VEH_TARIFA")
  @TableGenerator(
      name = "SEQ_VEH_TARIFA",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.VehTarifa",
      allocationSize = 1)
  private Long id;

  @Column(name = "CATEGORIA", length = 4)
  private String categoria;

  // bidirectional many-to-one association to VehPlan
  @ManyToOne
  @JoinColumn(name = "PLAN")
  private VehPlan plan;

  @Column(name = "CODIGO_USO", length = 4)
  private String codigoUso;

  @Column(name = "ANHO_ANTIGUEDAD", length = 4)
  private String anhoAntiguedad;

  @Column(name = "CODIGO_UBICACION", length = 1)
  private String codigoUbicacion;

  @Column(name = "TASA_MES", length = 5, precision = 5)
  private BigDecimal tasaMes;

  @Column(name = "TASA_ANUAL", length = 5, precision = 5)
  private BigDecimal tasaAnual;
}
