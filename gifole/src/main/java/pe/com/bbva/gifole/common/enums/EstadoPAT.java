package pe.com.bbva.gifole.common.enums;

public enum EstadoPAT {
  REGIS<PERSON>AD<PERSON>("REGISTRAD<PERSON>", "REGIS<PERSON>AD<PERSON>"),
  CONTRATADO("CONTRATADO", "CONTRATADO"),
  PROCESADO("PROCESADO", "PROCESADO"),
  SOLICITUD_PERIODO_GRACIA("SPG", "REGIS<PERSON>ADO"),
  CON_PERIODO_GRACIA("CPG", "ACEPTADO"),
  SOLICITUD_EXTORNO("SPGEXT", "PENDIENTE DE EXTORNAR"),
  EXTORNADO_PERIODO_GRACIA("CPGEXT", "EXTORNADO");

  private String codigo;
  private String descripcion;

  private EstadoPAT(String codigo, String descripcion) {
    this.codigo = codigo;
    this.descripcion = descripcion;
  }

  public String getCodigo() {
    return codigo;
  }

  public String getDescripcion() {
    return descripcion;
  }

  public static EstadoPAT buscarPorCodigo(String codigo) {
    EstadoPAT[] values = values();
    for (int i = 0; i < values.length; i++) {
      if (values[i].getCodigo().equalsIgnoreCase(codigo)) {
        return values[i];
      }
    }
    return null;
  }
}
