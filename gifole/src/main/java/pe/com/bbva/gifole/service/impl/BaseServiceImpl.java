package pe.com.bbva.gifole.service.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import pe.com.bbva.gifole.model.BaseEntity;
import pe.com.bbva.gifole.service.BaseService;

public abstract class BaseServiceImpl<T extends BaseEntity> implements BaseService<T> {

  @PersistenceContext protected EntityManager entityManager;

  private final Class<T> entityClass;

  @SuppressWarnings("unchecked")
  public BaseServiceImpl() {
    this.entityClass =
        (Class<T>)
            ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
  }

  @Override
  @Transactional
  public T save(T entity) {
    if (entity.getId() == null) {
      entityManager.persist(entity);
      return entity;
    } else {
      return entityManager.merge(entity);
    }
  }

  @Override
  public Optional<T> findById(Long id) {
    T entity = entityManager.find(entityClass, id);
    return Optional.ofNullable(entity);
  }

  @Override
  public List<T> findAll() {
    String jpql = "SELECT e FROM " + entityClass.getSimpleName() + " e";
    TypedQuery<T> query = entityManager.createQuery(jpql, entityClass);
    return query.getResultList();
  }

  @Override
  public Page<T> findAll(Pageable pageable) {
    String jpql = "SELECT e FROM " + entityClass.getSimpleName() + " e";
    TypedQuery<T> query = entityManager.createQuery(jpql, entityClass);

    query.setFirstResult((int) pageable.getOffset());
    query.setMaxResults(pageable.getPageSize());

    List<T> content = query.getResultList();

    String countJpql = "SELECT COUNT(e) FROM " + entityClass.getSimpleName() + " e";
    TypedQuery<Long> countQuery = entityManager.createQuery(countJpql, Long.class);
    Long total = countQuery.getSingleResult();

    return new PageImpl<>(content, pageable, total);
  }

  @Override
  @Transactional
  public void deleteById(Long id) {
    T entity = entityManager.find(entityClass, id);
    if (entity != null) {
      entityManager.remove(entity);
    }
  }

  @Override
  public boolean existsById(Long id) {
    return findById(id).isPresent();
  }
}
