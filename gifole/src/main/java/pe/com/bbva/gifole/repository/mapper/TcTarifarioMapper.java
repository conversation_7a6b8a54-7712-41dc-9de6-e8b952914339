package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.Parametro;
import pe.com.bbva.gifole.domain.TcTarifario;

@Component
public class TcTarifarioMapper implements RowMapper<TcTarifario> {

  @Override
  public TcTarifario mapRow(ResultSet rs, int i) throws SQLException {

    TcTarifario tcTarifario = new TcTarifario();

    Parametro tcMarcaTarjeta = new Parametro();
    tcMarcaTarjeta.setId(rs.getLong("B_ID"));
    tcMarcaTarjeta.setCodigo(rs.getString("B_CODIGO"));
    tcMarcaTarjeta.setNombre(rs.getString("B_NOMBRE"));
    tcMarcaTarjeta.setTipo(rs.getString("B_TIPO"));

    Parametro tcProgramaLealtad = new Parametro();
    tcProgramaLealtad.setId(rs.getLong("C_ID"));
    tcProgramaLealtad.setCodigo(rs.getString("C_CODIGO"));
    tcProgramaLealtad.setNombre(rs.getString("C_NOMBRE"));
    tcProgramaLealtad.setTipo(rs.getString("C_TIPO"));

    Parametro estado = new Parametro();
    estado.setId(rs.getLong("D_ID"));
    estado.setCodigo(rs.getString("D_CODIGO"));
    estado.setNombre(rs.getString("D_NOMBRE"));
    estado.setTipo(rs.getString("D_TIPO"));

    Parametro orientacion = new Parametro();
    orientacion.setId(rs.getLong("E_ID"));
    orientacion.setCodigo(rs.getString("E_CODIGO"));
    orientacion.setNombre(rs.getString("E_NOMBRE"));
    orientacion.setTipo(rs.getString("E_TIPO"));

    Parametro flag_tarjeta_hincha = new Parametro();
    flag_tarjeta_hincha.setId(rs.getLong("F_ID"));
    flag_tarjeta_hincha.setCodigo(rs.getString("F_CODIGO"));
    flag_tarjeta_hincha.setNombre(rs.getString("F_NOMBRE"));
    flag_tarjeta_hincha.setTipo(rs.getString("F_TIPO"));

    Parametro orientacion_tarjeta_hincha = new Parametro();
    orientacion_tarjeta_hincha.setId(rs.getLong("G_ID"));
    orientacion_tarjeta_hincha.setCodigo(rs.getString("G_CODIGO"));
    orientacion_tarjeta_hincha.setNombre(rs.getString("G_NOMBRE"));
    orientacion_tarjeta_hincha.setTipo(rs.getString("G_TIPO"));

    tcTarifario.setId(rs.getLong("A_ID"));
    tcTarifario.setBin(rs.getString("A_BIN"));
    tcTarifario.setRango_tcea(rs.getString("A_RANGO_TCEA"));
    tcTarifario.setFuncion_tagueo(rs.getString("A_DESCRIPCION"));
    tcTarifario.setObservacion(rs.getString("A_OBSERVACION"));
    tcTarifario.setMembresia(rs.getString("A_MEMBRESIA"));
    tcTarifario.setEecc(rs.getString("A_EECC"));
    tcTarifario.setSeg_desgravamen(rs.getString("A_SEG_DESGRAVAMEN"));
    tcTarifario.setImagen(rs.getString("A_IMAGEN"));
    tcTarifario.setPrioridad(rs.getString("A_PRIORIDAD"));
    tcTarifario.setImagen_tarjeta_hincha(rs.getString("A_IMAGEN_TARJETA_HINCHA"));
    tcTarifario.setNombre_tarjeta_hincha(rs.getString("A_NOMBRE_TARJETA_HINCHA"));

    /* Inciio PBP-2229 Gifole TOC: parametrías */
    tcTarifario.setBono(rs.getString("A_BONO"));
    tcTarifario.setConsumoBono(rs.getString("A_CONSUMO_BONO"));
    tcTarifario.setDiasBono(rs.getString("A_DIAS_BONO"));
    tcTarifario.setConsumoExoneracionMembresia(rs.getString("A_CONSUMO_EX_MEMBRESIA"));
    /* Fin PBP-2229 Gifole TOC: parametrías */

    tcTarifario.setTcMarcaTarjeta(tcMarcaTarjeta);
    tcTarifario.setTcProgramaLealtad(tcProgramaLealtad);
    tcTarifario.setEstado(estado);
    tcTarifario.setOrientacion(orientacion);
    tcTarifario.setFlag_tarjeta_hincha(flag_tarjeta_hincha);
    tcTarifario.setOrientacion_tarjeta_hincha(orientacion_tarjeta_hincha);
    tcTarifario.setDescripcion_zona_publica(rs.getString("A_DESCRIPCION_ZP"));

    return tcTarifario;
  }
}
