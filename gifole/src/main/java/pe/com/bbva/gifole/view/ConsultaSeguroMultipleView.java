package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

import pe.com.bbva.gifole.domain.Seguro;
import pe.com.bbva.gifole.util.Utilitario; // Asumo que existe, ajusta según tu proyecto
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Seguro Múltiple")
@Route(value = "reporte/seguros/multiple", layout = MainLayout.class)
public class ConsultaSeguroMultipleView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final String FORMATO_FECHA = "dd/MM/yyyy"; // Ajusta el formato según necesites
    private static final SimpleDateFormat FORMATO_FECHA_HORA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<Seguro> allData = new ArrayList<>();
    private DataTable<Seguro> dataTable;

    public ConsultaSeguroMultipleView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Seguro Múltiple");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<Seguro>builder()
                .id("tabla-seguro-multiple") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaSeguroMultipleUI)
                .column("id", "Id",
                    bean -> StringUtils.trimToEmpty(String.valueOf(bean.getId())), "80px")
                .column("fechaRegistro", "Fecha", // Mostrando la fecha formateada
                    bean -> bean.getFechaRegistro() != null ? Utilitario.formatearFecha(bean.getFechaRegistro(), FORMATO_FECHA) : "", "120px")
                 .column("fechaHoraRegistro", "Fecha Hora", // Mostrando la fecha y hora completa (alternativa)
                    bean -> bean.getFechaRegistro() != null ? FORMATO_FECHA_HORA.format(bean.getFechaRegistro()) : "", "150px")
                .column("nombre", "Nombre",
                    bean -> StringUtils.trimToEmpty(bean.getNombre()), "150px")
                .column("apellido", "Apellido",
                    bean -> StringUtils.trimToEmpty(bean.getApellido()), "150px")
                .column("tipoDocumento", "Tipo Documento",
                    bean -> StringUtils.trimToEmpty(bean.getTipoDocumento()), "120px")
                .column("documento", "Documento",
                    bean -> StringUtils.trimToEmpty(bean.getDocumento()), "120px")
                .column("departamento", "Departamento",
                    bean -> StringUtils.trimToEmpty(bean.getDepartamento()), "150px")
                .column("correo", "Correo",
                    bean -> StringUtils.trimToEmpty(bean.getCorreo()), "200px")
                .column("telefono", "Teléfono",
                    bean -> StringUtils.trimToEmpty(bean.getTelefono()), "120px")
                .column("horario", "Horario",
                    bean -> StringUtils.trimToEmpty(bean.getHorario()), "100px")
                .column("autorizacion", "Autorización",
                    bean -> StringUtils.trimToEmpty(bean.getAutorizacion()), "120px")
                // Columnas dinámicas basadas en tipoSeguro (ejemplos)
                .column("VIDA", "Vida",
                    bean -> "VIDA".equalsIgnoreCase(bean.getTipoSeguro()) ? "SI" : "", "60px")
                .column("DESGRAVAMEN", "Desgravamen",
                    bean -> "DESGRAVAMEN".equalsIgnoreCase(bean.getTipoSeguro()) ? "SI" : "", "100px")
                .column("SOAT", "SOAT",
                    bean -> "SOAT".equalsIgnoreCase(bean.getTipoSeguro()) ? "SI" : "", "60px")
                .column("VEHICULAR", "Vehicular",
                    bean -> "VEHICULAR".equalsIgnoreCase(bean.getTipoSeguro()) ? "SI" : "", "80px")
                .column("HOGAR", "Hogar",
                    bean -> "HOGAR".equalsIgnoreCase(bean.getTipoSeguro()) ? "SI" : "", "60px")
                .column("ACCIDENTES", "Accidentes",
                    bean -> "ACCIDENTES".equalsIgnoreCase(bean.getTipoSeguro()) ? "SI" : "", "90px")
                .column("ONCOLOGICO", "Oncológico",
                    bean -> "ONCOLOGICO".equalsIgnoreCase(bean.getTipoSeguro()) ? "SI" : "", "90px")
                .column("EXEQUIAL", "Exequial",
                    bean -> "EXEQUIAL".equalsIgnoreCase(bean.getTipoSeguro()) ? "SI" : "", "80px")
                // Puedes agregar más columnas aquí si las necesitas, siguiendo el mismo patrón
                // Datos (inicialmente vacío)
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}