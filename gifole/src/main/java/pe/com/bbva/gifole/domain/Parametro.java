package pe.com.bbva.gifole.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

@Data
@Entity
@Table(name = "PARAMETRO")
public class Parametro implements Serializable {

  @Id
  @Column(unique = true, nullable = false, precision = 16)
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_PARAMETRO")
  @TableGenerator(
      name = "SEQ_PARAMETRO",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.Parametro",
      allocationSize = 1)
  private Long id;

  @Column(length = 40)
  private String codigo;

  @Column(length = 50)
  private String nombre;

  @Column(length = 200)
  private String descripcion;

  @Column(length = 100)
  private String valor;

  @Column(length = 20)
  private String tipo;

  @Column(length = 1)
  private String estado;

  @Column(unique = false, nullable = true, precision = 16)
  private Integer orden;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "CREADOR", nullable = false, insertable = true, updatable = false)
  private Usuario creador;

  @Column(name = "CREACION", nullable = false, insertable = true, updatable = false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date creacion;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "EDITOR", insertable = false, updatable = true)
  private Usuario editor;

  @Column(name = "EDICION", insertable = false, updatable = true)
  @Temporal(TemporalType.TIMESTAMP)
  private Date edicion;

  @JsonInclude(Include.NON_NULL)
  @Column(name = "FLAG_ADJUNTAR_ARCHIVO", length = 1)
  private String flagAdjuntarArchivo;

  public abstract static class CODIGO {

    public abstract static class FILE {
      public static final String FLAG_EMAIL_SV = "FLAG_EMAIL_SV";
      public static final String FLAG_EMAIL_PT = "FLAG_EMAIL_PT";
      public static final String FLAG_EMAIL_RH = "FLAG_EMAIL_RH";
      public static final String FLAG_EMAIL_PM = "FLAG_EMAIL_PM";
      public static final String FLAG_EMAIL_MSP = "FLAG_EMAIL_MSP";
      public static final String FLAG_EMAIL_MSE = "FLAG_EMAIL_MSE";
      public static final String FLAG_EMAIL_EECC_FFMM = "FLAG_EMAIL_EECC_FFMM";
      public static final String FLAG_EMAIL_PRE_HIP = "FLAG_EMAIL_PRE_HIP";
      public static final String FLAG_EMAIL_SUS_PRO_FFMM = "FLAG_EMAIL_SUS_PRO_FFMM";
      public static final String FLAG_EMAIL_SUS_PRO_BXI_FFMM = "FLAG_EMAIL_SUS_PRO_BXI_FFMM";
      public static final String FLAG_EMAIL_COT_VEH = "FLAG_EMAIL_COT_VEH";
      public static final String FLAG_EMAIL_TC_ADI_PROC = "FLAG_EMAIL_TC_ADI_PROC";
      public static final String FLAG_EMAIL_TC_ADI_RECH = "FLAG_EMAIL_TC_ADI_RECH";
      public static final String FLAG_EMAIL_TC_CRE_PROC = "FLAG_EMAIL_TC_CRE_PROC";
      public static final String FLAG_EMAIL_REMARKETING_TOC = "FLAG_EMAIL_REMARKETING_TOC";
      public static final String FLAG_EMAIL_REMARKETING_PAT = "FLAG_EMAIL_REMARKETING_PAT";
      public static final String FLAG_ARCHIVO_SV = "FLAG_ARCHIVO_SV";
      public static final String FLAG_ARCHIVO_PT = "FLAG_ARCHIVO_PT";
      public static final String FLAG_ARCHIVO_RH = "FLAG_ARCHIVO_RH";
      public static final String FLAG_ARCHIVO_PM = "FLAG_ARCHIVO_PM";
      public static final String FLAG_ARCHIVO_MSP = "FLAG_ARCHIVO_MSP";
      public static final String FLAG_ARCHIVO_MSE = "FLAG_ARCHIVO_MSE";
      public static final String FLAG_ARCHIVO_EECC_FFMM = "FLAG_ARCHIVO_EECC_FFMM";
      public static final String FLAG_ARCHIVO_PRE_HIP = "FLAG_ARCHIVO_PRE_HIP";
      public static final String FLAG_ARCHIVO_SUS_PRO_FFMM = "FLAG_ARCHIVO_SUS_PRO_FFMM";
      public static final String FLAG_ARCHIVO_SUS_PRO_BXI_FFMM = "FLAG_ARCHIVO_SUS_PRO_BXI_FFMM";
      public static final String FLAG_ARCHIVO_COT_VEH = "FLAG_ARCHIVO_COT_VEH";

      public static final String PATH_OUT_EXTRA_PT = "PATH_OUT_EXTRA_PT";
      public static final String PATH_OUT_EXTRA_EECC_FFMM = "PATH_OUT_EXTRA_EECC_FFMM";
      public static final String PATH_OUT_EXTRA_SUS_PRO_FFMM = "PATH_OUT_EXTRA_SUS_PRO_FFMM";
      public static final String PATH_OUT_EXTRA_SUS_PRO_BXI_FFMM =
          "PATH_OUT_EXTRA_SUS_PRO_BXI_FFMM";
      public static final String PATH_OUT_EXTRA_COT_VEH = "PATH_OUT_EXTRA_COT_VEH";

      // Rutas nuevas para file server en Jboss
      public static final String JB_PATH_OUT_INTRANET = "JB_PATH_OUT_INTRANET";
      public static final String JB_PATH_OUT_INTRANET_EECC_FFMM = "JB_PATH_OUT_INTRANET_EECC_FFMM";
      public static final String JB_PATH_OUT_INTRANET_SUS_PRO_FFMM =
          "JB_PATH_OUT_INTRANET_SUS_PRO_FFMM";
      public static final String JB_PATH_OUT_INTRANET_SUS_PRO_BXI_FFMM =
          "JB_PATH_OUT_INTRANET_SUS_PRO_BXI_FFMM";
      public static final String JB_PATH_OUT_INTRANET_COT_VEH = "JB_PATH_OUT_INTRANET_COT_VEH";
      public static final String JB_PATH_OUT_INTRANET_SEG_PT = "JB_PATH_OUT_INTRANET_SEG_PT";
      public static final String JB_PATH_OUT_INTRANET_TARJ_TOC = "JB_PATH_OUT_INTRANET_TARJ_TOC";
      public static final String JB_PATH_OUT_INTRANET_TARJ_CERO = "JB_PATH_OUT_INTRANET_TARJ_CERO";
      public static final String JB_PATH_OUT_INTRANET_TARJ_CONGELADA =
          "JB_PATH_OUT_INTRANET_TARJ_CONGELADA";
      public static final String JB_PATH_OUT_INTRANET_EXTRACCION_ADICIONAL =
          "JB_PATH_OUT_EXTRAC_TC_ADICIONAL";
      public static final String JB_PATH_OUT_INTRANET_EXTRACCION_ADICIONAL_DETALLE =
          "JB_PATH_OUT_EXTRAC_TC_ADICIONAL_DETALLE";
      public static final String JB_PATH_OUT_INTRANET_EXTRACCION_ADICIONAL_DETALLE_ESTADO =
          "JB_PATH_OUT_EXTRAC_TC_ADICIONAL_DET_ESTADO";
      public static final String JB_PATH_OUT_INTRANET_EXTRACCION_TOC = "JB_PATH_OUT_EXTRAC_TC_TOC";
      public static final String JB_PATH_OUT_INTRANET_EXTRACCION_TOC_DET_ESTADO =
          "JB_PATH_OUT_EXTRAC_TC_TOC_DET_ESTADO";
      public static final String JB_PATH_OUT_INTRANET_EXTRACCION_TC_ESTADO =
          "JB_PATH_OUT_EXTRAC_TC_ESTADO";
      public static final String JB_DIAS_MANTENER_ARCHIVOS = "JB_DIAS_MANTENER_ARCHIVOS";
      public static final String JB_RUTAS_DEPURAR = "JB_RUTAS_DEPURAR";
      public static final String JB_PATH_OUT_EXTRAC_TC_GARANTIZADA =
          "JB_PATH_OUT_EXTRAC_TC_GARANTIZADA";
      public static final String JB_PATH_OUT_EXTRAC_TC_UPGRADE = "JB_PATH_OUT_EXTRAC_TC_UPGRADE";

      // Rutas opt/apps/ en Jboss
      public static final String JB_PATH_LOG = "JB_PATH_LOG";
      public static final String JB_PATH_LOG_COMPRIMIDO = "JB_PATH_LOG_COMPRIMIDO";

      // reprogramacion de deudas
      public static final String JB_PATH_OUT_INTRANET_RPRO_DEUDAS =
          "JB_PATH_OUT_INTRANET_RPRO_DEUDAS";
      public static final String PATH_OUT_EXTRA_RPRO_DEUDAS = "PATH_OUT_EXTRA_RPRO_DEUDAS";
      public static final String FLAG_EMAIL_RPRO_DEUDAS = "FLAG_EMAIL_RPRO_DEUDAS";
      public static final String FLAG_ARCHIVO_RPRO_DEUDAS = "FLAG_ARCHIVO_RPRO_DEUDAS";
      public static final String FLAG_HABILITAR_JOB_IVR = "FLAG_HABILITAR_JOB_IVR";

      // pago diferido
      public static final String JB_PATH_OUT_INTRANET_PAGO_DIFERIDO =
          "JB_PATH_OUT_INTRANET_PAGO_DIFERIDO";
      public static final String PATH_OUT_EXTRA_PAGO_DIFERIDO = "PATH_OUT_EXTRA_PAGO_DIFERIDO";
      public static final String FLAG_ARCHIVO_PAGO_DIFERIDO = "FLAG_ARCHIVO_PAGO_DIFERIDO";

      // TOC Innominadas (Lima y Provincia)
      public static final String JB_PATH_OUT_INTRANET_TOC_INNOMINADAS =
          "JB_PATH_OUT_INTRANET_TOC_INNOMINADAS";
      public static final String FLAG_EMAIL_TOC_INNOMINADAS = "FLAG_EMAIL_TOC_INNOMINADAS";

      // cuentas afiliaciones vip
      public static final String JB_PATH_OUT_INTRANET_AFILIACIONES_VIP =
          "JB_PATH_OUT_INTRANET_AFILIACIONES_VIP";
      public static final String FLAG_EMAIL_AFILIACIONES_VIP = "FLAG_EMAIL_AFILIACIONES_VIP";

      // Cotizador vehicular glomo
      public static final String FLAG_ENVIO_FTP_COTIZADOR_GLOMO = "FLAG_ENVIO_FTP_COTIZADOR_GLOMO";
      public static final String FLAG_EMAIL_COTIZADOR_GLOMO = "FLAG_EMAIL_COTIZADOR_GLOMO";

      // Apertura Cuenta Datio
      public static final String JB_PATH_OUT_INTRANET_AC_DATIO = "JB_PATH_OUT_INTRANET_AC_DATIO";
      public static final String FLAG_EMAIL_AC_DATIO = "FLAG_EMAIL_AC_DATIO";
      public static final String FECHA_DESDE_AC_DATIO = "FECHA_DESDE_AC_DATIO";

      // Prestamo vehicular glomo
      public static final String FLAG_ENVIO_FTP_PRESTAMO_VEHICULAR_GLOMO =
          "FLAG_ENVIO_FTP_PRESTAMO_VEHICULAR_GLOMO";
      public static final String FLAG_EMAIL_PRESTAMO_VEHICULAR_GLOMO =
          "FLAG_EMAIL_PRESTAMO_VEHICULAR_GLOMO";
      public static final String PATH_OUT_SFTP_CONSUMER_FINANCE = "PATH_OUT_SFTP_CONSUMER_FINANCE";
      public static final String JB_PATH_OUT_INTRANET_PV_GLOMO = "JB_PATH_OUT_INTRANET_PV_GLOMO";
      public static final String FTP_IP_PRESTAMO_VEHICULAR_GLOMO =
          "FTP_IP_PRESTAMO_VEHICULAR_GLOMO";
      public static final String FTP_PUERTO_PRESTAMO_VEHICULAR_GLOMO =
          "FTP_PUERTO_PRESTAMO_VEHICULAR_GLOMO";
      public static final String FTP_USUARIO_PRESTAMO_VEHICULAR_GLOMO =
          "FTP_USUARIO_PRESTAMO_VEHICULAR_GLOMO";
      public static final String FTP_CLAVE_PRESTAMO_VEHICULAR_GLOMO =
          "FTP_CLAVE_PRESTAMO_VEHICULAR_GLOMO";

      // Cuentas AFP Covid 19
      public static final String JB_PATH_OUT_INTRANET_CUENTAS_AFP =
          "JB_PATH_OUT_INTRANET_CUENTAS_AFP";
      public static final String ARCHIVO_CUENTAS_AFP = "ARCHIVO_CUENTAS_AFP";
      public static final String EXTENSION_ARCHIVO_CUENTAS_AFP = "EXTENSION_ARCHIVO_CUENTAS_AFP";

      public static final String JB_PATH_OUT_INTRANET_CUENTAS_HOST =
          "JB_PATH_OUT_INTRANET_CUENTAS_AFP_HOST";
      public static final String ARCHIVO_CUENTAS_HOST = "ARCHIVO_CUENTAS_HOST";
      public static final String EXTENSION_ARCHIVO_CUENTAS_HOST = "EXTENSION_ARCHIVO_CUENTAS_HOST";

      public static final String LISTA_FECHAS_CUENTAS_AFP = "LISTA_FECHAS_CUENTAS_AFP";
      public static final String LISTA_FECHAS_CUENTAS_HOST = "LISTA_FECHAS_CUENTAS_HOST";
      public static final String SEPARADOR_CUENTAS_AFP = "SEPARADOR_CUENTAS_AFP";
      public static final String SEPARADOR_CUENTAS_HOST = "SEPARADOR_CUENTAS_HOST";
      public static final String CHECKSUM_CARRETERA_TOC = "CHECKSUM_CARRETERA_TOC";
      public static final String CHECKSUM_CARRETERA_TOC_TEMPORAL =
          "CHECKSUM_CARRETERA_TOC_TEMPORAL";
      public static final String CANCELACION_TARJETAS_DIAS = "CANCELACION_TARJETAS_DIAS";

      // Transmisión datos de archivos Gifole (JOB) hacia un servidor de Finanzas (XCOM)
      public static final String PATH_OUT_EXTRA_XCOMNTIP_FINANZAS =
          "PATH_OUT_EXTRA_XCOMNTIP_FINANZAS";

      // Fecha de corte motor
      public static final String FECHA_CORTE_MOTOR = "FECHA_CORTE_MOTOR";
      public static final String RUTA_FECHA_CORTE_MOTOR = "RUTA_FECHA_CORTE_MOTOR";
    }

    public abstract static class SMTP {
      public static final String SMTP_HOST = "SMTP_HOST";
      public static final String SMTP_PORT = "SMTP_PORT";
      public static final String SMTP_JBOSS_PUNTUAL_HOST = "SMTP_JBOSS_PUNTUAL_HOST";
      public static final String SMTP_JBOSS_PUNTUAL_PORT = "SMTP_JBOSS_PUNTUAL_PORT";
      public static final String SMTP_SOCKET_FACT = "SMTP_SOCKET_FACT";
      public static final String SMTP_AUTH = "SMTP_AUTH";
      public static final String SMTP_EMAIL_SEG_VEH = "SMTP_EMAIL_SEG_VEH";
      public static final String SMTP_PER_SEG_VEH = "SMTP_PER_SEG_VEH";
      public static final String SMTP_EMAIL_MS = "SMTP_EMAIL_MS";
      public static final String SMTP_PER_MS = "SMTP_PER_MS";
      public static final String SMTP_EMAIL_FFMM_CLI = "SMTP_EMAIL_FFMM_CLI";
      public static final String SMTP_PER_FFMM_CLI = "SMTP_PER_FFMM_CLI";
      public static final String SMTP_EMAIL_FFMM = "SMTP_EMAIL_FFMM";
      public static final String SMTP_PER_FFMM = "SMTP_PER_FFMM";
      public static final String SMTP_EMAIL_PRE_HIP = "SMTP_EMAIL_PRE_HIP";
      public static final String SMTP_PER_PRE_HIP = "SMTP_PER_PRE_HIP";
      public static final String SMTP_EMAIL_SEG_PT = "SMTP_EMAIL_SEG_PT";
      public static final String SMTP_PER_SEG_PT = "SMTP_PER_SEG_PT";
      public static final String SMTP_EMAIL_COT_VEH = "SMTP_EMAIL_COT_VEH";
      public static final String SMTP_PER_COT_VEH = "SMTP_PER_COT_VEH";
      public static final String SMTP_BCC_SEG_PT = "SMTP_BCC_SEG_PT";

      public static final String SMTP_EMAIL_TC_ADI = "SMTP_EMAIL_TC_ADI";
      public static final String SMTP_PER_TC_ADI = "SMTP_PER_TC_ADI";
      public static final String SMTP_EMAIL_TC_CRE = "SMTP_EMAIL_TC_CRE";
      public static final String SMTP_PER_TC_CRE = "SMTP_PER_TC_CRE";
      public static final String SMTP_EMAIL_AC = "SMTP_EMAIL_AC";
      public static final String SMTP_PER_AC = "SMTP_PER_AC";

      public static final String SMTP_EMAIL_DESTINATARIO_REMARKETING_TOC =
          "SMTP_EMAIL_DESTINATARIO_REMARKETING_TOC";
      public static final String SMTP_EMAIL_DESTINATARIO_REMARKETING_PAT =
          "SMTP_EMAIL_DESTINATARIO_REMARKETING_PAT";

      // reprogramacion de deudas
      public static final String SMTP_EMAIL_RPRO_DEUDAS = "SMTP_EMAIL_RPRO_DEUDAS";
      public static final String SMTP_PER_RPRO_DEUDAS = "SMTP_PER_RPRO_DEUDAS";

      // toc innominadas Lima
      public static final String SMTP_EMAIL_TOC_INNOMINADAS = "SMTP_EMAIL_TOC_INNOMINADAS";
      public static final String SMTP_PER_TOC_INNOMINADAS = "SMTP_PER_TOC_INNOMINADAS";

      // Cuentas afiliaciones vip
      public static final String SMTP_EMAIL_AFILIACIONES_VIP = "SMTP_EMAIL_AFILIACIONES_VIP";
      public static final String SMTP_PER_AFILIACIONES_VIP = "SMTP_PER_AFILIACIONES_VIP";

      // Cotizador vehicular
      public static final String SMTP_EMAIL_COTIZADOR_GLOMO = "SMTP_EMAIL_COTIZADOR_GLOMO";
      public static final String SMTP_PER_COTIZADOR_GLOMO = "SMTP_PER_COTIZADOR_GLOMO";

      // Apertura Cuenta Datio
      public static final String SMTP_EMAIL_AC_DATIO = "SMTP_EMAIL_AC_DATIO";
      public static final String SMTP_PER_AC_DATIO = "SMTP_PER_AC_DATIO";

      // Prestamo vehicular
      public static final String SMTP_EMAIL_PRESTAMO_VEHICULAR_GLOMO =
          "SMTP_EMAIL_PRESTAMO_VEHICULAR_GLOMO";
      public static final String SMTP_PER_PRESTAMO_VEHICULAR_GLOMO =
          "SMTP_PER_PRESTAMO_VEHICULAR_GLOMO";
    }

    public abstract static class TIPO_SEG {
      public static final String SEG_PROTE_TAR = "SEG_PROTE_TAR";
      public static final String SEG_RENTA_HOS = "SEG_RENTA_HOS";
      public static final String SEG_PROTE_MUL = "SEG_PROTE_MUL";
    }

    public abstract static class EMAIL {
      public static final String EMAIL_BUZON_TOC = "EMAIL_BUZON_TOC";
    }

    public abstract static class PLANTILLA_CORREO {
      public static final String TC_CREDITO_PROCESADO = "TC_CREDITO_PROCESADO_FTL";
      public static final String TC_CREDITO_RECHAZADO = "TC_CREDITO_RECHAZADO_FTL";
      public static final String TC_CREDITO_RECHAZADO_MAYOR_MOI =
          "TC_CREDITO_RECHAZADO_MAYOR_MOI_FTL";
    }

    public abstract static class CONFIGURACION {
      public static final String URL_LDAP = "URL_LDAP";
      public static final String URL_CONSULTAR_FONDOS_MUTUOS = "URL_CONSULTAR_FONDOS_MUTUOS";
      public static final String URL_CONSULTAR_DEPOSITO_PLAZO = "URL_CONSULTAR_DEPOSITO_PLAZO";
    }

    public abstract static class LDAP {
      public static final String FLAG_LDAP = "FLAG_LDAP";
      public static final String FLAGLDAPSINBD = "FLAGLDAPSINBD";
      public static final String FLAGHABWEBSEAL = "FLAGHABWEBSEAL";
    }

    public abstract static class PARAMETRO {
      public static final String ESTADO_REGISTRO = "ESTADO_REGISTRO";
    }
  }

  public abstract static class TIPO {
    public static final String FILE = "FILE";
    public static final String SMTP = "SMTP";
    public static final String TIPO_SEG = "TIPO_SEG";
    public static final String EMAIL_SEG_VEH = "EMAIL_SEG_VEH";
    public static final String EMAIL_SEG_PRO_MUL = "EMAIL_SEG_PRO_MUL";
    public static final String EMAIL_SEG_PRO_TAR = "EMAIL_SEG_PRO_TAR";
    public static final String EMAIL_SEG_REN_HOS = "EMAIL_SEG_REN_HOS";
    public static final String EMAIL_MUN_SUE_PER = "EMAIL_MUN_SUE_PER";
    public static final String EMAIL_MUN_SUE_EMP = "EMAIL_MUN_SUE_EMP";
    public static final String EMAIL_EECC_FFMM = "EMAIL_EECC_FFMM";
    public static final String EMAIL_PRE_HIP = "EMAIL_PRE_HIP";
    public static final String EMAIL_SUS_PRO_FFMM = "EMAIL_SUS_PRO_FFMM";
    public static final String EMAIL_SUS_PRO_BXI_FFMM = "EMAIL_SUS_PRO_BXI_FFMM";
    public static final String EMAIL_COT_VEH = "EMAIL_COT_VEH";
    public static final String EMAIL_TARJ_TOC = "EMAIL_TARJ_TOC";

    // reprogramacion de deudas
    public static final String EMAIL_RPRO_DEUDAS = "EMAIL_RPRO_DEUDAS";

    // toc innominadas Lima
    public static final String EMAIL_TOC_INNOMINADAS = "EMAIL_TOC_INNOMINADAS";

    // Cuenta Afiliaicones VIP
    public static final String EMAIL_AFILIACIONES_VIP = "EMAIL_AFILIACIONES_VIP";

    // Cotizador vehicular glomo
    public static final String EMAIL_COTIZADOR_GLOMO = "EMAIL_COTIZADOR_GLOMO";

    // Apertura Cuenta Datio
    public static final String EMAIL_AC_DATIO = "EMAIL_AC_DATIO";

    // Prestamo vehicular glomo
    public static final String EMAIL_PRESTAMO_VEHICULAR_GLOMO = "EMAIL_PRESTAMO_VEHICULAR_GLOMO";

    public static final String PLANTILLAS_FTL = "PLANTILLAS_FTL";

    public static final String CONFIGURACION = "CONFIGURACION";

    public static final String LDAP = "LDAP";

    // Anio Reporteria JOB
    public static final String ANIO_REPORTE_GIFOLE_JOB = "ANIO_REPORTE_GIFOLE_JOB";

    // Fecha de corte motor
    public static final String FECHA_CORTE_MOTOR = "FECHA_CORTE_MOTOR";
    public static final String PATH_FECHA_CORTE_MOTOR = "PATH_FECHA_CORTE_MOTOR";
  }

  public abstract static class FILE_UTIL {
    public static final String FLAG_NO_APLICA = "";
    public static final String FLAG_ACTIVO = "1";
    public static final String FLAG_INACTIVO = "0";
    public static final String FLAG_SUB_DETALLE = "2";

    public static final String ESTADO_ACTIVO = "A";
    public static final String ESTADO_INACTIVO = "I";

    // public static final String PATH_OUT_INTRANET = "PATH_OUT_INTRANET_";
    public static final String JB_PATH_OUT_INTRANET = "JB_PATH_OUT_INTRANET_";
    public static final String FILE = "FILE";
    public static final String SUBDETALLE = "_SUBDETALLE_";

    public static final String SEPARADOR = "_";
    public static final String PUNTO = ".";
    public static final String EXT_PUNTO = "\\.";

    public static final String FILE_PDF = "pdf";
  }

  @Override
  public int hashCode() {
    HashCodeBuilder hashCodeBuilder = new HashCodeBuilder(3, 7);
    hashCodeBuilder.append(codigo);
    return hashCodeBuilder.toHashCode();
  }

  @Override
  public boolean equals(Object obj) {
    boolean equals = false;
    if (obj instanceof Parametro) {
      Parametro bean = (Parametro) obj;
      EqualsBuilder equalsBuilder = new EqualsBuilder();
      equalsBuilder.append(codigo, bean.codigo);
      equalsBuilder.append(tipo, bean.tipo);
      equals = equalsBuilder.isEquals();
    }
    return equals;
  }
}
