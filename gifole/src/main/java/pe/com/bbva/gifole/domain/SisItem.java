package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "SIS_ITEM")
public class SisItem implements Serializable {

  @Id
  @Column(unique = true, nullable = false, precision = 16)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequence")
  @SequenceGenerator(name = "sequence", sequenceName = "SQ_SIS_ITEM", allocationSize = 1)
  private Long id;

  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Column(name = "ESTADO")
  private String estado;

  @OneToOne
  @JoinColumn(name = "ITEM", nullable = true)
  private SisItem itemId;
}
