package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import lombok.Data;

@Data
@Entity
@Table(name = "SEGURO_TIPO")
public class SeguroTipo {
  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_SEGURO_TIPO")
  @TableGenerator(
      name = "SEQ_SEGURO_TIPO",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.SeguroTipo",
      allocationSize = 1)
  private Long id;

  // bidirectional many-to-one association to Parametro
  @ManyToOne
  @JoinColumn(name = "TIPO")
  private Parametro tipo;

  // bidirectional many-to-one association to Seguro
  @ManyToOne
  @JoinColumn(name = "SEGURO")
  private Seguro seguro;

  @Column(name = "PROCESADO", length = 1)
  private String procesado;
}
