package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.AfiliacionEECCFFMM;
import pe.com.bbva.gifole.domain.AfiliacionEECCFFMMDetalle;

@Component
public class AfiliacionEECCFFMMDetalleMapper implements RowMapper<AfiliacionEECCFFMMDetalle> {

  @Override
  public AfiliacionEECCFFMMDetalle mapRow(ResultSet rs, int i) throws SQLException {
    AfiliacionEECCFFMMDetalle afiliacionDetalle = new AfiliacionEECCFFMMDetalle();
    afiliacionDetalle.setNombreFondo(rs.getString("NOMBRE_FONDO"));
    afiliacionDetalle.setNumeroContrato(rs.getString("NUMERO_CONTRATO"));
    afiliacionDetalle.setAfiliacionEECCFFMM(new AfiliacionEECCFFMM());
    afiliacionDetalle.getAfiliacionEECCFFMM().setId(rs.getLong("ID"));
    afiliacionDetalle.getAfiliacionEECCFFMM().setNombre(rs.getString("NOMBRE"));
    afiliacionDetalle.getAfiliacionEECCFFMM().setCorreo1(rs.getString("CORREO1"));
    afiliacionDetalle.getAfiliacionEECCFFMM().setFechaRegistro(rs.getTimestamp("FECHA_REGISTRO"));
    afiliacionDetalle.getAfiliacionEECCFFMM().setCodigoCentral(rs.getString("CODIGO_CENTRAL"));
    return afiliacionDetalle;
  }
}
