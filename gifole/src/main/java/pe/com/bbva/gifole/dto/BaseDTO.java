package pe.com.bbva.gifole.dto;

import java.time.LocalDateTime;

/** DTO base con campos comunes */
public abstract class BaseDTO {

  private Long id;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;

  // Getters and setters
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public LocalDateTime getUpdatedAt() {
    return updatedAt;
  }

  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }
}
