package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;

public class CancelacionAutomaticaBean implements Serializable {

  private static final long serialVersionUID = -2022072908844005159L;

  private Long id;
  private String codCentral;
  private String subProducto;
  private String nroContrato;
  private String nombrePremio;
  private String canal;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getCodCentral() {
    return codCentral;
  }

  public void setCodCentral(String codCentral) {
    this.codCentral = codCentral;
  }

  public String getSubProducto() {
    return subProducto;
  }

  public void setSubProducto(String subProducto) {
    this.subProducto = subProducto;
  }

  public String getNroContrato() {
    return nroContrato;
  }

  public void setNroContrato(String nroContrato) {
    this.nroContrato = nroContrato;
  }

  public String getNombrePremio() {
    return nombrePremio;
  }

  public void setNombrePremio(String nombrePremio) {
    this.nombrePremio = nombrePremio;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }
}
