package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;

public class ProductoSeguroBean implements Serializable {
  private Long id;
  private String codigoProducto;
  private String nombreProducto;
  private String indRoyal;
  private String estado;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getCodigoProducto() {
    return codigoProducto;
  }

  public void setCodigoProducto(String codigoProducto) {
    this.codigoProducto = codigoProducto;
  }

  public String getNombreProducto() {
    return nombreProducto;
  }

  public void setNombreProducto(String nombreProducto) {
    this.nombreProducto = nombreProducto;
  }

  public String getIndRoyal() {
    return indRoyal;
  }

  public void setIndRoyal(String indRoyal) {
    this.indRoyal = indRoyal;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }
}
