package pe.com.bbva.gifole.common.bean;

public class PremioEntregadoBean {

  private Long id;
  private String nroSecuencia;
  private String codigoAtributo;
  private String dniEnClaro;
  private String estadoDelAbono;
  private String descripcionDelEstado;
  private String descripcionDelAbono;
  private String puntosAbonados;
  private String fechaAbono;
  private String origen;
  private String tipoDoc;
  private String nroDocReferido;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getNroSecuencia() {
    return nroSecuencia;
  }

  public void setNroSecuencia(String nroSecuencia) {
    this.nroSecuencia = nroSecuencia;
  }

  public String getCodigoAtributo() {
    return codigoAtributo;
  }

  public void setCodigoAtributo(String codigoAtributo) {
    this.codigoAtributo = codigoAtributo;
  }

  public String getDniEnClaro() {
    return dniEnClaro;
  }

  public void setDniEnClaro(String dniEnClaro) {
    this.dniEnClaro = dniEnClaro;
  }

  public String getEstadoDelAbono() {
    return estadoDelAbono;
  }

  public void setEstadoDelAbono(String estadoDelAbono) {
    this.estadoDelAbono = estadoDelAbono;
  }

  public String getDescripcionDelEstado() {
    return descripcionDelEstado;
  }

  public void setDescripcionDelEstado(String descripcionDelEstado) {
    this.descripcionDelEstado = descripcionDelEstado;
  }

  public String getPuntosAbonados() {
    return puntosAbonados;
  }

  public void setPuntosAbonados(String puntosAbonados) {
    this.puntosAbonados = puntosAbonados;
  }

  public String getFechaAbono() {
    return fechaAbono;
  }

  public void setFechaAbono(String fechaAbono) {
    this.fechaAbono = fechaAbono;
  }

  public String getDescripcionDelAbono() {
    return descripcionDelAbono;
  }

  public void setDescripcionDelAbono(String descripcionDelAbono) {
    this.descripcionDelAbono = descripcionDelAbono;
  }

  public String getOrigen() {
    return origen;
  }

  public void setOrigen(String origen) {
    this.origen = origen;
  }

  public String getTipoDoc() {
    return tipoDoc;
  }

  public void setTipoDoc(String tipoDoc) {
    this.tipoDoc = tipoDoc;
  }

  public String getNroDocReferido() {
    return nroDocReferido;
  }

  public void setNroDocReferido(String nroDocReferido) {
    this.nroDocReferido = nroDocReferido;
  }
}
