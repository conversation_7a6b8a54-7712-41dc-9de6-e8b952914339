package pe.com.bbva.gifole.common.bean;

import java.util.Date;

public class PrestamoAlToqueBean {

  private Long id;
  private String codigoCentral;
  private String tipoDocumento;
  private String numeroDocumento;
  private String nombreCompleto;
  private String telefono;
  private String correo;
  private Date fechaRegistro;
  private String monto;
  private String cuota;
  private String plazo;
  private String tea;
  private String tcea;
  private String simulacion;
  private String canal;
  private String estado;
  private String numeroContrato;
  private Integer flagPeriodoGracia;
  private String fechaDesde;
  private String fechaHasta;
  private String correoExtorno;
  private Date fechaAprobacionPeriodoGracia;
  private Date fechaSolicitudExtorno;
  private Date fechaExtorno;
  private String estadoPeriodoGracia;
  private String estadoExtorno;
  private String marcaPh;

  private String pasoAbandono;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public String getNumeroDocumento() {
    return numeroDocumento;
  }

  public void setNumeroDocumento(String numeroDocumento) {
    this.numeroDocumento = numeroDocumento;
  }

  public String getNombreCompleto() {
    return nombreCompleto;
  }

  public void setNombreCompleto(String nombreCompleto) {
    this.nombreCompleto = nombreCompleto;
  }

  public String getTelefono() {
    return telefono;
  }

  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public Date getFechaRegistro() {
    return fechaRegistro;
  }

  public void setFechaRegistro(Date fechaRegistro) {
    this.fechaRegistro = fechaRegistro;
  }

  public String getMonto() {
    return monto;
  }

  public void setMonto(String monto) {
    this.monto = monto;
  }

  public String getCuota() {
    return cuota;
  }

  public void setCuota(String cuota) {
    this.cuota = cuota;
  }

  public String getPlazo() {
    return plazo;
  }

  public void setPlazo(String plazo) {
    this.plazo = plazo;
  }

  public String getTea() {
    return tea;
  }

  public void setTea(String tea) {
    this.tea = tea;
  }

  public String getTcea() {
    return tcea;
  }

  public void setTcea(String tcea) {
    this.tcea = tcea;
  }

  public String getSimulacion() {
    return simulacion;
  }

  public void setSimulacion(String simulacion) {
    this.simulacion = simulacion;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public String getNumeroContrato() {
    return numeroContrato;
  }

  public void setNumeroContrato(String numeroContrato) {
    this.numeroContrato = numeroContrato;
  }

  public Integer getFlagPeriodoGracia() {
    return flagPeriodoGracia;
  }

  public void setFlagPeriodoGracia(Integer flagPeriodoGracia) {
    this.flagPeriodoGracia = flagPeriodoGracia;
  }

  public String getFechaDesde() {
    return fechaDesde;
  }

  public void setFechaDesde(String fechaDesde) {
    this.fechaDesde = fechaDesde;
  }

  public String getFechaHasta() {
    return fechaHasta;
  }

  public void setFechaHasta(String fechaHasta) {
    this.fechaHasta = fechaHasta;
  }

  public String getCorreoExtorno() {
    return correoExtorno;
  }

  public void setCorreoExtorno(String correoExtorno) {
    this.correoExtorno = correoExtorno;
  }

  public Date getFechaAprobacionPeriodoGracia() {
    return fechaAprobacionPeriodoGracia;
  }

  public void setFechaAprobacionPeriodoGracia(Date fechaAprobacionPeriodoGracia) {
    this.fechaAprobacionPeriodoGracia = fechaAprobacionPeriodoGracia;
  }

  public Date getFechaSolicitudExtorno() {
    return fechaSolicitudExtorno;
  }

  public void setFechaSolicitudExtorno(Date fechaSolicitudExtorno) {
    this.fechaSolicitudExtorno = fechaSolicitudExtorno;
  }

  public Date getFechaExtorno() {
    return fechaExtorno;
  }

  public void setFechaExtorno(Date fechaExtorno) {
    this.fechaExtorno = fechaExtorno;
  }

  public String getEstadoPeriodoGracia() {
    return estadoPeriodoGracia;
  }

  public void setEstadoPeriodoGracia(String estadoPeriodoGracia) {
    this.estadoPeriodoGracia = estadoPeriodoGracia;
  }

  public String getEstadoExtorno() {
    return estadoExtorno;
  }

  public void setEstadoExtorno(String estadoExtorno) {
    this.estadoExtorno = estadoExtorno;
  }

  public String getMarcaPh() {
    return marcaPh;
  }

  public void setMarcaPh(String marcaPh) {
    this.marcaPh = marcaPh;
  }

  public String getPasoAbandono() {
    return pasoAbandono;
  }

  public void setPasoAbandono(String pasoAbandono) {
    this.pasoAbandono = pasoAbandono;
  }
}
