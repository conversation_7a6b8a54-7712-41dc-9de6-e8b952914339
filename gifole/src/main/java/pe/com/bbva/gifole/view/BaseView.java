package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.theme.lumo.LumoUtility;
import pe.com.bbva.gifole.view.theme.BBVATheme;

/** Vista base para todas las vistas de la aplicación */
public abstract class BaseView extends VerticalLayout {

  public BaseView() {
    initializeView();
    setupLayout();
    createContent();
  }

  private void initializeView() {
    setSizeFull();
    setPadding(true);
    setSpacing(true);
    addClassName(LumoUtility.Background.BASE);
  }

  private void setupLayout() {
    setDefaultHorizontalComponentAlignment(Alignment.STRETCH);
  }

  /** Método abstracto para crear el contenido específico de cada vista */
  protected abstract void createContent();

  /** Aplica el tema BBVA a un componente */
  protected void applyBBVATheme(com.vaadin.flow.component.Component component, String themeClass) {
    component.addClassName(themeClass);
  }

  /** Crea un header con estilo BBVA */
  protected com.vaadin.flow.component.html.H1 createBBVAHeader(String title) {
    com.vaadin.flow.component.html.H1 header = new com.vaadin.flow.component.html.H1(title);
    header.addClassName(BBVATheme.BBVA_TEXT_PRIMARY);
    return header;
  }

  /** Crea un título de página con estilo BBVA homologado */
  protected com.vaadin.flow.component.html.H2 createPageTitle(String title) {
    com.vaadin.flow.component.html.H2 pageTitle = new com.vaadin.flow.component.html.H2(title);
    pageTitle.addClassName("bbva-page-title");
    return pageTitle;
  }
}
