package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class TarjetaGarantizadaBean implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;
  private String codigoCentral;
  private String tipoDocumento;
  private String nroDocumento;
  private String nombres;
  private String apellidoPaterno;
  private String apellidoMaterno;
  private String nombreCompleto;
  private String fechaRegistro;
  private String nroTarjeta;
  private String telefono;
  private String correo;
  private String estado;
  private String codigoOficina;
  private String descripcionOficina;
  private String oficinaRecojo;
  private String canal;
  private boolean ordenamientoCanal;
  private Date fechaRegistroDesde;
  private Date fechaRegistroHasta;
  private String fechaInicioCampania;
  private String fechaFinCampania;
  private String registroGestor;
  private String lineaCredito;
  private String tipoTarjeta;
  private String cuentaRespaldo;
  private String fechaAlta;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public String getNroDocumento() {
    return nroDocumento;
  }

  public void setNroDocumento(String nroDocumento) {
    this.nroDocumento = nroDocumento;
  }

  public String getNombres() {
    return nombres;
  }

  public void setNombres(String nombres) {
    this.nombres = nombres;
  }

  public String getApellidoPaterno() {
    return apellidoPaterno;
  }

  public void setApellidoPaterno(String apellidoPaterno) {
    this.apellidoPaterno = apellidoPaterno;
  }

  public String getApellidoMaterno() {
    return apellidoMaterno;
  }

  public void setApellidoMaterno(String apellidoMaterno) {
    this.apellidoMaterno = apellidoMaterno;
  }

  public String getNombreCompleto() {
    return nombreCompleto;
  }

  public void setNombreCompleto(String nombreCompleto) {
    this.nombreCompleto = nombreCompleto;
  }

  public String getFechaRegistro() {
    return fechaRegistro;
  }

  public void setFechaRegistro(String fechaRegistro) {
    this.fechaRegistro = fechaRegistro;
  }

  public String getNroTarjeta() {
    return nroTarjeta;
  }

  public void setNroTarjeta(String nroTarjeta) {
    this.nroTarjeta = nroTarjeta;
  }

  public String getTelefono() {
    return telefono;
  }

  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public String getCodigoOficina() {
    return codigoOficina;
  }

  public void setCodigoOficina(String codigoOficina) {
    this.codigoOficina = codigoOficina;
  }

  public String getDescripcionOficina() {
    return descripcionOficina;
  }

  public void setDescripcionOficina(String descripcionOficina) {
    this.descripcionOficina = descripcionOficina;
  }

  public String getOficinaRecojo() {
    return oficinaRecojo;
  }

  public void setOficinaRecojo(String oficinaRecojo) {
    this.oficinaRecojo = oficinaRecojo;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public boolean isOrdenamientoCanal() {
    return ordenamientoCanal;
  }

  public void setOrdenamientoCanal(boolean ordenamientoCanal) {
    this.ordenamientoCanal = ordenamientoCanal;
  }

  public Date getFechaRegistroDesde() {
    return fechaRegistroDesde;
  }

  public void setFechaRegistroDesde(Date fechaRegistroDesde) {
    this.fechaRegistroDesde = fechaRegistroDesde;
  }

  public Date getFechaRegistroHasta() {
    return fechaRegistroHasta;
  }

  public void setFechaRegistroHasta(Date fechaRegistroHasta) {
    this.fechaRegistroHasta = fechaRegistroHasta;
  }

  public String getFechaInicioCampania() {
    return fechaInicioCampania;
  }

  public void setFechaInicioCampania(String fechaInicioCampania) {
    this.fechaInicioCampania = fechaInicioCampania;
  }

  public String getFechaFinCampania() {
    return fechaFinCampania;
  }

  public void setFechaFinCampania(String fechaFinCampania) {
    this.fechaFinCampania = fechaFinCampania;
  }

  public String getRegistroGestor() {
    return registroGestor;
  }

  public void setRegistroGestor(String registroGestor) {
    this.registroGestor = registroGestor;
  }

  public String getLineaCredito() {
    return lineaCredito;
  }

  public void setLineaCredito(String lineaCredito) {
    this.lineaCredito = lineaCredito;
  }

  public String getTipoTarjeta() {
    return tipoTarjeta;
  }

  public void setTipoTarjeta(String tipoTarjeta) {
    this.tipoTarjeta = tipoTarjeta;
  }

  public String getCuentaRespaldo() {
    return cuentaRespaldo;
  }

  public void setCuentaRespaldo(String cuentaRespaldo) {
    this.cuentaRespaldo = cuentaRespaldo;
  }

  public String getFechaAlta() {
    return fechaAlta;
  }

  public void setFechaAlta(String fechaAlta) {
    this.fechaAlta = fechaAlta;
  }
}
