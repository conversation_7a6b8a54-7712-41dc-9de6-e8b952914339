package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.TCAdicionalDetEstado;

@Component
public class AdicoinalConsultaEstadoMapper implements RowMapper<TCAdicionalDetEstado> {

  @Override
  public TCAdicionalDetEstado mapRow(ResultSet rs, int i) throws SQLException {

    TCAdicionalDetEstado adicional = new TCAdicionalDetEstado();
    adicional.setId(rs.getLong("ID"));
    return adicional;
  }
}
