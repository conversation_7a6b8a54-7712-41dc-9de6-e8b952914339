package pe.com.bbva.gifole.exception;

/** Excepción personalizada que se lanza cuando no se encuentra un usuario en el sistema */
public class UsuarioNotFoundException extends RuntimeException {

  private final String codigo;
  private final Object parametros;

  public UsuarioNotFoundException(String mensaje) {
    super(mensaje);
    this.codigo = "USUARIO_NOT_FOUND";
    this.parametros = null;
  }

  public UsuarioNotFoundException(String mensaje, String codigo) {
    super(mensaje);
    this.codigo = codigo;
    this.parametros = null;
  }

  public UsuarioNotFoundException(String mensaje, String codigo, Object parametros) {
    super(mensaje);
    this.codigo = codigo;
    this.parametros = parametros;
  }

  public UsuarioNotFoundException(String mensaje, Throwable causa) {
    super(mensaje, causa);
    this.codigo = "USUARIO_NOT_FOUND";
    this.parametros = null;
  }

  public String getCodigo() {
    return codigo;
  }

  public Object getParametros() {
    return parametros;
  }
}
