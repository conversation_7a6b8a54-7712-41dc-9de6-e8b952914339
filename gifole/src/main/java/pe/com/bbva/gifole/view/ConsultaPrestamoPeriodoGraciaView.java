package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.PrestamoAlToqueBean; // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Préstamo Período de Gracia")
@Route(value = "reporte/prestamos/periodo-gracia", layout = MainLayout.class)
public class ConsultaPrestamoPeriodoGraciaView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<PrestamoAlToqueBean> allData = new ArrayList<>();
    private DataTable<PrestamoAlToqueBean> dataTable;

    public ConsultaPrestamoPeriodoGraciaView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Préstamo Período de Gracia");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<PrestamoAlToqueBean>builder()
                .id("tabla-prestamo-periodo-gracia") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaPrestamoPeriodoGraciaUI)
                // Seleccionadas según las columnas visibles en la UI antigua y el análisis del código
                .column("fechaRegistro", "Fecha Registro", // Equivalente a "fechaSolicitud" del DTO
                    bean -> bean.getFechaRegistro() != null ? FORMATO_FECHA.format(bean.getFechaRegistro()) : "", "150px")
                .column("codigoCentral", "Cód.Central", // Equivalente a "numeroPrestamo" (parcial) del DTO
                    bean -> StringUtils.trimToEmpty(bean.getCodigoCentral()), "100px")
                .column("nombreCompleto", "Nombres Titular", // Equivalente a "cliente" del DTO
                    bean -> StringUtils.trimToEmpty(bean.getNombreCompleto()), "250px")
                .column("correo", "Correo", // Información de contacto
                    bean -> StringUtils.trimToEmpty(bean.getCorreo()), "250px")
                .column("numeroContrato", "Contrato", // Equivalente a "numeroPrestamo" (parcial) del DTO
                    bean -> StringUtils.trimToEmpty(bean.getNumeroContrato()), "200px")
                .column("canal", "Canal", // Canal
                    bean -> StringUtils.trimToEmpty(bean.getCanal()), "170px")
                .column("estadoPeriodoGracia", "Estado periodo de gracia", // Equivalente a "estado" del DTO
                    bean -> StringUtils.trimToEmpty(bean.getEstadoPeriodoGracia()), "200px")
                .column("fechaAprobacionPeriodoGracia", "Fecha que se procesó", // Equivalente a "fechaAprobacion" del DTO
                    bean -> bean.getFechaAprobacionPeriodoGracia() != null ? FORMATO_FECHA.format(bean.getFechaAprobacionPeriodoGracia()) : "", "150px")
                .column("fechaSolicitudExtorno", "Fecha de rechazo", // Información adicional
                    bean -> bean.getFechaSolicitudExtorno() != null ? FORMATO_FECHA.format(bean.getFechaSolicitudExtorno()) : "", "150px")
                .column("correoExtorno", "Correo extorno periodo de gracia", // Información adicional
                    bean -> StringUtils.trimToEmpty(bean.getCorreoExtorno()), "250px")
                .column("estadoExtorno", "Estado extorno periodo de gracia", // Información adicional
                    bean -> StringUtils.trimToEmpty(bean.getEstadoExtorno()), "200px")
                .column("fechaExtorno", "Fecha de extorno", // Información adicional
                    bean -> bean.getFechaExtorno() != null ? FORMATO_FECHA.format(bean.getFechaExtorno()) : "", "150px")
                // Puedes agregar más columnas aquí si las necesitas, siguiendo el mismo patrón
                // Datos (inicialmente vacío)
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}