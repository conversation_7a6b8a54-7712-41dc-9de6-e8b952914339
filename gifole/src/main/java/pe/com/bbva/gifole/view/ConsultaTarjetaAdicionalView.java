package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils; // Ajusta el paquete si es necesario

import pe.com.bbva.gifole.domain.TCAdicionalDetEstado;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Tarjeta Adicional") // Ajusta el título según corresponda
@Route(value = "reporte/tarjetas/adicionales", layout = MainLayout.class) // Ajusta la ruta según corresponda
public class ConsultaTarjetaAdicionalView extends VerticalLayout { // Ajusta el nombre de la clase según corresponda

  // Formato de fecha para mostrar en la tabla
  private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy");

  private final List<TCAdicionalDetEstado> allData = new ArrayList<>();
  private DataTable<TCAdicionalDetEstado> dataTable;

  public ConsultaTarjetaAdicionalView() { // Ajusta el nombre del constructor
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Tarjeta Adicional"); // Ajusta el título
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);

    // Cargar datos de ejemplo o reales (opcional)
    // loadSampleData();
    // loadData(service.getData()); // Ejemplo de carga real
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    dataTable = DataTable.<TCAdicionalDetEstado>builder()
        .id("tabla-tarjeta-adicional") // ID único para la tabla
        // Mapeo de columnas basado en la UI anterior
        .column("nombre", "Nombre",
            bean -> StringUtils.trimToEmpty(
                bean.getTcAdicionalDetalle() != null &&
                    bean.getTcAdicionalDetalle().getTcAdicional() != null
                        ? bean.getTcAdicionalDetalle().getTcAdicional().getNombre()
                        : ""),
            "250px")
        .column("nroTarjeta", "Nro Tarjeta", // Formateado
            bean -> {
              if (bean.getTcAdicionalDetalle() != null &&
                  bean.getTcAdicionalDetalle().getTcAdicional() != null) {
                String tarjetaTitular = bean.getTcAdicionalDetalle().getTcAdicional().getTarjetaTitular();
                if (tarjetaTitular != null && tarjetaTitular.length() == 16) {
                  String part1 = tarjetaTitular.substring(0, 4);
                  String part2 = tarjetaTitular.substring(4, 8);
                  String part3 = tarjetaTitular.substring(8, 12);
                  String part4 = tarjetaTitular.substring(12, 16);
                  return part1 + "-" + part2 + "-" + part3 + "-" + part4;
                } else {
                  return StringUtils.trimToEmpty(tarjetaTitular);
                }
              }
              return "";
            }, "150px")
        .column("nombreTarjeta", "Nombre Tarjeta",
            bean -> StringUtils.trimToEmpty(
                bean.getTcAdicionalDetalle() != null &&
                    bean.getTcAdicionalDetalle().getTcAdicional() != null
                        ? bean.getTcAdicionalDetalle().getTcAdicional().getNombreTarjeta()
                        : ""),
            "150px")
        .column("divisa", "Divisa",
            bean -> StringUtils.trimToEmpty(
                bean.getTcAdicionalDetalle() != null &&
                    bean.getTcAdicionalDetalle().getTcAdicional() != null
                        ? bean.getTcAdicionalDetalle().getTcAdicional().getDivisa()
                        : ""),
            "100px")
        .column("fechaRegistro", "Fecha Registro",
            bean -> {
              if (bean.getTcAdicionalDetalle() != null &&
                  bean.getTcAdicionalDetalle().getTcAdicional().getFechaRegistro() != null) {
                return FORMATO_FECHA.format(bean.getTcAdicionalDetalle().getTcAdicional().getFechaRegistro());
              }
              return "";
            }, "120px")
        .column("estado", "Estado",
            bean -> StringUtils.trimToEmpty(
                bean.getTcAdicionalDetalle() != null ? bean.getTcAdicionalDetalle().getEstado() : ""),
            "100px")
        .column("combo", "Combo",
            bean -> StringUtils.trimToEmpty(
                bean.getTcAdicionalDetalle() != null ? bean.getTcAdicionalDetalle().getCombo() : ""),
            "100px")
        .column("canal", "Canal",
            bean -> StringUtils.trimToEmpty(
                bean.getTcAdicionalDetalle() != null ? bean.getTcAdicionalDetalle().getTcAdicional().getCanal() : ""),
            "100px")
        .column("fechaModificacion", "Fecha Modificación",
            bean -> {
              if (bean.getTcAdicionalDetalle().getFechaModificacion() != null) {
                return FORMATO_FECHA.format(bean.getTcAdicionalDetalle().getFechaModificacion());
              }
              return "";
            }, "150px")
        // Nota: La columna "detEstado" (botón) y "objeto" (para exportación)
        // requieren manejo especial que no se incluye directamente en el builder.
        // Datos (inicialmente vacío)
        .items(new ArrayList<>())
        .pageSize(10)
        .build();

    card.add(dataTable);
    return card;
  }

}