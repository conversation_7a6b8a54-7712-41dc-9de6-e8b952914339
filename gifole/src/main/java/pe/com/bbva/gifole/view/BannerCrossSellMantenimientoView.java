package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;

@PageTitle("Mantenimiento de Banner Cross Sell")
@Route(value = "banner-cross-sell-mantenimiento", layout = MainLayout.class)
public class BannerCrossSellMantenimientoView extends VerticalLayout {

  // Filtros
  private final ComboBox<String> estadoFilter = new ComboBox<>("Estado");
  private final TextField flujoFilter = new TextField("Flujo");
  private final TextField producto1Filter = new TextField("Producto 1");
  private final TextField producto2Filter = new TextField("Producto 2");
  private final TextField producto3Filter = new TextField("Producto 3");
  private final TextField producto4Filter = new TextField("Producto 4");

  public BannerCrossSellMantenimientoView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título principal
    H2 title = new H2("Mantenimiento de Banner Cross Sell");
    title.addClassName("bbva-grid-title");

    // Panel de filtros
    HorizontalLayout filtersPanel = createFiltersPanel();

    mainLayout.add(title, filtersPanel);
    add(mainLayout);
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.END);
    filtersPanel.setSpacing(false);

    estadoFilter.addClassName("bbva-input-floating");
    estadoFilter.setWidth("90px");
    estadoFilter.setItems("Todos", "Activo", "Inactivo");
    estadoFilter.setValue("Todos");

    flujoFilter.addClassName("bbva-input-floating");
    flujoFilter.setWidth("120px");
    flujoFilter.setPlaceholder("Buscar flujo...");

    producto1Filter.addClassName("bbva-input-floating");
    producto1Filter.setWidth("100px");
    producto1Filter.setPlaceholder("Producto 1...");

    producto2Filter.addClassName("bbva-input-floating");
    producto2Filter.setWidth("100px");
    producto2Filter.setPlaceholder("Producto 2...");

    producto3Filter.addClassName("bbva-input-floating");
    producto3Filter.setWidth("100px");
    producto3Filter.setPlaceholder("Producto 3...");

    producto4Filter.addClassName("bbva-input-floating");
    producto4Filter.setWidth("100px");
    producto4Filter.setPlaceholder("Producto 4...");

    filtersPanel.add(
        estadoFilter,
        flujoFilter,
        producto1Filter,
        producto2Filter,
        producto3Filter,
        producto4Filter);

    return filtersPanel;
  }
}
