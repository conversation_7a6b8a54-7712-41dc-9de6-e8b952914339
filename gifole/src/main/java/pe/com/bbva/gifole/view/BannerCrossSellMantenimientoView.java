package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.shared.Tooltip;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import pe.com.bbva.gifole.common.bean.BannerCrossSellBean;
import pe.com.bbva.gifole.domain.Parametro;

@PageTitle("Mantenimiento de Banner Cross Sell")
@Route(value = "banner-cross-sell-mantenimiento", layout = MainLayout.class)
public class BannerCrossSellMantenimientoView extends VerticalLayout {

  // Constantes
  private static final String ACTIVO = "A";
  private static final String INACTIVO = "I";

  private final Grid<BannerCrossSellBean> grid = new Grid<>(BannerCrossSellBean.class, false);
  private final List<BannerCrossSellBean> bannersList = new ArrayList<>();
  private final List<BannerCrossSellBean> allBanners = new ArrayList<>();

  // Variables de paginación
  private int currentPage = 0;
  private final int pageSize = 10;
  private int totalPages = 0;

  // Componentes de paginación
  private Button previousButton;
  private Button nextButton;
  private Span pageInfo;

  // Filtros
  private ComboBox<String> estadoFilter;
  private TextField flujoFilter;
  private TextField producto1Filter;
  private TextField producto2Filter;
  private TextField producto3Filter;
  private TextField producto4Filter;

  // Campos del formulario
  private final ComboBox<String> cmbFlujo = new ComboBox<>();
  private final ComboBox<String> cmbEstado = new ComboBox<>();
  private final ComboBox<String> cmbProducto1 = new ComboBox<>();
  private final TextField txtImagenDesktop1 = new TextField();
  private final TextField txtImagenResponsive1 = new TextField();
  private final TextField txtRedireccion1 = new TextField();
  private final ComboBox<String> cmbProducto2 = new ComboBox<>();
  private final TextField txtImagenDesktop2 = new TextField();
  private final TextField txtImagenResponsive2 = new TextField();
  private final TextField txtRedireccion2 = new TextField();
  private final ComboBox<String> cmbProducto3 = new ComboBox<>();
  private final TextField txtImagenDesktop3 = new TextField();
  private final TextField txtImagenResponsive3 = new TextField();
  private final TextField txtRedireccion3 = new TextField();
  private final ComboBox<String> cmbProducto4 = new ComboBox<>();
  private final TextField txtImagenDesktop4 = new TextField();
  private final TextField txtImagenResponsive4 = new TextField();
  private final TextField txtRedireccion4 = new TextField();

  // Botones
  private final Button btnGuardar = new Button("Guardar");
  private final Button btnEliminar = new Button("Eliminar");
  private final Button btnNuevo = new Button("Nuevo Banner", VaadinIcon.PLUS.create());
  private final Button btnCerrarDrawer = new Button(VaadinIcon.CLOSE.create());

  // Drawer/Panel flotante
  private VerticalLayout drawerPanel;
  private boolean drawerVisible = false;
  private BannerCrossSellBean bannerEnEdicion = null;

  public BannerCrossSellMantenimientoView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);
    getStyle().set("position", "relative"); // Para posicionamiento del drawer

    // Crear layout principal
    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Panel de filtros superiores
    HorizontalLayout filtersPanel = createFiltersPanel();

    // Header con título y botón nuevo
    HorizontalLayout headerLayout = createHeaderLayout();

    // Card para el Grid
    VerticalLayout gridCard = createGridCard();
    gridCard.setSizeFull();

    mainLayout.add(filtersPanel, headerLayout, gridCard);
    mainLayout.setFlexGrow(1, gridCard);

    // Crear drawer flotante
    drawerPanel = createDrawerPanel();

    add(mainLayout, drawerPanel);

    // Configurar componentes
    configureComponents();

    // Cargar datos de ejemplo
    loadData();
    updateGrid();
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(FlexComponent.Alignment.END);
    filtersPanel.setSpacing(false); // Controlado por CSS gap

    // Configurar filtros con clases globales como en TcTarifarioMantenimientoView
    estadoFilter = new ComboBox<>("Estado");
    estadoFilter.addClassName("bbva-input-floating");
    estadoFilter.setWidth("90px");
    estadoFilter.setItems("Todos", "Activo", "Inactivo");
    estadoFilter.setValue("Todos");
    estadoFilter.addValueChangeListener(e -> applyFilters());

    flujoFilter = new TextField("Flujo");
    flujoFilter.addClassName("bbva-input-floating");
    flujoFilter.setWidth("120px");
    flujoFilter.setPlaceholder("Buscar flujo...");
    flujoFilter.addValueChangeListener(e -> applyFilters());

    producto1Filter = new TextField("Producto 1");
    producto1Filter.addClassName("bbva-input-floating");
    producto1Filter.setWidth("100px");
    producto1Filter.setPlaceholder("Producto 1...");
    producto1Filter.addValueChangeListener(e -> applyFilters());

    producto2Filter = new TextField("Producto 2");
    producto2Filter.addClassName("bbva-input-floating");
    producto2Filter.setWidth("100px");
    producto2Filter.setPlaceholder("Producto 2...");
    producto2Filter.addValueChangeListener(e -> applyFilters());

    producto3Filter = new TextField("Producto 3");
    producto3Filter.addClassName("bbva-input-floating");
    producto3Filter.setWidth("100px");
    producto3Filter.setPlaceholder("Producto 3...");
    producto3Filter.addValueChangeListener(e -> applyFilters());

    producto4Filter = new TextField("Producto 4");
    producto4Filter.addClassName("bbva-input-floating");
    producto4Filter.setWidth("100px");
    producto4Filter.setPlaceholder("Producto 4...");
    producto4Filter.addValueChangeListener(e -> applyFilters());

    // Agregar todos los filtros directamente al panel principal
    filtersPanel.add(
        estadoFilter,
        flujoFilter,
        producto1Filter,
        producto2Filter,
        producto3Filter,
        producto4Filter);

    return filtersPanel;
  }

  private HorizontalLayout createHeaderLayout() {
    H2 title = new H2("Lista de Banner Cross Sell");
    title.addClassName("bbva-grid-title");
    title.getStyle().set("margin", "0");

    // Botón para abrir el drawer
    btnNuevo.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnNuevo.addClassName("bbva-filters-button");
    btnNuevo.addClickListener(e -> toggleDrawer());
    Tooltip.forComponent(btnNuevo).withText("Crear nuevo banner");

    HorizontalLayout header = new HorizontalLayout(title, btnNuevo);
    header.setWidthFull();
    header.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    header.setAlignItems(FlexComponent.Alignment.CENTER);
    header.addClassName("bbva-header-layout");

    return header;
  }

  private VerticalLayout createDrawerPanel() {
    VerticalLayout drawer = new VerticalLayout();
    drawer.addClassName("drawer-panel");
    drawer.setWidth("500px");
    drawer.setHeight("100%");
    drawer.setPadding(true);
    drawer.setSpacing(true);

    // Estilos CSS para el drawer flotante
    drawer
        .getStyle()
        .set("position", "fixed")
        .set("top", "0")
        .set("right", "-500px") // Inicialmente oculto
        .set("background", "var(--bbva-white)")
        .set("box-shadow", "-4px 0 20px rgba(0, 0, 0, 0.15)")
        .set("z-index", "1000")
        .set("transition", "right 0.3s ease-in-out")
        .set("overflow-y", "auto");

    // Header del drawer
    HorizontalLayout drawerHeader = new HorizontalLayout();
    drawerHeader.setWidthFull();
    drawerHeader.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    drawerHeader.setAlignItems(FlexComponent.Alignment.CENTER);

    H2 drawerTitle = new H2("Formulario de Banner Cross Sell");
    drawerTitle.addClassName("bbva-page-title");
    drawerTitle.getStyle().set("margin", "0");

    btnCerrarDrawer.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
    btnCerrarDrawer.addClickListener(e -> toggleDrawer());
    Tooltip.forComponent(btnCerrarDrawer).withText("Cerrar formulario");

    drawerHeader.add(drawerTitle, btnCerrarDrawer);

    // Contenido del formulario
    VerticalLayout formContent = createFormContent();

    drawer.add(drawerHeader, formContent);
    drawer.setFlexGrow(1, formContent);

    return drawer;
  }

  private VerticalLayout createFormContent() {
    VerticalLayout formContent = new VerticalLayout();
    formContent.setSpacing(true);
    formContent.setPadding(false);

    // Configurar campos del formulario usando clases globales
    cmbFlujo.setLabel("Flujo donde se muestra:");
    cmbFlujo.addClassName("bbva-form-field");
    cmbFlujo.setItems("Login", "Dashboard", "Home", "Transferencias", "Pagos");

    cmbEstado.setLabel("Estado:");
    cmbEstado.addClassName("bbva-form-field");
    cmbEstado.setItems(ACTIVO, INACTIVO);
    cmbEstado.setValue(ACTIVO);

    cmbProducto1.setLabel("Producto 1:");
    cmbProducto1.addClassName("bbva-form-field");
    cmbProducto1.setItems(
        "Tarjeta de Crédito",
        "Préstamo Personal",
        "Cuenta de Ahorros",
        "Depósito a Plazo",
        "Seguro");

    txtImagenDesktop1.setLabel("Imagen Desktop Producto 1:");
    txtImagenDesktop1.addClassName("bbva-form-field");
    txtImagenDesktop1.setPlaceholder("URL de la imagen para desktop");

    txtImagenResponsive1.setLabel("Imagen Responsive Producto 1:");
    txtImagenResponsive1.addClassName("bbva-form-field");
    txtImagenResponsive1.setPlaceholder("URL de la imagen para móvil");

    txtRedireccion1.setLabel("Redirección 1:");
    txtRedireccion1.addClassName("bbva-form-field");
    txtRedireccion1.setPlaceholder("URL de redirección");

    cmbProducto2.setLabel("Producto 2:");
    cmbProducto2.addClassName("bbva-form-field");
    cmbProducto2.setItems(
        "Tarjeta de Crédito",
        "Préstamo Personal",
        "Cuenta de Ahorros",
        "Depósito a Plazo",
        "Seguro");

    txtImagenDesktop2.setLabel("Imagen Desktop Producto 2:");
    txtImagenDesktop2.addClassName("bbva-form-field");
    txtImagenDesktop2.setPlaceholder("URL de la imagen para desktop");

    txtImagenResponsive2.setLabel("Imagen Responsive Producto 2:");
    txtImagenResponsive2.addClassName("bbva-form-field");
    txtImagenResponsive2.setPlaceholder("URL de la imagen para móvil");

    txtRedireccion2.setLabel("Redirección 2:");
    txtRedireccion2.addClassName("bbva-form-field");
    txtRedireccion2.setPlaceholder("URL de redirección");

    cmbProducto3.setLabel("Producto 3:");
    cmbProducto3.addClassName("bbva-form-field");
    cmbProducto3.setItems(
        "Tarjeta de Crédito",
        "Préstamo Personal",
        "Cuenta de Ahorros",
        "Depósito a Plazo",
        "Seguro");

    txtImagenDesktop3.setLabel("Imagen Desktop Producto 3:");
    txtImagenDesktop3.addClassName("bbva-form-field");
    txtImagenDesktop3.setPlaceholder("URL de la imagen para desktop");

    txtImagenResponsive3.setLabel("Imagen Responsive Producto 3:");
    txtImagenResponsive3.addClassName("bbva-form-field");
    txtImagenResponsive3.setPlaceholder("URL de la imagen para móvil");

    txtRedireccion3.setLabel("Redirección 3:");
    txtRedireccion3.addClassName("bbva-form-field");
    txtRedireccion3.setPlaceholder("URL de redirección");

    cmbProducto4.setLabel("Producto 4:");
    cmbProducto4.addClassName("bbva-form-field");
    cmbProducto4.setItems(
        "Tarjeta de Crédito",
        "Préstamo Personal",
        "Cuenta de Ahorros",
        "Depósito a Plazo",
        "Seguro");

    txtImagenDesktop4.setLabel("Imagen Desktop Producto 4:");
    txtImagenDesktop4.addClassName("bbva-form-field");
    txtImagenDesktop4.setPlaceholder("URL de la imagen para desktop");

    txtImagenResponsive4.setLabel("Imagen Responsive Producto 4:");
    txtImagenResponsive4.addClassName("bbva-form-field");
    txtImagenResponsive4.setPlaceholder("URL de la imagen para móvil");

    txtRedireccion4.setLabel("Redirección 4:");
    txtRedireccion4.addClassName("bbva-form-field");
    txtRedireccion4.setPlaceholder("URL de redirección");

    // Configurar botones usando clases globales
    btnGuardar.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnGuardar.addClassName("bbva-button");
    btnGuardar.addClickListener(e -> guardarBanner());

    btnEliminar.addThemeVariants(ButtonVariant.LUMO_ERROR);
    btnEliminar.addClassName("bbva-button");
    btnEliminar.addClickListener(e -> eliminarBanner());
    btnEliminar.setVisible(false); // Solo visible en modo edición

    HorizontalLayout buttonLayout = new HorizontalLayout(btnGuardar, btnEliminar);
    buttonLayout.setSpacing(true);
    buttonLayout.setWidthFull();

    formContent.add(
        cmbFlujo,
        cmbEstado,
        cmbProducto1,
        txtImagenDesktop1,
        txtImagenResponsive1,
        txtRedireccion1,
        cmbProducto2,
        txtImagenDesktop2,
        txtImagenResponsive2,
        txtRedireccion2,
        cmbProducto3,
        txtImagenDesktop3,
        txtImagenResponsive3,
        txtRedireccion3,
        cmbProducto4,
        txtImagenDesktop4,
        txtImagenResponsive4,
        txtRedireccion4,
        buttonLayout);

    return formContent;
  }

  private void toggleDrawer() {
    drawerVisible = !drawerVisible;
    if (drawerVisible) {
      drawerPanel.getStyle().set("right", "0");
      btnNuevo.setText("Cerrar");
      btnNuevo.setIcon(VaadinIcon.CLOSE.create());
    } else {
      drawerPanel.getStyle().set("right", "-500px");
      btnNuevo.setText("Nuevo Banner");
      btnNuevo.setIcon(VaadinIcon.PLUS.create());
      limpiarFormulario();
    }
  }

  private void configureComponents() {
    // Configurar listeners y validaciones adicionales si es necesario
  }

  private VerticalLayout createGridCard() {
    VerticalLayout gridCard = new VerticalLayout();
    gridCard.setSizeFull();
    gridCard.addClassName("bbva-grid-card");
    gridCard.setSpacing(true);
    gridCard.setPadding(true);

    // Configurar grid
    grid.setSizeFull();
    grid.addClassName("bbva-grid");
    grid.addThemeVariants(GridVariant.LUMO_ROW_STRIPES, GridVariant.LUMO_WRAP_CELL_CONTENT);

    // Configurar para que los headers se muestren completos
    grid.setColumnReorderingAllowed(true);
    grid.setMultiSort(true);

    // Configurar columnas con ancho automático basado en el header
    Grid.Column<BannerCrossSellBean> estadoColumn =
        grid.addColumn(
                banner ->
                    banner.getEstado() != null && ACTIVO.equals(banner.getEstado().getValor())
                        ? "Activo"
                        : "Inactivo")
            .setHeader("Estado")
            .setSortable(true)
            .setAutoWidth(true);

    grid.addColumn(banner -> banner.getFlujo() != null ? banner.getFlujo().getValor() : "")
        .setHeader("Flujo donde se muestra")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(
            banner -> banner.getProducto_1() != null ? banner.getProducto_1().getValor() : "")
        .setHeader("Producto 1")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(banner -> truncateText(banner.getImagen_desk_1(), 30))
        .setHeader("Imagen Desktop Producto 1")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0);

    grid.addColumn(banner -> truncateText(banner.getImagen_resp_1(), 30))
        .setHeader("Imagen Responsive Producto 1")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0);

    grid.addColumn(banner -> truncateText(banner.getRedireccion_1(), 30))
        .setHeader("Redirección 1")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0);

    grid.addColumn(
            banner -> banner.getProducto_2() != null ? banner.getProducto_2().getValor() : "")
        .setHeader("Producto 2")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(banner -> truncateText(banner.getImagen_desk_2(), 30))
        .setHeader("Imagen Desktop Producto 2")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0);

    grid.addColumn(banner -> truncateText(banner.getImagen_resp_2(), 30))
        .setHeader("Imagen Responsive Producto 2")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0);

    grid.addColumn(banner -> truncateText(banner.getRedireccion_2(), 30))
        .setHeader("Redirección 2")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0);

    grid.addColumn(
            banner -> banner.getProducto_3() != null ? banner.getProducto_3().getValor() : "")
        .setHeader("Producto 3")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(banner -> truncateText(banner.getImagen_desk_3(), 30))
        .setHeader("Imagen Desktop Producto 3")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0);

    grid.addColumn(banner -> truncateText(banner.getImagen_resp_3(), 30))
        .setHeader("Imagen Responsive Producto 3")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0);

    grid.addColumn(banner -> truncateText(banner.getRedireccion_3(), 30))
        .setHeader("Redirección 3")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0);

    grid.addColumn(
            banner -> banner.getProducto_4() != null ? banner.getProducto_4().getValor() : "")
        .setHeader("Producto 4")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(banner -> truncateText(banner.getImagen_desk_4(), 30))
        .setHeader("Imagen Desktop Producto 4")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0);

    grid.addColumn(banner -> truncateText(banner.getImagen_resp_4(), 30))
        .setHeader("Imagen Responsive Producto 4")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0);

    grid.addColumn(banner -> truncateText(banner.getRedireccion_4(), 30))
        .setHeader("Redirección 4")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0);

    // Columna de acciones
    grid.addComponentColumn(this::createActionsColumn)
        .setHeader("Acciones")
        .setSortable(false)
        .setAutoWidth(true)
        .setFlexGrow(0);

    // Aplicar estilos a las columnas de estado
    estadoColumn.setClassNameGenerator(
        banner -> {
          if (banner.getEstado() != null && ACTIVO.equals(banner.getEstado().getValor())) {
            return "bbva-status-active";
          } else {
            return "bbva-status-inactive";
          }
        });

    // Crear controles de paginación
    HorizontalLayout paginationControls = createPagination();

    gridCard.add(grid, paginationControls);
    gridCard.setFlexGrow(1, grid);

    return gridCard;
  }

  private HorizontalLayout createActionsColumn(BannerCrossSellBean banner) {
    Button editButton = new Button(new Icon(VaadinIcon.EDIT));
    editButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY, ButtonVariant.LUMO_SMALL);
    editButton.addClassName("bbva-action-button");
    editButton.addClickListener(e -> editBanner(banner));
    Tooltip.forComponent(editButton).withText("Editar banner");

    Button deleteButton = new Button(new Icon(VaadinIcon.TRASH));
    deleteButton.addThemeVariants(
        ButtonVariant.LUMO_TERTIARY, ButtonVariant.LUMO_ERROR, ButtonVariant.LUMO_SMALL);
    deleteButton.addClassName("bbva-action-button");
    deleteButton.addClickListener(e -> deleteBanner(banner));
    Tooltip.forComponent(deleteButton).withText("Eliminar banner");

    HorizontalLayout actions = new HorizontalLayout(editButton, deleteButton);
    actions.setSpacing(false);
    actions.setJustifyContentMode(FlexComponent.JustifyContentMode.CENTER);
    actions.addClassName("bbva-actions-layout");
    return actions;
  }

  private HorizontalLayout createPagination() {
    HorizontalLayout paginationLayout = new HorizontalLayout();
    paginationLayout.addClassName("bbva-pagination");
    paginationLayout.setWidthFull();
    paginationLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    paginationLayout.setAlignItems(FlexComponent.Alignment.CENTER);

    // Información de página
    pageInfo = new Span();
    pageInfo.addClassName("bbva-pagination-info");

    // Controles de navegación
    HorizontalLayout controls = new HorizontalLayout();
    controls.addClassName("bbva-pagination-controls");
    controls.setSpacing(true);

    previousButton = new Button("Anterior", VaadinIcon.ANGLE_LEFT.create());
    previousButton.addClassName("bbva-pagination-button");
    previousButton.addClickListener(e -> previousPage());

    nextButton = new Button("Siguiente", VaadinIcon.ANGLE_RIGHT.create());
    nextButton.addClassName("bbva-pagination-button");
    nextButton.addClickListener(e -> nextPage());

    controls.add(previousButton, nextButton);
    paginationLayout.add(pageInfo, controls);

    return paginationLayout;
  }

  private void loadData() {
    // Datos de ejemplo - en producción esto vendría de un servicio
    allBanners.clear();

    // Crear parámetros de ejemplo
    Parametro estadoActivo = new Parametro();
    estadoActivo.setValor(ACTIVO);

    Parametro estadoInactivo = new Parametro();
    estadoInactivo.setValor(INACTIVO);

    Parametro flujoLogin = new Parametro();
    flujoLogin.setValor("Login");

    Parametro flujoDashboard = new Parametro();
    flujoDashboard.setValor("Dashboard");

    Parametro productoTC = new Parametro();
    productoTC.setValor("Tarjeta de Crédito");

    Parametro productoPP = new Parametro();
    productoPP.setValor("Préstamo Personal");

    Parametro productoCA = new Parametro();
    productoCA.setValor("Cuenta de Ahorros");

    // Ejemplo de datos
    BannerCrossSellBean banner1 = new BannerCrossSellBean();
    banner1.setId(1L);
    banner1.setEstado(estadoActivo);
    banner1.setFlujo(flujoLogin);
    banner1.setProducto_1(productoTC);
    banner1.setImagen_desk_1("/images/tc-desktop.jpg");
    banner1.setImagen_resp_1("/images/tc-mobile.jpg");
    banner1.setRedireccion_1("https://bbva.pe/tarjetas");
    banner1.setProducto_2(productoPP);
    banner1.setImagen_desk_2("/images/pp-desktop.jpg");
    banner1.setImagen_resp_2("/images/pp-mobile.jpg");
    banner1.setRedireccion_2("https://bbva.pe/prestamos");

    BannerCrossSellBean banner2 = new BannerCrossSellBean();
    banner2.setId(2L);
    banner2.setEstado(estadoInactivo);
    banner2.setFlujo(flujoDashboard);
    banner2.setProducto_1(productoCA);
    banner2.setImagen_desk_1("/images/ca-desktop.jpg");
    banner2.setImagen_resp_1("/images/ca-mobile.jpg");
    banner2.setRedireccion_1("https://bbva.pe/cuentas");

    allBanners.add(banner1);
    allBanners.add(banner2);
  }

  private void applyFilters() {
    List<BannerCrossSellBean> filteredBanners =
        allBanners.stream()
            .filter(
                banner -> {
                  // Filtro por estado
                  if (!"Todos".equals(estadoFilter.getValue())) {
                    String expectedState =
                        "Activo".equals(estadoFilter.getValue()) ? ACTIVO : INACTIVO;
                    if (banner.getEstado() == null
                        || !expectedState.equals(banner.getEstado().getValor())) {
                      return false;
                    }
                  }

                  // Filtro por flujo
                  if (flujoFilter.getValue() != null && !flujoFilter.getValue().isEmpty()) {
                    if (banner.getFlujo() == null
                        || banner.getFlujo().getValor() == null
                        || !banner
                            .getFlujo()
                            .getValor()
                            .toLowerCase()
                            .contains(flujoFilter.getValue().toLowerCase())) {
                      return false;
                    }
                  }

                  // Filtros por productos
                  if (producto1Filter.getValue() != null && !producto1Filter.getValue().isEmpty()) {
                    if (banner.getProducto_1() == null
                        || banner.getProducto_1().getValor() == null
                        || !banner
                            .getProducto_1()
                            .getValor()
                            .toLowerCase()
                            .contains(producto1Filter.getValue().toLowerCase())) {
                      return false;
                    }
                  }

                  if (producto2Filter.getValue() != null && !producto2Filter.getValue().isEmpty()) {
                    if (banner.getProducto_2() == null
                        || banner.getProducto_2().getValor() == null
                        || !banner
                            .getProducto_2()
                            .getValor()
                            .toLowerCase()
                            .contains(producto2Filter.getValue().toLowerCase())) {
                      return false;
                    }
                  }

                  if (producto3Filter.getValue() != null && !producto3Filter.getValue().isEmpty()) {
                    if (banner.getProducto_3() == null
                        || banner.getProducto_3().getValor() == null
                        || !banner
                            .getProducto_3()
                            .getValor()
                            .toLowerCase()
                            .contains(producto3Filter.getValue().toLowerCase())) {
                      return false;
                    }
                  }

                  if (producto4Filter.getValue() != null && !producto4Filter.getValue().isEmpty()) {
                    if (banner.getProducto_4() == null
                        || banner.getProducto_4().getValor() == null
                        || !banner
                            .getProducto_4()
                            .getValor()
                            .toLowerCase()
                            .contains(producto4Filter.getValue().toLowerCase())) {
                      return false;
                    }
                  }

                  return true;
                })
            .collect(Collectors.toList());

    bannersList.clear();
    bannersList.addAll(filteredBanners);
    currentPage = 0;
    updateGrid();
  }

  private void clearFilters() {
    estadoFilter.setValue("Todos");
    flujoFilter.clear();
    producto1Filter.clear();
    producto2Filter.clear();
    producto3Filter.clear();
    producto4Filter.clear();
    applyFilters();
  }

  private void updateGrid() {
    totalPages = (int) Math.ceil((double) bannersList.size() / pageSize);

    int start = currentPage * pageSize;
    int end = Math.min(start + pageSize, bannersList.size());

    List<BannerCrossSellBean> pageData = bannersList.subList(start, end);
    grid.setItems(pageData);

    updatePaginationControls();
  }

  private void updatePaginationControls() {
    previousButton.setEnabled(currentPage > 0);
    nextButton.setEnabled(currentPage < totalPages - 1);

    if (totalPages > 0) {
      pageInfo.setText(
          String.format(
              "Página %d de %d (%d registros)", currentPage + 1, totalPages, bannersList.size()));
    } else {
      pageInfo.setText("No hay registros");
    }
  }

  private void previousPage() {
    if (currentPage > 0) {
      currentPage--;
      updateGrid();
    }
  }

  private void nextPage() {
    if (currentPage < totalPages - 1) {
      currentPage++;
      updateGrid();
    }
  }

  private String truncateText(String text, int maxLength) {
    if (text == null) return "";
    return text.length() > maxLength ? text.substring(0, maxLength) + "..." : text;
  }

  private void guardarBanner() {
    if (validarCampos()) {
      if (bannerEnEdicion == null) {
        // Crear nuevo banner
        BannerCrossSellBean nuevoBanner = new BannerCrossSellBean();
        mapearFormularioABean(nuevoBanner);
        nuevoBanner.setId((long) (allBanners.size() + 1));

        allBanners.add(nuevoBanner);

        Notification.show("Banner creado exitosamente")
            .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
      } else {
        // Actualizar banner existente
        mapearFormularioABean(bannerEnEdicion);

        Notification.show("Banner actualizado exitosamente")
            .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
      }

      applyFilters();
      toggleDrawer();
    }
  }

  private void eliminarBanner() {
    if (bannerEnEdicion != null) {
      allBanners.remove(bannerEnEdicion);
      applyFilters();
      toggleDrawer();

      Notification.show("Banner eliminado exitosamente")
          .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
    }
  }

  private void editBanner(BannerCrossSellBean banner) {
    bannerEnEdicion = banner;
    cargarBannerEnFormulario(banner);

    if (!drawerVisible) {
      toggleDrawer();
    }

    btnEliminar.setVisible(true);
    btnGuardar.setText("Actualizar");
  }

  private void deleteBanner(BannerCrossSellBean banner) {
    bannerEnEdicion = banner;
    eliminarBanner();
  }

  private boolean validarCampos() {
    if (cmbFlujo.getValue() == null || cmbFlujo.getValue().trim().isEmpty()) {
      Notification.show("El flujo es requerido").addThemeVariants(NotificationVariant.LUMO_ERROR);
      return false;
    }

    if (cmbEstado.getValue() == null) {
      Notification.show("El estado es requerido").addThemeVariants(NotificationVariant.LUMO_ERROR);
      return false;
    }

    return true;
  }

  private void mapearFormularioABean(BannerCrossSellBean banner) {
    // Crear parámetros para los campos
    if (cmbFlujo.getValue() != null) {
      Parametro flujo = new Parametro();
      flujo.setValor(cmbFlujo.getValue());
      banner.setFlujo(flujo);
    }

    if (cmbEstado.getValue() != null) {
      Parametro estado = new Parametro();
      estado.setValor(cmbEstado.getValue());
      banner.setEstado(estado);
    }

    if (cmbProducto1.getValue() != null) {
      Parametro producto1 = new Parametro();
      producto1.setValor(cmbProducto1.getValue());
      banner.setProducto_1(producto1);
    }

    banner.setImagen_desk_1(txtImagenDesktop1.getValue());
    banner.setImagen_resp_1(txtImagenResponsive1.getValue());
    banner.setRedireccion_1(txtRedireccion1.getValue());

    if (cmbProducto2.getValue() != null) {
      Parametro producto2 = new Parametro();
      producto2.setValor(cmbProducto2.getValue());
      banner.setProducto_2(producto2);
    }

    banner.setImagen_desk_2(txtImagenDesktop2.getValue());
    banner.setImagen_resp_2(txtImagenResponsive2.getValue());
    banner.setRedireccion_2(txtRedireccion2.getValue());

    if (cmbProducto3.getValue() != null) {
      Parametro producto3 = new Parametro();
      producto3.setValor(cmbProducto3.getValue());
      banner.setProducto_3(producto3);
    }

    banner.setImagen_desk_3(txtImagenDesktop3.getValue());
    banner.setImagen_resp_3(txtImagenResponsive3.getValue());
    banner.setRedireccion_3(txtRedireccion3.getValue());

    if (cmbProducto4.getValue() != null) {
      Parametro producto4 = new Parametro();
      producto4.setValor(cmbProducto4.getValue());
      banner.setProducto_4(producto4);
    }

    banner.setImagen_desk_4(txtImagenDesktop4.getValue());
    banner.setImagen_resp_4(txtImagenResponsive4.getValue());
    banner.setRedireccion_4(txtRedireccion4.getValue());
  }

  private void cargarBannerEnFormulario(BannerCrossSellBean banner) {
    cmbFlujo.setValue(banner.getFlujo() != null ? banner.getFlujo().getValor() : "");
    cmbEstado.setValue(banner.getEstado() != null ? banner.getEstado().getValor() : ACTIVO);
    cmbProducto1.setValue(banner.getProducto_1() != null ? banner.getProducto_1().getValor() : "");
    txtImagenDesktop1.setValue(banner.getImagen_desk_1() != null ? banner.getImagen_desk_1() : "");
    txtImagenResponsive1.setValue(
        banner.getImagen_resp_1() != null ? banner.getImagen_resp_1() : "");
    txtRedireccion1.setValue(banner.getRedireccion_1() != null ? banner.getRedireccion_1() : "");

    cmbProducto2.setValue(banner.getProducto_2() != null ? banner.getProducto_2().getValor() : "");
    txtImagenDesktop2.setValue(banner.getImagen_desk_2() != null ? banner.getImagen_desk_2() : "");
    txtImagenResponsive2.setValue(
        banner.getImagen_resp_2() != null ? banner.getImagen_resp_2() : "");
    txtRedireccion2.setValue(banner.getRedireccion_2() != null ? banner.getRedireccion_2() : "");

    cmbProducto3.setValue(banner.getProducto_3() != null ? banner.getProducto_3().getValor() : "");
    txtImagenDesktop3.setValue(banner.getImagen_desk_3() != null ? banner.getImagen_desk_3() : "");
    txtImagenResponsive3.setValue(
        banner.getImagen_resp_3() != null ? banner.getImagen_resp_3() : "");
    txtRedireccion3.setValue(banner.getRedireccion_3() != null ? banner.getRedireccion_3() : "");

    cmbProducto4.setValue(banner.getProducto_4() != null ? banner.getProducto_4().getValor() : "");
    txtImagenDesktop4.setValue(banner.getImagen_desk_4() != null ? banner.getImagen_desk_4() : "");
    txtImagenResponsive4.setValue(
        banner.getImagen_resp_4() != null ? banner.getImagen_resp_4() : "");
    txtRedireccion4.setValue(banner.getRedireccion_4() != null ? banner.getRedireccion_4() : "");
  }

  private void limpiarFormulario() {
    bannerEnEdicion = null;
    cmbFlujo.clear();
    cmbEstado.setValue(ACTIVO);
    cmbProducto1.clear();
    txtImagenDesktop1.clear();
    txtImagenResponsive1.clear();
    txtRedireccion1.clear();
    cmbProducto2.clear();
    txtImagenDesktop2.clear();
    txtImagenResponsive2.clear();
    txtRedireccion2.clear();
    cmbProducto3.clear();
    txtImagenDesktop3.clear();
    txtImagenResponsive3.clear();
    txtRedireccion3.clear();
    cmbProducto4.clear();
    txtImagenDesktop4.clear();
    txtImagenResponsive4.clear();
    txtRedireccion4.clear();

    btnEliminar.setVisible(false);
    btnGuardar.setText("Guardar");
  }
}
