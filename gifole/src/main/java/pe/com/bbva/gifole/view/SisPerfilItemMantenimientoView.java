package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import pe.com.bbva.gifole.domain.SisPerfilItem;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Mantenimiento de Perfil Items")
@Route(value = "sis-perfil-item-mantenimiento", layout = MainLayout.class)
public class SisPerfilItemMantenimientoView extends VerticalLayout {

  public SisPerfilItemMantenimientoView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Mantenimiento de Perfil Items");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    DataTable<SisPerfilItem> dataTable =
        DataTable.<SisPerfilItem>builder()
            .id("tabla-asignaciones")
            .column(
                "estado",
                "Estado",
                asignacion -> {
                  String estado = asignacion.getEstado();
                  return estado != null ? estado : "Desconocido";
                },
                "100px")
            .column(
                "perfil",
                "Perfil",
                asignacion ->
                    asignacion.getSisPerfil() != null
                        ? asignacion.getSisPerfil().getDescripcion()
                        : "Sin perfil",
                "200px")
            .column(
                "item",
                "Item",
                asignacion ->
                    asignacion.getSisItem() != null
                        ? asignacion.getSisItem().getDescripcion()
                        : "Sin item",
                "200px")
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
