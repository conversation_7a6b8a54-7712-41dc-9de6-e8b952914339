package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.TCReporteReferidosBean; // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Referidos")
@Route(value = "reporte/referidos/consulta", layout = MainLayout.class)
public class ConsultaReferidosView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<TCReporteReferidosBean> allData = new ArrayList<>();
    private DataTable<TCReporteReferidosBean> dataTable;

    public ConsultaReferidosView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Referidos");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<TCReporteReferidosBean>builder()
                .id("tabla-referidos") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaReferidosUI)
                // Seleccionadas según las columnas del DTO proporcionado y el análisis del código de la UI antigua
                // Se enfoca en un subconjunto representativo de las muchas columnas disponibles
                .column("tipoDocumentoReferidor", "Tipo Doc. Referidor",
                    bean -> StringUtils.trimToEmpty(bean.getTipoDocumentoReferidor()), "80px")
                .column("numeroDocumentoReferidor", "Num. Doc. Referidor",
                    bean -> StringUtils.trimToEmpty(bean.getNumeroDocumentoReferidor()), "100px")
                .column("nombreCompletoReferidor", "Cliente Referidor", // Mapeo del DTO
                    bean -> StringUtils.trimToEmpty(bean.getNombreCompletoReferidor()), "200px")
                .column("tipoDocumentoReferido", "Tipo Doc. Referido",
                    bean -> StringUtils.trimToEmpty(bean.getTipoDocumentoReferido()), "80px")
                .column("numeroDocumentoReferido", "Num. Doc. Referido",
                    bean -> StringUtils.trimToEmpty(bean.getNumeroDocumentoReferido()), "100px")
                .column("nombreCompletoReferido", "Cliente Referido", // Mapeo del DTO
                    bean -> StringUtils.trimToEmpty(bean.getNombreCompletoReferido()), "200px")
                .column("fechaRegistro", "Fecha Registro", // Mapeo del DTO (fechaEstado en UI)
                    bean -> bean.getFechaRegistro() != null ? FORMATO_FECHA.format(bean.getFechaRegistro()) : "", "150px")
                .column("estadoReferido", "Estado", // Mapeo del DTO
                    bean -> StringUtils.trimToEmpty(bean.getEstadoReferido()), "120px")
                .column("tipoTarjetaReferido", "Tipo Tarjeta Referido",
                    bean -> StringUtils.trimToEmpty(bean.getTipoTarjetaReferido()), "150px")
                .column("nbrTarjetaReferido", "Nro. Tarjeta Referido",
                    bean -> StringUtils.trimToEmpty(bean.getNbrTarjetaReferido()), "170px")
                .column("multibinReferido", "Multibin",
                    bean -> StringUtils.trimToEmpty(bean.getMultibinReferido()), "100px")
                .column("lineaReferido", "Línea Referido",
                    bean -> StringUtils.trimToEmpty(bean.getLineaReferido()), "120px")
                .column("tasaReferido", "Tasa Referido",
                    bean -> StringUtils.trimToEmpty(String.valueOf(bean.getTasaReferido())), "100px")
                .column("campaniaReferido", "Campaña Referido",
                    bean -> StringUtils.trimToEmpty(bean.getCampaniaReferido()), "150px")
                .column("observacion", "Observación", // Mapeo de historicoObservacion
                    bean -> StringUtils.trimToEmpty(bean.getHistoricoObservacion()), "200px")
                .column("nroContratoTablon", "Nro. Contrato Táblon",
                    bean -> StringUtils.trimToEmpty(bean.getNroContratoTablon()), "170px")
                .column("nroTarjetaTablon", "Nro. Tarjeta Táblon",
                    bean -> StringUtils.trimToEmpty(bean.getNroTarjetaTablon()), "170px")
                .column("fechaContratacionTablon", "Fecha Contratación Táblon",
                    bean -> bean.getFechaContratacionTablon() != null ? FORMATO_FECHA.format(bean.getFechaContratacionTablon()) : "", "180px")
                .column("fechaActivacionTablon", "Fecha Activación Táblon",
                    bean -> bean.getFechaActivacionTablon() != null ? FORMATO_FECHA.format(bean.getFechaActivacionTablon()) : "", "180px")
                .column("binTablon", "Bin Táblon",
                    bean -> StringUtils.trimToEmpty(bean.getBinTablon()), "100px")
                .column("marcaEmpleadoReferidor", "Marca Empleado Referidor",
                    bean -> StringUtils.trimToEmpty(bean.getMarcaEmpleadoReferidor()), "180px")
                .column("campaniaReferidor", "Campaña Referidor",
                    bean -> StringUtils.trimToEmpty(bean.getCampaniaReferidor()), "150px")
                .column("celularReferido", "Celular Referido",
                    bean -> StringUtils.trimToEmpty(bean.getCelularReferido()), "120px")
                .column("correoReferido", "Correo Referido",
                    bean -> StringUtils.trimToEmpty(bean.getCorreoReferido()), "200px")
                .column("historicoEvaluacion", "Histórico Evaluación",
                    bean -> StringUtils.trimToEmpty(bean.getHistoricoEvaluacion()), "180px")
                .column("pasoMotor", "Paso Motor",
                    bean -> StringUtils.trimToEmpty(bean.getPasoMotor()), "120px")
                .column("fechaEvaluacion", "Fecha Evaluación",
                    bean -> bean.getFechaEvaluacion() != null ? FORMATO_FECHA.format(bean.getFechaEvaluacion()) : "", "150px")
                // Puedes agregar más columnas aquí si las necesitas, siguiendo el mismo patrón
                // Datos (inicialmente vacío)
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}