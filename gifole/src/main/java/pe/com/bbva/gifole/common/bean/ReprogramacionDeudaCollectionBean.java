package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import pe.com.bbva.gifole.util.Constante;

public class ReprogramacionDeudaCollectionBean implements Serializable {

  private static final long serialVersionUID = 4925893573127851110L;

  private List<ReprogramacionDeudaBean> listaReprogramacionDeudaBMTarjetas;
  private List<ReprogramacionDeudaBean> listaReprogramacionDeudaBITarjetas;
  private List<ReprogramacionDeudaBean> listaReprogramacionDeudaBMPrestamos;
  private List<ReprogramacionDeudaBean> listaReprogramacionDeudaBIPrestamos;
  private List<ReprogramacionDeudaBean> listaReprogramacionDeudaIVRTarjetas;
  private List<ReprogramacionDeudaBean> listaReprogramacionDeudaIVRPrestamos;

  public List<ReprogramacionDeudaBean> getListaReprogramacionDeudaBMTarjetas() {
    return listaReprogramacionDeudaBMTarjetas;
  }

  public void setListaReprogramacionDeudaBMTarjetas(
      List<ReprogramacionDeudaBean> listaReprogramacionDeudaBMTarjetas) {
    this.listaReprogramacionDeudaBMTarjetas = listaReprogramacionDeudaBMTarjetas;
  }

  public List<ReprogramacionDeudaBean> getListaReprogramacionDeudaBITarjetas() {
    return listaReprogramacionDeudaBITarjetas;
  }

  public void setListaReprogramacionDeudaBITarjetas(
      List<ReprogramacionDeudaBean> listaReprogramacionDeudaBITarjetas) {
    this.listaReprogramacionDeudaBITarjetas = listaReprogramacionDeudaBITarjetas;
  }

  public List<ReprogramacionDeudaBean> getListaReprogramacionDeudaBMPrestamos() {
    return listaReprogramacionDeudaBMPrestamos;
  }

  public void setListaReprogramacionDeudaBMPrestamos(
      List<ReprogramacionDeudaBean> listaReprogramacionDeudaBMPrestamos) {
    this.listaReprogramacionDeudaBMPrestamos = listaReprogramacionDeudaBMPrestamos;
  }

  public List<ReprogramacionDeudaBean> getListaReprogramacionDeudaBIPrestamos() {
    return listaReprogramacionDeudaBIPrestamos;
  }

  public void setListaReprogramacionDeudaBIPrestamos(
      List<ReprogramacionDeudaBean> listaReprogramacionDeudaBIPrestamos) {
    this.listaReprogramacionDeudaBIPrestamos = listaReprogramacionDeudaBIPrestamos;
  }

  public List<ReprogramacionDeudaBean> getListaReprogramacionDeudaIVRTarjetas() {
    return listaReprogramacionDeudaIVRTarjetas;
  }

  public void setListaReprogramacionDeudaIVRTarjetas(
      List<ReprogramacionDeudaBean> listaReprogramacionDeudaIVRTarjetas) {
    this.listaReprogramacionDeudaIVRTarjetas = listaReprogramacionDeudaIVRTarjetas;
  }

  public List<ReprogramacionDeudaBean> getListaReprogramacionDeudaIVRPrestamos() {
    return listaReprogramacionDeudaIVRPrestamos;
  }

  public void setListaReprogramacionDeudaIVRPrestamos(
      List<ReprogramacionDeudaBean> listaReprogramacionDeudaIVRPrestamos) {
    this.listaReprogramacionDeudaIVRPrestamos = listaReprogramacionDeudaIVRPrestamos;
  }

  public void cargarListas(List<ReprogramacionDeudaBean> listaReprogramacionDeuda) {

    listaReprogramacionDeudaBMTarjetas = new ArrayList<>();
    listaReprogramacionDeudaBITarjetas = new ArrayList<>();
    listaReprogramacionDeudaIVRTarjetas = new ArrayList<>();
    listaReprogramacionDeudaBMPrestamos = new ArrayList<>();
    listaReprogramacionDeudaBIPrestamos = new ArrayList<>();
    listaReprogramacionDeudaIVRPrestamos = new ArrayList<>();

    for (ReprogramacionDeudaBean obj : listaReprogramacionDeuda) {

      if (obj.getDescripcionProducto().equalsIgnoreCase("TARJETA")
          && obj.getCanal().equalsIgnoreCase("BMOVIL")) {
        listaReprogramacionDeudaBMTarjetas.add(obj);
      } else if (obj.getDescripcionProducto().equalsIgnoreCase("TARJETA")
          && obj.getCanal().equalsIgnoreCase("BNET")) {
        listaReprogramacionDeudaBITarjetas.add(obj);
      } else if (obj.getDescripcionProducto().equalsIgnoreCase("TARJETA")
          && obj.getCanal().equalsIgnoreCase(Constante.CANAL.IVR)) {
        listaReprogramacionDeudaIVRTarjetas.add(obj);
      } else if (obj.getDescripcionProducto().equalsIgnoreCase("PRESTAMO")
          && obj.getCanal().equalsIgnoreCase("BMOVIL")) {
        listaReprogramacionDeudaBMPrestamos.add(obj);
      } else if (obj.getDescripcionProducto().equalsIgnoreCase("PRESTAMO")
          && obj.getCanal().equalsIgnoreCase("BNET")) {
        listaReprogramacionDeudaBIPrestamos.add(obj);
      } else if (obj.getDescripcionProducto().equalsIgnoreCase("PRESTAMO")
          && obj.getCanal().equalsIgnoreCase(Constante.CANAL.IVR)) {
        listaReprogramacionDeudaIVRPrestamos.add(obj);
      }
    }
  }
}
