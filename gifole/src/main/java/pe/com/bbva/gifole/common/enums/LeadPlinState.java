package pe.com.bbva.gifole.common.enums;

public enum LeadPlinState {
  PENDIENTE(1, "PENDIENTE"),
  EN_REVISION(2, "EN_REVISION"),
  APROBADO(3, "APROBADO"),
  RECHA<PERSON>AD<PERSON>(4, "RECHAZADO"),
  ;

  private int codigo;
  private String descripcion;

  private LeadPlinState(int codigo, String descripcion) {
    this.codigo = codigo;
    this.descripcion = descripcion;
  }

  public int getCodigo() {
    return codigo;
  }

  public String getDescripcion() {
    return descripcion;
  }
}
