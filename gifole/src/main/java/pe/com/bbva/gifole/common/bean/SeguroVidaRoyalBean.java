package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class SeguroVidaRoyalBean implements Serializable {
  private Long id;
  private String fechaRegistro;
  private String nombre;
  private String apellido;
  private String tipoDocumento;
  private String documento;
  private String correo;
  private String telefono;
  private String codigoOficina;
  private String codigoUsuario;
  private String horarioContacto;
  private String canal;
  private String indicadorSiEsCliente;
  private String indicadorProcesado;
  private String nroContrato;
  private Date fechaRegistroDesde;
  private Date fechaRegistroHasta;

  /**
   * @return Long return the id
   */
  public Long getId() {
    return id;
  }

  /**
   * @param id the id to set
   */
  public void setId(Long id) {
    this.id = id;
  }

  /**
   * @return String return the fechaRegistro
   */
  public String getFechaRegistro() {
    return fechaRegistro;
  }

  /**
   * @param fechaRegistro the fechaRegistro to set
   */
  public void setFechaRegistro(String fechaRegistro) {
    this.fechaRegistro = fechaRegistro;
  }

  /**
   * @return String return the nombre
   */
  public String getNombre() {
    return nombre;
  }

  /**
   * @param nombre the nombre to set
   */
  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  /**
   * @return String return the apellido
   */
  public String getApellido() {
    return apellido;
  }

  /**
   * @param apellido the apellido to set
   */
  public void setApellido(String apellido) {
    this.apellido = apellido;
  }

  /**
   * @return String return the tipoDocumento
   */
  public String getTipoDocumento() {
    return tipoDocumento;
  }

  /**
   * @param tipoDocumento the tipoDocumento to set
   */
  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  /**
   * @return String return the documento
   */
  public String getDocumento() {
    return documento;
  }

  /**
   * @param documento the documento to set
   */
  public void setDocumento(String documento) {
    this.documento = documento;
  }

  /**
   * @return String return the correo
   */
  public String getCorreo() {
    return correo;
  }

  /**
   * @param correo the correo to set
   */
  public void setCorreo(String correo) {
    this.correo = correo;
  }

  /**
   * @return String return the telefono
   */
  public String getTelefono() {
    return telefono;
  }

  /**
   * @param telefono the telefono to set
   */
  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  /**
   * @return String return the codigoOficina
   */
  public String getCodigoOficina() {
    return codigoOficina;
  }

  /**
   * @param codigoOficina the codigoOficina to set
   */
  public void setCodigoOficina(String codigoOficina) {
    this.codigoOficina = codigoOficina;
  }

  /**
   * @return String return the codigoUsuario
   */
  public String getCodigoUsuario() {
    return codigoUsuario;
  }

  /**
   * @param codigoUsuario the codigoUsuario to set
   */
  public void setCodigoUsuario(String codigoUsuario) {
    this.codigoUsuario = codigoUsuario;
  }

  /**
   * @return String return the horarioContacto
   */
  public String getHorarioContacto() {
    return horarioContacto;
  }

  /**
   * @param horarioContacto the horarioContacto to set
   */
  public void setHorarioContacto(String horarioContacto) {
    this.horarioContacto = horarioContacto;
  }

  /**
   * @return String return the canal
   */
  public String getCanal() {
    return canal;
  }

  /**
   * @param canal the canal to set
   */
  public void setCanal(String canal) {
    this.canal = canal;
  }

  /**
   * @return String return the indicadorSiEsCliente
   */
  public String getIndicadorSiEsCliente() {
    return indicadorSiEsCliente;
  }

  /**
   * @param indicadorSiEsCliente the indicadorSiEsCliente to set
   */
  public void setIndicadorSiEsCliente(String indicadorSiEsCliente) {
    this.indicadorSiEsCliente = indicadorSiEsCliente;
  }

  /**
   * @return String return the indicadorProcesado
   */
  public String getIndicadorProcesado() {
    return indicadorProcesado;
  }

  /**
   * @param indicadorProcesado the indicadorProcesado to set
   */
  public void setIndicadorProcesado(String indicadorProcesado) {
    this.indicadorProcesado = indicadorProcesado;
  }

  /**
   * @return String return the nroContrato
   */
  public String getNroContrato() {
    return nroContrato;
  }

  /**
   * @param nroContrato the nroContrato to set
   */
  public void setNroContrato(String nroContrato) {
    this.nroContrato = nroContrato;
  }

  /**
   * @return Date return the fechaRegistroDesde
   */
  public Date getFechaRegistroDesde() {
    return fechaRegistroDesde;
  }

  /**
   * @param fechaRegistroDesde the fechaRegistroDesde to set
   */
  public void setFechaRegistroDesde(Date fechaRegistroDesde) {
    this.fechaRegistroDesde = fechaRegistroDesde;
  }

  /**
   * @return Date return the fechaRegistroHasta
   */
  public Date getFechaRegistroHasta() {
    return fechaRegistroHasta;
  }

  /**
   * @param fechaRegistroHasta the fechaRegistroHasta to set
   */
  public void setFechaRegistroHasta(Date fechaRegistroHasta) {
    this.fechaRegistroHasta = fechaRegistroHasta;
  }
}
