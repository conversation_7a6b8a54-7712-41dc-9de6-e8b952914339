package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.ReprogramacionDeudaBean;

@Component
public class ReprogramacionDeudasRowMapper implements RowMapper<ReprogramacionDeudaBean> {

  @Override
  public ReprogramacionDeudaBean mapRow(ResultSet rs, int row) throws SQLException {
    ReprogramacionDeudaBean reprogramacionDeuda = new ReprogramacionDeudaBean();
    reprogramacionDeuda.setId(Long.valueOf(rs.getString("ID")));
    reprogramacionDeuda.setCodigoCentral(StringUtils.trimToEmpty(rs.getString("COD_CENTRAL")));
    reprogramacionDeuda.setTipoDocumento(StringUtils.trimToEmpty(rs.getString("TIPO_DOCUMENTO")));
    reprogramacionDeuda.setNumeroDocumento(StringUtils.trimToEmpty(rs.getString("NUM_DOCUMENTO")));
    reprogramacionDeuda.setNombresApellidos(
        StringUtils.trimToEmpty(rs.getString("NOMBRES_APELLIDOS")));
    reprogramacionDeuda.setCelular(StringUtils.trimToEmpty(rs.getString("CELULAR")));
    reprogramacionDeuda.setCorreo(StringUtils.trimToEmpty(rs.getString("CORREO")));
    reprogramacionDeuda.setNumeroContrato(StringUtils.trimToEmpty(rs.getString("NUM_CONTRATO")));
    reprogramacionDeuda.setDescripcionProducto(
        StringUtils.trimToEmpty(rs.getString("DES_PRODUCTO")));
    reprogramacionDeuda.setNroMesReprogramacion(
        StringUtils.trimToEmpty(rs.getString("NUM_MES_REPROGRAMACION")));
    reprogramacionDeuda.setNroCoutasProrroga(
        StringUtils.trimToEmpty(rs.getString("NUM_CUOTAS_PRORROGA")));
    reprogramacionDeuda.setCanal(StringUtils.trimToEmpty(rs.getString("CANAL")));
    reprogramacionDeuda.setFechaRegistro(StringUtils.trimToEmpty(rs.getString("FECHA_REGISTRO")));
    return reprogramacionDeuda;
  }
}
