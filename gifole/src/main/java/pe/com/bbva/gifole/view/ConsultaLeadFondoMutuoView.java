package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

import pe.com.bbva.gifole.common.bean.FondoMutuoBean;
import pe.com.bbva.gifole.util.GifoleUtil;
import pe.com.bbva.gifole.util.Util;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Lead Fondo Mutuo")
@Route(value = "reporte/leads/fondo-mutuo", layout = MainLayout.class)
public class ConsultaLeadFondoMutuoView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<FondoMutuoBean> allData = new ArrayList<>();
    private DataTable<FondoMutuoBean> dataTable;

    public ConsultaLeadFondoMutuoView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Lead Fondo Mutuo");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<FondoMutuoBean>builder()
                .id("tabla-lead-fondo-mutuo") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaLeadFondoMutuoUI)
                .column("codigoCentral", "Código Central", 
                    bean -> StringUtils.trimToEmpty(bean.getCodigoCentral()), "100px")
                .column("nombres", "Nombres", 
                    bean -> StringUtils.trimToEmpty(bean.getNombres()), "200px")
                .column("tipoEnvioEECC", "Tipo Envío EECC", 
                    bean -> StringUtils.trimToEmpty(bean.getTipoEnvioEECC()).equals("CE") ? "CORREO" : 
                           StringUtils.trimToEmpty(bean.getTipoEnvioEECC()).equals("DF") ? "DIRECCIÓN" : "", "120px")
                .column("descEnvioEECC", "Desc. Envío EECC", 
                    bean -> StringUtils.trimToEmpty(bean.getDescEnvioEECC()).replace(",", " "), "200px")
                .column("indLpdp", "LPDP", 
                    bean -> StringUtils.trimToEmpty(bean.getIndLpdp()).equals("1") ? "SI" :
                           StringUtils.trimToEmpty(bean.getIndLpdp()).equals("0") ? "NO" : "", "80px")
                .column("idContrato", "ID Contrato", 
                    bean -> bean.getFondoMutuoDetalle() != null ? 
                           StringUtils.trimToEmpty(bean.getFondoMutuoDetalle().getIdContratoFondo()) : "", "120px")
                .column("idClaseFondo", "Clase Fondo", 
                    bean -> bean.getFondoMutuoDetalle() != null ? 
                           StringUtils.trimToEmpty(bean.getFondoMutuoDetalle().getIdClaseFondo()) : "", "120px")
                .column("nroCuenta", "Nro. Cuenta", 
                    bean -> bean.getFondoMutuoDetalle() != null ? 
                           GifoleUtil.ofuscarCuenta(StringUtils.trimToEmpty(bean.getFondoMutuoDetalle().getNroCuenta()), 4, 4) : "", "150px")
                .column("tipoFondo", "Tipo Fondo", 
                    bean -> bean.getFondoMutuoDetalle() != null ? 
                           bean.getFondoMutuoDetalle().getTipoFondo() : "", "120px")
                .column("fondo", "Fondo", 
                    bean -> bean.getFondoMutuoDetalle() != null ? 
                           bean.getFondoMutuoDetalle().getFondo() : "", "150px")
                .column("monto", "Monto", 
                    bean -> bean.getFondoMutuoDetalle() != null ? 
                           bean.getFondoMutuoDetalle().getMonto() : "", "100px")
                .column("divisa", "Divisa", 
                    bean -> bean.getFondoMutuoDetalle() != null ? 
                           bean.getFondoMutuoDetalle().getTipoDivisa() : "", "100px")
                .column("estado", "Estado", 
                    bean -> bean.getEstado(), "100px")
                .column("fechaCreacion", "Fecha Creación", 
                    bean -> Util.convertirFechaHoraAString(bean.getFechaCreacion()), "150px")
                .column("canal", "Canal", 
                    bean -> bean.getCanal(), "120px")
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}