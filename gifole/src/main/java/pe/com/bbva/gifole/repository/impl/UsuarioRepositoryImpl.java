package pe.com.bbva.gifole.repository.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaQuery;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Repository;
import pe.com.bbva.gifole.domain.Usuario;
import pe.com.bbva.gifole.repository.UsuarioRepository;

@Repository
public class UsuarioRepositoryImpl implements UsuarioRepository {

  private static final Logger logger = LogManager.getLogger(UsuarioRepositoryImpl.class);

  @PersistenceContext private EntityManager entityManager;

  @Override
  public Usuario obtenerUsuario(CriteriaQuery criteriaQuery) {
    TypedQuery<Usuario> query = entityManager.createQuery(criteriaQuery);
    query.setMaxResults(1);
    List<Usuario> usuarios = query.getResultList();
    if (!usuarios.isEmpty()) {
      return usuarios.get(0);
    }
    return null;
  }

  @Override
  public void actualizar(Usuario usuario) {
    entityManager.merge(usuario);
  }

  @Override
  public List<Usuario> buscarUsuario(CriteriaQuery criteriaQuery) {
    TypedQuery<Usuario> query = entityManager.createQuery(criteriaQuery);
    query.setMaxResults(0);
    return query.getResultList();
  }

  @Override
  public void eliminarUsuario(Long id) {
    Usuario usuario = entityManager.find(Usuario.class, id);
    if (usuario != null) {
      entityManager.remove(usuario);
    }
  }

  @Override
  public String crearUsuario(Usuario usuario) {
    entityManager.persist(usuario);
    return usuario.getId().toString();
  }

  @Override
  public void actualizarUsuario(Usuario usuario) {
    entityManager.merge(usuario);
  }
}
