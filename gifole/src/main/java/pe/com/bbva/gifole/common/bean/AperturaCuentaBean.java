package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class AperturaCuentaBean implements Serializable {

  private static final long serialVersionUID = 4395374511960448215L;

  private Long id;
  private String tipoCuenta;
  private String tipoMoneda;
  private String indPremio;
  private String nombrePremio;
  private String indTarjetaFisica;
  private String nroTarjetaAsociada;
  private Date fechaNacimiento;
  private String codigoCentral;
  private String email;
  private String nombres;
  private String paterno;
  private String materno;
  private String paisNacimiento;
  private String paisDomicilio;
  private String nacionalidad;
  private String paisResidencia1;
  private String nroIdentificacion1;
  private String paisResidencia2;
  private String nroIdentificacion2;
  private String paisResidencia3;
  private String nroIdentificacion3;
  private String kitFatca;
  private Date fechaFatca;
  private String declaracionFatca;
  private String indNacionalidadFatca;
  private String indCertificadoFacta;
  private String nroCuenta;
  private String cci;
  private String estadoActual;
  private String estadoPremio;
  private String canal;

  private String telefonoContacto;

  private Date fechaNacimientoOp;
  private Date fechaFatcaOp;

  private Date fechaRegistro;
  private Date fechaRegistro1;
  private Date fechaRegistro2;

  private String nombreCompleto;

  private String observacion;

  private Integer flagAfiliacionVip;

  private String tipoDocumento;

  private String numeroDocumento;

  private String rbaRiesgo;
  private String rbaDiligencia;

  private String creador;
  private String editor;
  private String indOpcionRequisitos;
  private String indPais;
  private String indNacionalidad;
  private Integer flagProcAfiliacionVip;
}
