package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import lombok.Data;

@Data
public class FileBean implements Serializable {

  private static final long serialVersionUID = 1L;

  private String nombre;
  private String extension;
  private String fechaModificacion;
  private String rutaArchivo;
  private String codigoMaestro;
  private int secuencial;
  private boolean indicadorRegistro;
}
