package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.PagoDiferidoBean;

@Component
public class PagoDiferidoRowMapper implements RowMapper<PagoDiferidoBean> {

  @Override
  public PagoDiferidoBean mapRow(ResultSet rs, int row) throws SQLException {
    PagoDiferidoBean pagoDiferido = new PagoDiferidoBean();
    pagoDiferido.setCodigoCentral(StringUtils.trimToEmpty(rs.getString("COD_CENTRAL")));
    pagoDiferido.setTipoDocumento(StringUtils.trimToEmpty(rs.getString("TIPO_DOCUMENTO")));
    pagoDiferido.setNumeroDocumento(StringUtils.trimToEmpty(rs.getString("NUM_DOCUMENTO")));
    pagoDiferido.setNombresApellidos(StringUtils.trimToEmpty(rs.getString("NOMBRES_APELLIDOS")));
    pagoDiferido.setCelular(StringUtils.trimToEmpty(rs.getString("CELULAR")));
    pagoDiferido.setCorreo(StringUtils.trimToEmpty(rs.getString("CORREO")));
    pagoDiferido.setNumeroContrato(StringUtils.trimToEmpty(rs.getString("NUM_CONTRATO")));
    pagoDiferido.setIndicador(StringUtils.trimToEmpty(rs.getString("INDICADOR")));
    pagoDiferido.setCanal(StringUtils.trimToEmpty(rs.getString("CANAL")));
    pagoDiferido.setFechaRegistro(StringUtils.trimToEmpty(rs.getString("FECHA_REGISTRO")));
    return pagoDiferido;
  }
}
