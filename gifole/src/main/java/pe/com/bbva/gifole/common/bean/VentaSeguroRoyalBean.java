package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class VentaSeguroRoyalBean implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;
  private String fechaRegistroCotizacion;
  private String fechaRegistroVenta;
  private String nombre;
  private String documento;
  private String apellido;
  private String correo;
  private String telefono;
  private String productoId;
  private String productoName;
  private Date fechaRegistroDesde;
  private Date fechaRegistroHasta;
  private String planCuotasMoneda;
  private String planCuotasPeriodo;
  private String planCuotasMonto;
  private String planNumeroCuotasTotal;
  private String cotizacionId;
  private String primaMonto;
  private String primaMontoMoneda;
  private String canal;
  private String informacionBanco;
  private String codigoOficina;
  private String sumaAsegurada;
  private String sumaAseguradaMoneda;
  private String contratoId;
  private String polizaId;
  private String fechaVigIni;
  private String fechaVigFin;
  private String metodoPago;
  private String nroCuentaOTarjeta;
  private String couponCode;
  private String subChannel;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public String getApellido() {
    return apellido;
  }

  public void setApellido(String apellido) {
    this.apellido = apellido;
  }

  public String getDocumento() {
    return documento;
  }

  public void setDocumento(String documento) {
    this.documento = documento;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public String getTelefono() {
    return telefono;
  }

  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  public String getFechaRegistroCotizacion() {
    return fechaRegistroCotizacion;
  }

  public void setFechaRegistroCotizacion(String fechaRegistro) {
    this.fechaRegistroCotizacion = fechaRegistro;
  }

  public String getFechaRegistroVenta() {
    return fechaRegistroVenta;
  }

  public void setFechaRegistroVenta(String fechaRegistroVenta) {
    this.fechaRegistroVenta = fechaRegistroVenta;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public String getProductoId() {
    return productoId;
  }

  public void setProductoId(String productoId) {
    this.productoId = productoId;
  }

  public String getProductoName() {
    return productoName;
  }

  public void setProductoName(String productoName) {
    this.productoName = productoName;
  }

  public Date getFechaRegistroDesde() {
    return fechaRegistroDesde;
  }

  public void setFechaRegistroDesde(Date fechaRegistroDesde) {
    this.fechaRegistroDesde = fechaRegistroDesde;
  }

  public Date getFechaRegistroHasta() {
    return fechaRegistroHasta;
  }

  public void setFechaRegistroHasta(Date fechaRegistroHasta) {
    this.fechaRegistroHasta = fechaRegistroHasta;
  }

  public String getPlanCuotasMoneda() {
    return planCuotasMoneda;
  }

  public void setPlanCuotasMoneda(String planCuotasMoneda) {
    this.planCuotasMoneda = planCuotasMoneda;
  }

  public String getPlanCuotasPeriodo() {
    return planCuotasPeriodo;
  }

  public void setPlanCuotasPeriodo(String planCuotasPeriodo) {
    this.planCuotasPeriodo = planCuotasPeriodo;
  }

  public String getPlanCuotasMonto() {
    return planCuotasMonto;
  }

  public void setPlanCuotasMonto(String planCuotasMonto) {
    this.planCuotasMonto = planCuotasMonto;
  }

  public String getPlanNumeroCuotasTotal() {
    return planNumeroCuotasTotal;
  }

  public void setPlanNumeroCuotasTotal(String planNumeroCuotasTotal) {
    this.planNumeroCuotasTotal = planNumeroCuotasTotal;
  }

  public String getCotizacionId() {
    return cotizacionId;
  }

  public void setCotizacionId(String cotizacionId) {
    this.cotizacionId = cotizacionId;
  }

  public String getPrimaMonto() {
    return primaMonto;
  }

  public void setPrimaMonto(String primaMonto) {
    this.primaMonto = primaMonto;
  }

  public String getPrimaMontoMoneda() {
    return primaMontoMoneda;
  }

  public void setPrimaMontoMoneda(String primaMontoMoneda) {
    this.primaMontoMoneda = primaMontoMoneda;
  }

  public String getContratoId() {
    return contratoId;
  }

  public void setContratoId(String contratoId) {
    this.contratoId = contratoId;
  }

  public String getPolizaId() {
    return polizaId;
  }

  public void setPolizaId(String polizaId) {
    this.polizaId = polizaId;
  }

  public String getFechaVigIni() {
    return fechaVigIni;
  }

  public void setFechaVigIni(String fechaVigIni) {
    this.fechaVigIni = fechaVigIni;
  }

  public String getFechaVigFin() {
    return fechaVigFin;
  }

  public void setFechaVigFin(String fechaVigFin) {
    this.fechaVigFin = fechaVigFin;
  }

  public String getMetodoPago() {
    return metodoPago;
  }

  public void setMetodoPago(String metodoPago) {
    this.metodoPago = metodoPago;
  }

  public String getNroCuentaOTarjeta() {
    return nroCuentaOTarjeta;
  }

  public void setNroCuentaOTarjeta(String nroCuentaOTarjeta) {
    this.nroCuentaOTarjeta = nroCuentaOTarjeta;
  }

  public String getInformacionBanco() {
    return informacionBanco;
  }

  public void setInformacionBanco(String informacionBanco) {
    this.informacionBanco = informacionBanco;
  }

  public String getCodigoOficina() {
    return codigoOficina;
  }

  public void setCodigoOficina(String codigoOficina) {
    this.codigoOficina = codigoOficina;
  }

  public String getSumaAsegurada() {
    return sumaAsegurada;
  }

  public void setSumaAsegurada(String sumaAsegurada) {
    this.sumaAsegurada = sumaAsegurada;
  }

  public String getSumaAseguradaMoneda() {
    return sumaAseguradaMoneda;
  }

  public void setSumaAseguradaMoneda(String sumaAseguradaMoneda) {
    this.sumaAseguradaMoneda = sumaAseguradaMoneda;
  }

  public String getCouponCode() {
    return couponCode;
  }

  public void setCouponCode(String couponCode) {
    this.couponCode = couponCode;
  }

  public String getSubChannel() {
    return subChannel;
  }

  public void setSubChannel(String subChannel) {
    this.subChannel = subChannel;
  }
}
