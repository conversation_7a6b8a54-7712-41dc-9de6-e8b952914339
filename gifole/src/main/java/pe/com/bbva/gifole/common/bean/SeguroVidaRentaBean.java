package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class SeguroVidaRentaBean implements Serializable {

  private static final long serialVersionUID = -4676896845365875703L;

  private Long id;
  private String fechaSolicitud;
  private Date fechaSolicitudOp;
  private int vigencia;
  private int diaPago;
  private String coberturaFallecimiento;
  private String coberturaSobrevivencia;
  private String numeroPoliza;
  private String tipoPlan;
  private String sumaAsegurada;
  private String primaSeguro;
  private String moneda;
  private String nroCertificado;
  private Date fechaIniSeguroOp;
  private Date fechaFinSeguroOp;
  private String fechaIniSeguro;
  private String fechaFinSeguro;
  private String nroCuentaCargo;
  private String codigoCentral;
  private String nombre;
  private String tipoDocumento;
  private String nroDocumento;
  private Date fechaNacimientoOp;
  private String fechaNacimiento;
  private String sexo;
  private String correo;
  private String telefono;
  private String direccion1;
  private String direccion2;
  private String direccion3;
  private String direccion4;
  private String estado;
  private String canal;
  private Date fechaRegistro;
  private String creador;

  private String valor1;
  private String valor2;
  private String fechaDeRegistro;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public int getVigencia() {
    return vigencia;
  }

  public void setVigencia(int vigencia) {
    this.vigencia = vigencia;
  }

  public int getDiaPago() {
    return diaPago;
  }

  public void setDiaPago(int diaPago) {
    this.diaPago = diaPago;
  }

  public String getCoberturaFallecimiento() {
    return coberturaFallecimiento;
  }

  public void setCoberturaFallecimiento(String coberturaFallecimiento) {
    this.coberturaFallecimiento = coberturaFallecimiento;
  }

  public String getCoberturaSobrevivencia() {
    return coberturaSobrevivencia;
  }

  public void setCoberturaSobrevivencia(String coberturaSobrevivencia) {
    this.coberturaSobrevivencia = coberturaSobrevivencia;
  }

  public String getNumeroPoliza() {
    return numeroPoliza;
  }

  public void setNumeroPoliza(String numeroPoliza) {
    this.numeroPoliza = numeroPoliza;
  }

  public String getTipoPlan() {
    return tipoPlan;
  }

  public void setTipoPlan(String tipoPlan) {
    this.tipoPlan = tipoPlan;
  }

  public String getSumaAsegurada() {
    return sumaAsegurada;
  }

  public void setSumaAsegurada(String sumaAsegurada) {
    this.sumaAsegurada = sumaAsegurada;
  }

  public String getPrimaSeguro() {
    return primaSeguro;
  }

  public void setPrimaSeguro(String primaSeguro) {
    this.primaSeguro = primaSeguro;
  }

  public String getMoneda() {
    return moneda;
  }

  public void setMoneda(String moneda) {
    this.moneda = moneda;
  }

  public String getNroCertificado() {
    return nroCertificado;
  }

  public void setNroCertificado(String nroCertificado) {
    this.nroCertificado = nroCertificado;
  }

  public String getNroCuentaCargo() {
    return nroCuentaCargo;
  }

  public void setNroCuentaCargo(String nroCuentaCargo) {
    this.nroCuentaCargo = nroCuentaCargo;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public String getNroDocumento() {
    return nroDocumento;
  }

  public void setNroDocumento(String nroDocumento) {
    this.nroDocumento = nroDocumento;
  }

  public String getSexo() {
    return sexo;
  }

  public void setSexo(String sexo) {
    this.sexo = sexo;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public String getTelefono() {
    return telefono;
  }

  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public Date getFechaRegistro() {
    return fechaRegistro;
  }

  public void setFechaRegistro(Date fechaRegistro) {
    this.fechaRegistro = fechaRegistro;
  }

  public String getCreador() {
    return creador;
  }

  public void setCreador(String creador) {
    this.creador = creador;
  }

  public String getFechaSolicitud() {
    return fechaSolicitud;
  }

  public void setFechaSolicitud(String fechaSolicitud) {
    this.fechaSolicitud = fechaSolicitud;
  }

  public Date getFechaSolicitudOp() {
    return fechaSolicitudOp;
  }

  public void setFechaSolicitudOp(Date fechaSolicitudOp) {
    this.fechaSolicitudOp = fechaSolicitudOp;
  }

  public Date getFechaIniSeguroOp() {
    return fechaIniSeguroOp;
  }

  public void setFechaIniSeguroOp(Date fechaIniSeguroOp) {
    this.fechaIniSeguroOp = fechaIniSeguroOp;
  }

  public Date getFechaFinSeguroOp() {
    return fechaFinSeguroOp;
  }

  public void setFechaFinSeguroOp(Date fechaFinSeguroOp) {
    this.fechaFinSeguroOp = fechaFinSeguroOp;
  }

  public String getFechaIniSeguro() {
    return fechaIniSeguro;
  }

  public void setFechaIniSeguro(String fechaIniSeguro) {
    this.fechaIniSeguro = fechaIniSeguro;
  }

  public String getFechaFinSeguro() {
    return fechaFinSeguro;
  }

  public void setFechaFinSeguro(String fechaFinSeguro) {
    this.fechaFinSeguro = fechaFinSeguro;
  }

  public Date getFechaNacimientoOp() {
    return fechaNacimientoOp;
  }

  public void setFechaNacimientoOp(Date fechaNacimientoOp) {
    this.fechaNacimientoOp = fechaNacimientoOp;
  }

  public String getFechaNacimiento() {
    return fechaNacimiento;
  }

  public void setFechaNacimiento(String fechaNacimiento) {
    this.fechaNacimiento = fechaNacimiento;
  }

  public String getDireccion1() {
    return direccion1;
  }

  public void setDireccion1(String direccion1) {
    this.direccion1 = direccion1;
  }

  public String getDireccion2() {
    return direccion2;
  }

  public void setDireccion2(String direccion2) {
    this.direccion2 = direccion2;
  }

  public String getDireccion3() {
    return direccion3;
  }

  public void setDireccion3(String direccion3) {
    this.direccion3 = direccion3;
  }

  public String getDireccion4() {
    return direccion4;
  }

  public void setDireccion4(String direccion4) {
    this.direccion4 = direccion4;
  }

  public String getValor1() {
    return valor1;
  }

  public void setValor1(String valor1) {
    this.valor1 = valor1;
  }

  public String getValor2() {
    return valor2;
  }

  public void setValor2(String valor2) {
    this.valor2 = valor2;
  }

  public String getFechaDeRegistro() {
    return fechaDeRegistro;
  }

  public void setFechaDeRegistro(String fechaDeRegistro) {
    this.fechaDeRegistro = fechaDeRegistro;
  }
}
