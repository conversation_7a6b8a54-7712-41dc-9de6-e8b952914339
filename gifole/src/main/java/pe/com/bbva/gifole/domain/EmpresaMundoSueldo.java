package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "EMPRESA_MUNDO_SUELDO")
public class EmpresaMundoSueldo implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_EMPRESA_MUNDO_SUELDO")
  @TableGenerator(
      name = "SEQ_EMPRESA_MUNDO_SUELDO",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.EmpresaMundoSueldo",
      allocationSize = 1)
  private Long id;

  @Column(name = "NOMBRE", length = 50)
  private String nombre;

  @Column(name = "NUMERO", length = 11)
  private String numero;

  @Column(name = "DEPARTAMENTO", length = 20)
  private String departamento;

  @Column(name = "CORREO", length = 80)
  private String correo;

  @Column(name = "TELEFONO1", length = 20)
  private String telefono1;

  @Column(name = "TELEFONO2", length = 20)
  private String telefono2;

  @Column(name = "HORARIO", length = 20)
  private String horario;

  @Column(name = "CONTACTO", length = 100)
  private String contacto;

  @Column(name = "FECHA_REGISTRO")
  private Date fechaRegistro;

  @Column(name = "PROCESADO", length = 1)
  private String procesado;

  @Column(name = "AUTORIZACION", length = 1)
  private String autorizacion;
}
