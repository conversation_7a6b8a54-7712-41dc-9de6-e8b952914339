package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_BENEFICIO")
public class VehBeneficio implements Serializable {
  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_VEH_BENEFICIO")
  @TableGenerator(
      name = "SEQ_VEH_BENEFICIO",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.VehBeneficio",
      allocationSize = 1)
  private Long id;

  @Column(name = "DESCRIPCION", length = 100)
  private String descripcion;

  @Column(name = "TIPO", length = 1)
  private String tipo;

  @Column(name = "ORDEN")
  private Integer orden;

  @Transient private List<VehPlanBeneficio> planBeneficios;
}
