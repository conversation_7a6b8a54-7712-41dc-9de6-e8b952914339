package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.PrestamoAlToqueBean; // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.util.Constante;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Préstamos")
@Route(value = "reporte/prestamos/consulta", layout = MainLayout.class)
public class ConsultaPrestamoView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<PrestamoAlToqueBean> allData = new ArrayList<>();
    private DataTable<PrestamoAlToqueBean> dataTable;

    public ConsultaPrestamoView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Préstamos");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<PrestamoAlToqueBean>builder()
                .id("tabla-prestamos") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaPrestamoUI)
                .column("codigoCentral", "Código Central", 
                    bean -> StringUtils.trimToEmpty(bean.getCodigoCentral()), "100px")
                .column("tipoDocumento", "Tipo Documento", 
                    bean -> StringUtils.trimToEmpty(bean.getTipoDocumento()), "120px")
                .column("numeroDocumento", "Número Documento", 
                    bean -> StringUtils.trimToEmpty(bean.getNumeroDocumento()), "120px")
                .column("nombreCompleto", "Nombre Completo", 
                    bean -> StringUtils.trimToEmpty(bean.getNombreCompleto()), "200px")
                .column("correo", "Correo", 
                    bean -> bean.getCorreo(), "180px")
                .column("telefono", "Teléfono", 
                    bean -> StringUtils.trimToEmpty(bean.getTelefono()), "100px")
                .column("numeroContrato", "Número Contrato", 
                    bean -> StringUtils.trimToEmpty(bean.getNumeroContrato()), "120px")
                .column("monto", "Monto", 
                    bean -> StringUtils.trimToEmpty(bean.getMonto()), "100px")
                .column("cuota", "Cuota", 
                    bean -> StringUtils.trimToEmpty(bean.getCuota()), "100px")
                .column("plazo", "Plazo", 
                    bean -> StringUtils.trimToEmpty(bean.getPlazo()), "100px")
                .column("tea", "TEA", 
                    bean -> StringUtils.trimToEmpty(bean.getTea()), "100px")
                .column("tcea", "TCEA", 
                    bean -> StringUtils.trimToEmpty(bean.getTcea()), "100px")
                .column("fechaContratacion", "Fecha Contratación", 
                    bean -> bean.getFechaRegistro() != null ? FORMATO_FECHA.format(bean.getFechaRegistro()) : "", "150px")
                .column("canal", "Canal", 
                    bean -> {
                        String canal = StringUtils.EMPTY;
                        if (bean.getCanal() == null) {
                            canal = Constante.CANAL.NOMBRE_BANCA_POR_INTERNET;
                        } else if (bean.getCanal().equals(Constante.CANAL.BANCA_POR_INTERNET_UX)) {
                            canal = Constante.CANAL.NOMBRE_BANCA_POR_INTERNET;
                        } else {
                            canal = StringUtils.trimToEmpty(bean.getCanal());
                        }
                        return canal;
                    }, "120px")
                .column("marcaPh", "Marca PH", 
                    bean -> StringUtils.trimToEmpty(bean.getMarcaPh()), "100px")
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

    // Método para cargar datos, puedes conectarlo a un servicio
    private void loadData(List<PrestamoAlToqueBean> data) {
        // Limpiar datos existentes
        allData.clear();
        allData.addAll(data); // Asumiendo que 'data' viene del servicio

        // Actualizar la tabla con los nuevos datos
        dataTable.setItems(allData);
    }

}