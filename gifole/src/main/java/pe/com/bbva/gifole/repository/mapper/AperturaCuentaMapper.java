package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.AperturaCuentaBean;

@SuppressWarnings("rawtypes")
@Component
public class AperturaCuentaMapper implements RowMapper {

  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    AperturaCuentaBean param = new AperturaCuentaBean();

    param.setId(rs.getLong("ID"));
    param.setTipoCuenta(rs.getString("TIPO_CUENTA"));
    param.setTipoMoneda(rs.getString("TIPO_MONEDA"));
    param.setIndPremio(rs.getString("IND_PREMIO"));
    param.setIndTarjetaFisica(rs.getString("IND_TARJETA_FISICA"));
    param.setNroTarjetaAsociada(rs.getString("NRO_TARJETA_ASOCIADA"));
    param.setFechaNacimiento(rs.getDate("FECHA_NACIMIENTO"));
    param.setNombres(rs.getString("NOMBRES"));
    param.setPaterno(rs.getString("APEPAT"));
    param.setMaterno(rs.getString("APEMAT"));
    param.setCodigoCentral(rs.getString("CODIGO_CENTRAL"));
    param.setEmail(rs.getString("EMAIL"));
    param.setPaisNacimiento(rs.getString("PAIS_NACIMIENTO"));
    param.setPaisDomicilio(rs.getString("PAIS_DOMICILIO"));
    param.setNacionalidad(rs.getString("NACIONALIDAD"));
    param.setPaisResidencia1(rs.getString("PAIS_RESIDENCIA1"));
    param.setNroIdentificacion1(rs.getString("NRO_IDENTIFICACION1"));
    param.setPaisResidencia2(rs.getString("PAIS_RESIDENCIA2"));
    param.setNroIdentificacion2(rs.getString("NRO_IDENTIFICACION2"));
    param.setPaisResidencia3(rs.getString("PAIS_RESIDENCIA3"));
    param.setNroIdentificacion3(rs.getString("NRO_IDENTIFICACION3"));
    param.setKitFatca(rs.getString("KIT_FATCA"));
    param.setFechaFatca(rs.getDate("FECHA_FATCA"));
    param.setDeclaracionFatca(rs.getString("DECLARACION_FATCA"));
    param.setIndNacionalidadFatca(rs.getString("IND_NACIONALIDAD_FATCA"));
    param.setIndCertificadoFacta(rs.getString("IND_CERTIFCADO_FACTA"));
    param.setNroCuenta(rs.getString("NRO_CUENTA"));
    param.setCci(rs.getString("CCI"));
    param.setEstadoActual(rs.getString("ESTADO_ACTUAL"));
    param.setEstadoPremio(rs.getString("ESTADO_PREMIO"));
    param.setFechaRegistro(rs.getDate("FECHA_REGISTRO"));
    param.setFechaRegistro1(rs.getDate("FECHA_MODIFICACION"));
    param.setCanal(rs.getString("CANAL"));
    param.setTelefonoContacto(rs.getString("TELEFONO"));
    param.setRbaRiesgo(rs.getString("RBA_RIESGO"));
    param.setRbaDiligencia(rs.getString("RBA_DILIGENCIA"));
    param.setObservacion(rs.getString("OBSERVACION"));
    param.setNombrePremio(rs.getString("NOMBRE_PREMIO"));
    param.setCreador(rs.getString("CREADOR"));
    param.setEditor(rs.getString("EDITOR"));
    param.setIndOpcionRequisitos(rs.getString("IND_OPCION_REQUISITOS"));
    param.setIndPais(rs.getString("IND_PAIS"));
    param.setIndNacionalidad(rs.getString("IND_NACIONALIDAD"));
    param.setFlagAfiliacionVip(rs.getInt("FLAG_AFILICN_VIP"));
    param.setFlagProcAfiliacionVip(rs.getInt("FLAG_PROCESO_AFLCN"));

    return param;
  }
}
