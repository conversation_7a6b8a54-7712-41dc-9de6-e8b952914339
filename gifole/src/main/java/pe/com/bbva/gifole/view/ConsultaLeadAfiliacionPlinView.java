package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.AfiliacionPlinBean; // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.util.Util;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Lead Afiliación Plin")
@Route(value = "reporte/leads/afiliacion-plin", layout = MainLayout.class)
public class ConsultaLeadAfiliacionPlinView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<AfiliacionPlinBean> allData = new ArrayList<>();
    private DataTable<AfiliacionPlinBean> dataTable;

    public ConsultaLeadAfiliacionPlinView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Lead Afiliación Plin");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<AfiliacionPlinBean>builder()
                .id("tabla-lead-afiliacion-plin") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaLeadAfiliacionPlinUI)
                .column("ruc", "RUC", bean -> StringUtils.trimToEmpty(bean.getRuc()), "120px")
                .column("razonSocial", "Razón Social", bean -> StringUtils.trimToEmpty(bean.getRazonSocial()), "200px")
                .column("numeroCuenta", "Número Cuenta", bean -> StringUtils.trimToEmpty(bean.getNumeroCuenta()), "120px")
                .column("tipoDocumento", "Tipo Documento", bean -> StringUtils.trimToEmpty(bean.getTipoDocumento()), "120px")
                .column("numeroDocumento", "Número Documento", bean -> StringUtils.trimToEmpty(bean.getNumeroDocumento()), "120px")
                .column("apellidosRepresentante", "Apellidos Representante", bean -> StringUtils.trimToEmpty(bean.getApellidosRepresentante()), "200px")
                .column("nombreRepresentante", "Nombre Representante", bean -> StringUtils.trimToEmpty(bean.getNombreRepresentante()), "200px")
                .column("celular", "Celular", bean -> StringUtils.trimToEmpty(bean.getCelular()), "120px")
                .column("correo", "Correo", bean -> StringUtils.trimToEmpty(bean.getCorreo()), "200px")
                .column("fechaRegistro", "Fecha Registro", bean -> Util.convertirFechaAString(bean.getFechaRegistro()), "150px")
                .column("fechaActualizacion", "Fecha Actualización", bean -> Util.convertirFechaAString(bean.getFechaActualizacion()), "150px")
                .column("estado", "Estado", bean -> StringUtils.trimToEmpty(bean.getEstado()), "120px")
                .column("comentario", "Comentario", bean -> StringUtils.trimToEmpty(bean.getComentario()), "200px")
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}