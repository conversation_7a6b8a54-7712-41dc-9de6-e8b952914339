package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.RouterLayout;

public class EmptyLayout extends VerticalLayout implements RouterLayout {

  public EmptyLayout() {
    setSizeFull();
    setMargin(false);
    setPadding(false);
    setSpacing(false);

    // Aplicar clase CSS espec�fica para este layout
    addClassName("empty-layout");
  }
}
