package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.SisPerfil;
import pe.com.bbva.gifole.domain.Usuario;

@Component
public class UsuarioMapper implements RowMapper<Usuario> {

  @Override
  public Usuario mapRow(ResultSet rs, int i) throws SQLException {
    Usuario usuario = new Usuario();
    SisPerfil sisPerfil = new SisPerfil();
    sisPerfil.setId(rs.getLong("ID_PERFIL"));
    sisPerfil.setDescripcion(rs.getString("DESCRIPCION"));
    sisPerfil.setEstado(rs.getString("ESTADO"));
    usuario.setId(rs.getLong("ID_USUARIO"));
    usuario.setRegistro(rs.getString("REGISTRO"));
    usuario.setNombre(rs.getString("NOMBRE"));
    usuario.setPaterno(rs.getString("PATERNO"));
    usuario.setMaterno(rs.getString("MATERNO"));
    usuario.setSisPerfil(sisPerfil);
    return usuario;
  }
}
