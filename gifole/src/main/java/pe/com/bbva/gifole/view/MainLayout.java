package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.applayout.AppLayout;
import com.vaadin.flow.component.avatar.Avatar;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.contextmenu.ContextMenu;
import com.vaadin.flow.component.contextmenu.MenuItem;
import com.vaadin.flow.component.contextmenu.SubMenu;
import com.vaadin.flow.component.html.Div;
import com.vaadin.flow.component.html.H1;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.menubar.MenuBar;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;

/**
 * Layout principal de la aplicación con navegación horizontal superior moderna Rediseñado desde
 * cero con mejores prácticas de UX y diseño BBVA
 */
public class MainLayout extends AppLayout {

  public MainLayout() {
    createModernNavigation();
    applyBBVATheme();
  }

  private void createModernNavigation() {
    // Contenedor principal del header moderno
    HorizontalLayout headerLayout = new HorizontalLayout();
    headerLayout.setWidthFull();
    headerLayout.setPadding(false);
    headerLayout.setSpacing(false);
    headerLayout.setAlignItems(FlexComponent.Alignment.CENTER);
    headerLayout.addClassName("bbva-modern-header");

    // Sección izquierda: Logo y título
    HorizontalLayout leftSection = createLeftSection();

    // Sección central: Navegación principal
    HorizontalLayout centerSection = createCenterSection();

    // Sección derecha: Usuario y acciones
    HorizontalLayout rightSection = createRightSection();

    // Configurar distribución del espacio
    headerLayout.add(leftSection, centerSection, rightSection);
    headerLayout.setFlexGrow(0, leftSection); // Tamaño fijo
    headerLayout.setFlexGrow(1, centerSection); // Se expande
    headerLayout.setFlexGrow(0, rightSection); // Tamaño fijo

    // Agregar al navbar de Vaadin
    addToNavbar(headerLayout);
  }

  private HorizontalLayout createLeftSection() {
    HorizontalLayout leftSection = new HorizontalLayout();
    leftSection.setSpacing(true);
    leftSection.setAlignItems(FlexComponent.Alignment.CENTER);
    leftSection.addClassName("bbva-header-left");

    // Logo BBVA moderno con animación
    Div logoContainer = createModernLogo();

    // Título de la aplicación con mejor tipografía
    H1 appTitle = new H1("BBVA Gifole");
    appTitle.addClassName("bbva-modern-title");

    leftSection.add(logoContainer, appTitle);
    return leftSection;
  }

  private Div createModernLogo() {
    Div logoContainer = new Div();
    logoContainer.addClassName("bbva-modern-logo");

    // Barras del logo con gradientes y animación
    Div bar1 = createLogoBar("bbva-logo-bar-1");
    Div bar2 = createLogoBar("bbva-logo-bar-2");
    Div bar3 = createLogoBar("bbva-logo-bar-3");

    logoContainer.add(bar1, bar2, bar3);
    return logoContainer;
  }

  private Div createLogoBar(String className) {
    Div bar = new Div();
    bar.addClassName("bbva-logo-bar");
    bar.addClassName(className);
    return bar;
  }

  private HorizontalLayout createCenterSection() {
    HorizontalLayout centerSection = new HorizontalLayout();
    centerSection.setSpacing(false);
    centerSection.setAlignItems(FlexComponent.Alignment.CENTER);
    centerSection.setJustifyContentMode(FlexComponent.JustifyContentMode.CENTER);
    centerSection.addClassName("bbva-header-center");

    MenuBar menuBar = new MenuBar();
    menuBar.addClassName("bbva-menu-bar");
    menuBar.setThemeName("bbva-dark-menu"); // Aplicar el tema aquí

    // Menú Configuración (con submenú) - YA ESTÁ CORRECTO
    MenuItem configMenu = menuBar.addItem(createMenuContent("Configuración", VaadinIcon.COG));
    SubMenu configSubMenu = configMenu.getSubMenu();
    configSubMenu.addItem("Parámetros", e -> handleNavigation("parametro"));
    configSubMenu.addItem("Usuarios", e -> handleNavigation("usuarios"));

    // Submenú para Permisos
    MenuItem permisosMenu = configSubMenu.addItem("Permisos");
    SubMenu permisosSubMenu = permisosMenu.getSubMenu();
    permisosSubMenu.addItem("Gestionar Roles", e -> handleNavigation("permisos/roles"));
    permisosSubMenu.addItem("Asignar a Usuarios", e -> handleNavigation("permisos/asignar"));

    // Menú Reporte
    menuBar.addItem(
        createMenuContent("Reporte", VaadinIcon.CHART_LINE), e -> handleNavigation("reporte"));

    // Menú Tarjetas
    menuBar.addItem(
        createMenuContent("Tarjetas", VaadinIcon.CREDIT_CARD), e -> handleNavigation("tarjetas"));

    // Menú Cuentas
    menuBar.addItem(
        createMenuContent("Cuentas", VaadinIcon.WALLET), e -> handleNavigation("cuentas"));

    // Menú Préstamos
    menuBar.addItem(
        createMenuContent("Préstamos", VaadinIcon.CASH), e -> handleNavigation("prestamos"));

    // Menú Cancelación de Productos
    menuBar.addItem(
        createMenuContent("Cancelación de Productos", VaadinIcon.TRASH),
        e -> handleNavigation("cancelacion"));

    // Menú Cambio Modalidad EECC
    menuBar.addItem(
        createMenuContent("Cambio Modalidad EECC", VaadinIcon.EDIT),
        e -> handleNavigation("cambio-modalidad"));

    // Menú Mantenimiento
    menuBar.addItem(
        createMenuContent("Mantenimiento", VaadinIcon.WRENCH),
        e -> handleNavigation("mantenimiento"));

    // Menú Carga de Archivos
    menuBar.addItem(
        createMenuContent("Carga de Archivos", VaadinIcon.UPLOAD),
        e -> handleNavigation("carga-archivos"));

    // Menú Historial de Carga
    menuBar.addItem(
        createMenuContent("Historial de Carga", VaadinIcon.RECORDS),
        e -> handleNavigation("historial-carga"));

    // Menú Operaciones (con submenú)
    MenuItem operacionesMenu =
        menuBar.addItem(createMenuContent("Operaciones", VaadinIcon.EXCHANGE));
    SubMenu operacionesSubMenu = operacionesMenu.getSubMenu();
    operacionesSubMenu.addItem(
        "Detalle de Operación", e -> handleNavigation("operaciones/detalle"));
    operacionesSubMenu.addItem("Detalle de Préstamo", e -> handleNavigation("prestamos/detalle"));
    operacionesSubMenu.addItem("Detalle de Producto", e -> handleNavigation("productos/detalle"));
    operacionesSubMenu.addItem("Detalle de Tarjeta", e -> handleNavigation("tarjetas/detalle"));
    operacionesSubMenu.addItem(
        "Detalle de Transacción", e -> handleNavigation("transacciones/detalle"));
    operacionesSubMenu.addItem("Deuda de Cliente", e -> handleNavigation("cobranza/deudas"));
    operacionesSubMenu.addItem(
        "Devolución de Comisión", e -> handleNavigation("operaciones/devoluciones/comisiones"));
    operacionesSubMenu.addItem(
        "Devolución de Impuesto", e -> handleNavigation("operaciones/devoluciones/impuestos"));
    operacionesSubMenu.addItem("Digital Onboarding", e -> handleNavigation("clientes/onboarding"));
    operacionesSubMenu.addItem(
        "Dirección de Cliente", e -> handleNavigation("clientes/direcciones"));
    operacionesSubMenu.addItem(
        "Disposición de Efectivo", e -> handleNavigation("tarjetas/disposicion-efectivo"));
    operacionesSubMenu.addItem(
        "Documentación de Cliente", e -> handleNavigation("clientes/documentacion"));
    operacionesSubMenu.addItem(
        "Documento Requerido", e -> handleNavigation("solicitudes/documentos"));
    operacionesSubMenu.addItem("Envío de Correo", e -> handleNavigation("comunicaciones/correo"));
    operacionesSubMenu.addItem("Estado de Tarjeta", e -> handleNavigation("tarjetas/estado"));
    operacionesSubMenu.addItem("Estado de Trámite", e -> handleNavigation("tramites/estado"));

    centerSection.add(menuBar);
    return centerSection;
  }

  private HorizontalLayout createMenuContent(String text, VaadinIcon icon) {
    HorizontalLayout layout = new HorizontalLayout(new Icon(icon), new Span(text));
    layout.setAlignItems(FlexComponent.Alignment.CENTER);
    layout.setSpacing(false);
    layout.setPadding(false);
    layout.setMargin(false);
    layout.getStyle().set("gap", "2px");
    return layout;
  }

  private HorizontalLayout createRightSection() {
    HorizontalLayout rightSection = new HorizontalLayout();
    rightSection.setSpacing(true);
    rightSection.setAlignItems(FlexComponent.Alignment.CENTER);
    rightSection.addClassName("bbva-header-right");

    // Botón de notificaciones
    Button notificationButton = createNotificationButton();

    // Avatar y menú de usuario
    Div userMenu = createUserMenu();

    rightSection.add(notificationButton, userMenu);
    return rightSection;
  }

  private Button createNotificationButton() {
    Button notificationBtn = new Button(new Icon(VaadinIcon.BELL));
    notificationBtn.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
    notificationBtn.addClassName("bbva-notification-button");

    // Badge de notificaciones
    Span badge = new Span("3");
    badge.addClassName("bbva-notification-badge");
    notificationBtn.getElement().appendChild(badge.getElement());

    notificationBtn.addClickListener(
        e -> {
          // Mostrar panel de notificaciones
          showNotifications();
        });

    return notificationBtn;
  }

  private Div createUserMenu() {
    Div userContainer = new Div();
    userContainer.addClassName("bbva-user-container");

    // Avatar del usuario
    Avatar userAvatar = new Avatar("Usuario BBVA");
    userAvatar.addClassName("bbva-user-avatar");

    // Información del usuario
    Div userInfo = new Div();
    userInfo.addClassName("bbva-user-info");

    Span userName = new Span("Juan Pérez");
    userName.addClassName("bbva-user-name");

    Span userRole = new Span("Administrador");
    userRole.addClassName("bbva-user-role");

    userInfo.add(userName, userRole);

    // Menú contextual del usuario
    ContextMenu userContextMenu = new ContextMenu(userContainer);
    userContextMenu.addClassName("bbva-user-menu");

    userContextMenu.addItem("Mi Perfil", e -> handleNavigation("perfil"));
    userContextMenu.addItem("Configuración", e -> handleNavigation("configuracion"));
    userContextMenu.addSeparator();
    userContextMenu.addItem("Cerrar Sesión", e -> logout());

    userContainer.add(userAvatar, userInfo);
    return userContainer;
  }

  private void handleNavigation(String route) {
    getUI().ifPresent(ui -> ui.navigate(route));
  }

  private void showNotifications() {
    // Implementar panel de notificaciones
    System.out.println("Mostrando notificaciones");
  }

  private void logout() {
    // Implementar logout
    getUI().ifPresent(ui -> ui.navigate(""));
  }

  private void applyBBVATheme() {
    addClassName("bbva-modern-layout");
  }
}
