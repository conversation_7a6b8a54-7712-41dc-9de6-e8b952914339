package pe.com.bbva.gifole.service.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.io.Serializable;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import pe.com.bbva.gifole.common.bean.FileBean;
import pe.com.bbva.gifole.domain.Parametro;
import pe.com.bbva.gifole.domain.Parametro.FILE_UTIL;
import pe.com.bbva.gifole.repository.ParametroRepository;
import pe.com.bbva.gifole.service.ParametroService;
import pe.com.bbva.gifole.util.FileUtil;

@Service
public class ParametroServiceImpl implements ParametroService, Serializable {

  private static final long serialVersionUID = 1L;

  @Autowired private ParametroRepository parametroRepository;

  @PersistenceContext private EntityManager entityManager;

  @Override
  @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
  public void guardarParametro(Parametro parametro, String indicadorFlagAnterior) {
    if (parametro.getId() == null) {
      parametroRepository.crear(parametro);
    } else {
      parametroRepository.actualizar(parametro);
      if (indicadorFlagAnterior != null) {
        // Procesamos la relacion si cumple los criterios
        procesarRelacionArchivoAdjunto(parametro, indicadorFlagAnterior);
      }
    }
  }

  @Override
  public List<Parametro> buscarParametro(Parametro parametro) {

    CriteriaBuilder cb = entityManager.getCriteriaBuilder();
    CriteriaQuery<Parametro> cq = cb.createQuery(Parametro.class);
    Root<Parametro> root = cq.from(Parametro.class);

    Predicate predicate = cb.conjunction(); // Equivalente a crear el criteria base

    if (parametro != null) {
      if (StringUtils.isNotBlank(parametro.getCodigo())) {
        predicate =
            cb.and(
                predicate,
                cb.like(
                    cb.lower(root.get("codigo")), "%" + parametro.getCodigo().toLowerCase() + "%"));
      }

      if (StringUtils.isNotBlank(parametro.getNombre())) {
        predicate =
            cb.and(
                predicate,
                cb.like(
                    cb.lower(root.get("nombre")), "%" + parametro.getNombre().toLowerCase() + "%"));
      }

      if (StringUtils.isNotBlank(parametro.getValor())) {
        predicate =
            cb.and(
                predicate,
                cb.like(
                    cb.lower(root.get("valor")), "%" + parametro.getValor().toLowerCase() + "%"));
      }

      if (StringUtils.isNotBlank(parametro.getTipo())) {
        predicate =
            cb.and(
                predicate,
                cb.like(cb.lower(root.get("tipo")), "%" + parametro.getTipo().toLowerCase() + "%"));
      }

      if (parametro.getOrden() != null) {
        predicate = cb.and(predicate, cb.equal(root.get("orden"), parametro.getOrden()));
      }

      if (StringUtils.isNotBlank(parametro.getEstado())) {
        predicate =
            cb.and(
                predicate,
                cb.like(
                    cb.lower(root.get("estado")), "%" + parametro.getEstado().toLowerCase() + "%"));
      }

      if (StringUtils.isNotBlank(parametro.getFlagAdjuntarArchivo())) {
        predicate =
            cb.and(
                predicate,
                cb.equal(root.get("flagAdjuntarArchivo"), parametro.getFlagAdjuntarArchivo()));
      }
    }
    return parametroRepository.buscarParametro(cq);
  }

  @Override
  @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
  public void eliminarParametro(Long id, String codigo, String tipo) {
    parametroRepository.eliminar(id);
    // Verificamos que si existe la relacion de parametros detalle
    eliminarDetalleMaestro(codigo, tipo);
  }

  @Override
  public Parametro obtenerParametroPorCodigo(String codigo, String tipo) {
    CriteriaBuilder cb = entityManager.getCriteriaBuilder();
    CriteriaQuery<Parametro> cq = cb.createQuery(Parametro.class);
    Root<Parametro> root = cq.from(Parametro.class);

    Predicate predicate = cb.conjunction();

    if (codigo != null && tipo != null) {
      if (StringUtils.isNotBlank(codigo)) {
        predicate = cb.and(predicate, cb.equal(root.get("codigo"), codigo));
      }
      if (StringUtils.isNotBlank(tipo)) {
        predicate = cb.and(predicate, cb.equal(root.get("tipo"), tipo));
      }
    }

    cq.where(predicate);

    return parametroRepository.obtenerParametro(cq);
  }

  private void eliminarDetalleMaestro(String codigo, String tipo) {
    if (codigo.startsWith(FILE_UTIL.JB_PATH_OUT_INTRANET) && tipo.equals(FILE_UTIL.FILE)) {
      // Eliminamos los registros previos
      Parametro detalleParametro = new Parametro();
      detalleParametro.setTipo(codigo);
      List<Parametro> listaParametro = buscarParametro(detalleParametro);
      for (Parametro param : listaParametro) {
        parametroRepository.eliminar(param.getId());
      }
    }
  }

  @Override
  public void procesarRelacionArchivoAdjunto(Parametro parametro, String indicadorFlagAnterior) {
    try {
      List<Parametro> listaParametro = null;
      Parametro detalleParametro = null;

      if (parametro.getFlagAdjuntarArchivo().equals(FILE_UTIL.FLAG_ACTIVO)) {

        if (!indicadorFlagAnterior.equals(FILE_UTIL.FLAG_ACTIVO)) {
          // Eliminamos los registros previos
          detalleParametro = new Parametro();
          detalleParametro.setTipo(parametro.getCodigo());
          listaParametro = buscarParametro(detalleParametro);
          for (Parametro param : listaParametro) {
            parametroRepository.eliminar(param.getId());
          }

          // Insertamos los nuevos registros
          List<FileBean> listaArchivoRepositorio =
              FileUtil.listarArchivoRepositorio(parametro.getValor());
          int secuencial = 1;
          for (FileBean fileBean : listaArchivoRepositorio) {
            detalleParametro = new Parametro();

            StringBuilder codigoDetalle = new StringBuilder();
            codigoDetalle.append(
                parametro.getCodigo().substring(FILE_UTIL.JB_PATH_OUT_INTRANET.length()));
            codigoDetalle.append(FILE_UTIL.SUBDETALLE);
            codigoDetalle.append(secuencial);

            detalleParametro.setCodigo(codigoDetalle.toString());
            detalleParametro.setNombre(codigoDetalle.toString());
            detalleParametro.setDescripcion(codigoDetalle.toString());
            detalleParametro.setValor(fileBean.getNombre());
            detalleParametro.setTipo(parametro.getCodigo());
            detalleParametro.setEstado(FILE_UTIL.ESTADO_ACTIVO);
            detalleParametro.setOrden(secuencial);
            detalleParametro.setFlagAdjuntarArchivo(FILE_UTIL.FLAG_SUB_DETALLE);
            parametroRepository.crear(detalleParametro);
            secuencial++;
          }
        }

      } else if (parametro.getFlagAdjuntarArchivo().equals(FILE_UTIL.FLAG_INACTIVO)) {

        // Eliminamos los registros previos
        detalleParametro = new Parametro();
        detalleParametro.setTipo(parametro.getCodigo());
        listaParametro = buscarParametro(detalleParametro);
        for (Parametro param : listaParametro) {
          parametroRepository.eliminar(param.getId());
        }
      }
    } catch (Exception e) {
    }
  }
}
