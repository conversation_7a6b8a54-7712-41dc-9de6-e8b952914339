package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.ReferidoBean;

@SuppressWarnings("rawtypes")
@Component
public class TCReferidoMapper implements RowMapper {

  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    ReferidoBean param = new ReferidoBean();

    param.setId(rs.getLong("ID"));
    param.setEstado(rs.getString("ESTADO"));

    return param;
  }
}
