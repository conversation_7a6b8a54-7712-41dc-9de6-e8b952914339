package pe.com.bbva.gifole.common.bean;

public class TCEnvioReferidoFinanzasBean {

  private Long idReferido;

  private String nombreCompletoReferidor;

  private String tipoDocumentoReferidor;

  private String numeroDocumentoReferidor;

  private String marcaEmpleadoReferidor;

  private String numeroDocumentoReferido;

  private String tipoDocumentoReferido;

  private String codigoCentralReferido;

  private String celularReferido;

  private String nombreCompletoReferido;

  private String nbrTarjetaReferido;

  private String tipoTarjetaReferido;

  private String lineaReferido;

  private String tasaReferido;

  private String correoReferido;

  private String fechaLeadGenerado;

  private String historicoEvaluacion;

  private String campaniaReferido;

  private String campaniaReferidor;

  private String historicoObservacion;

  private String estadoReferido;

  private String pasoMotor;

  private String multibinReferido;

  private String fechaRegistro;

  private String fechaEvaluacion;

  private String origenReferido;

  private String observacionPremioReferido;

  private String nroContratoTablon;

  private String nroTarjetaTablon;

  private String fechaContratacionTablon;

  private String fechaActivacionTablon;

  private String binTablon;

  public Long getIdReferido() {
    return idReferido;
  }

  public void setIdReferido(Long idReferido) {
    this.idReferido = idReferido;
  }

  public String getNombreCompletoReferidor() {
    return nombreCompletoReferidor;
  }

  public void setNombreCompletoReferidor(String nombreCompletoReferidor) {
    this.nombreCompletoReferidor = nombreCompletoReferidor;
  }

  public String getTipoDocumentoReferidor() {
    return tipoDocumentoReferidor;
  }

  public void setTipoDocumentoReferidor(String tipoDocumentoReferidor) {
    this.tipoDocumentoReferidor = tipoDocumentoReferidor;
  }

  public String getNumeroDocumentoReferidor() {
    return numeroDocumentoReferidor;
  }

  public void setNumeroDocumentoReferidor(String numeroDocumentoReferidor) {
    this.numeroDocumentoReferidor = numeroDocumentoReferidor;
  }

  public String getMarcaEmpleadoReferidor() {
    return marcaEmpleadoReferidor;
  }

  public void setMarcaEmpleadoReferidor(String marcaEmpleadoReferidor) {
    this.marcaEmpleadoReferidor = marcaEmpleadoReferidor;
  }

  public String getNumeroDocumentoReferido() {
    return numeroDocumentoReferido;
  }

  public void setNumeroDocumentoReferido(String numeroDocumentoReferido) {
    this.numeroDocumentoReferido = numeroDocumentoReferido;
  }

  public String getTipoDocumentoReferido() {
    return tipoDocumentoReferido;
  }

  public void setTipoDocumentoReferido(String tipoDocumentoReferido) {
    this.tipoDocumentoReferido = tipoDocumentoReferido;
  }

  public String getCodigoCentralReferido() {
    return codigoCentralReferido;
  }

  public void setCodigoCentralReferido(String codigoCentralReferido) {
    this.codigoCentralReferido = codigoCentralReferido;
  }

  public String getCelularReferido() {
    return celularReferido;
  }

  public void setCelularReferido(String celularReferido) {
    this.celularReferido = celularReferido;
  }

  public String getNombreCompletoReferido() {
    return nombreCompletoReferido;
  }

  public void setNombreCompletoReferido(String nombreCompletoReferido) {
    this.nombreCompletoReferido = nombreCompletoReferido;
  }

  public String getNbrTarjetaReferido() {
    return nbrTarjetaReferido;
  }

  public void setNbrTarjetaReferido(String nbrTarjetaReferido) {
    this.nbrTarjetaReferido = nbrTarjetaReferido;
  }

  public String getTipoTarjetaReferido() {
    return tipoTarjetaReferido;
  }

  public void setTipoTarjetaReferido(String tipoTarjetaReferido) {
    this.tipoTarjetaReferido = tipoTarjetaReferido;
  }

  public String getLineaReferido() {
    return lineaReferido;
  }

  public void setLineaReferido(String lineaReferido) {
    this.lineaReferido = lineaReferido;
  }

  public String getTasaReferido() {
    return tasaReferido;
  }

  public void setTasaReferido(String tasaReferido) {
    this.tasaReferido = tasaReferido;
  }

  public String getCorreoReferido() {
    return correoReferido;
  }

  public void setCorreoReferido(String correoReferido) {
    this.correoReferido = correoReferido;
  }

  public String getFechaLeadGenerado() {
    return fechaLeadGenerado;
  }

  public void setFechaLeadGenerado(String fechaLeadGenerado) {
    this.fechaLeadGenerado = fechaLeadGenerado;
  }

  public String getHistoricoEvaluacion() {
    return historicoEvaluacion;
  }

  public void setHistoricoEvaluacion(String historicoEvaluacion) {
    this.historicoEvaluacion = historicoEvaluacion;
  }

  public String getCampaniaReferido() {
    return campaniaReferido;
  }

  public void setCampaniaReferido(String campaniaReferido) {
    this.campaniaReferido = campaniaReferido;
  }

  public String getCampaniaReferidor() {
    return campaniaReferidor;
  }

  public void setCampaniaReferidor(String campaniaReferidor) {
    this.campaniaReferidor = campaniaReferidor;
  }

  public String getHistoricoObservacion() {
    return historicoObservacion;
  }

  public void setHistoricoObservacion(String historicoObservacion) {
    this.historicoObservacion = historicoObservacion;
  }

  public String getEstadoReferido() {
    return estadoReferido;
  }

  public void setEstadoReferido(String estadoReferido) {
    this.estadoReferido = estadoReferido;
  }

  public String getPasoMotor() {
    return pasoMotor;
  }

  public void setPasoMotor(String pasoMotor) {
    this.pasoMotor = pasoMotor;
  }

  public String getMultibinReferido() {
    return multibinReferido;
  }

  public void setMultibinReferido(String multibinReferido) {
    this.multibinReferido = multibinReferido;
  }

  public String getFechaRegistro() {
    return fechaRegistro;
  }

  public void setFechaRegistro(String fechaRegistro) {
    this.fechaRegistro = fechaRegistro;
  }

  public String getFechaEvaluacion() {
    return fechaEvaluacion;
  }

  public void setFechaEvaluacion(String fechaEvaluacion) {
    this.fechaEvaluacion = fechaEvaluacion;
  }

  public String getOrigenReferido() {
    return origenReferido;
  }

  public void setOrigenReferido(String origenReferido) {
    this.origenReferido = origenReferido;
  }

  public String getObservacionPremioReferido() {
    return observacionPremioReferido;
  }

  public void setObservacionPremioReferido(String observacionPremioReferido) {
    this.observacionPremioReferido = observacionPremioReferido;
  }

  public String getNroContratoTablon() {
    return nroContratoTablon;
  }

  public void setNroContratoTablon(String nroContratoTablon) {
    this.nroContratoTablon = nroContratoTablon;
  }

  public String getNroTarjetaTablon() {
    return nroTarjetaTablon;
  }

  public void setNroTarjetaTablon(String nroTarjetaTablon) {
    this.nroTarjetaTablon = nroTarjetaTablon;
  }

  public String getFechaContratacionTablon() {
    return fechaContratacionTablon;
  }

  public void setFechaContratacionTablon(String fechaContratacionTablon) {
    this.fechaContratacionTablon = fechaContratacionTablon;
  }

  public String getFechaActivacionTablon() {
    return fechaActivacionTablon;
  }

  public void setFechaActivacionTablon(String fechaActivacionTablon) {
    this.fechaActivacionTablon = fechaActivacionTablon;
  }

  public String getBinTablon() {
    return binTablon;
  }

  public void setBinTablon(String binTablon) {
    this.binTablon = binTablon;
  }
}
