package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import java.io.Serializable;
import lombok.Data;

@Entity
@Data
@Table(name = "AFILIACION_EECC_FFMM_DETALLE")
public class AfiliacionEECCFFMMDetalle implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_AFILIACION_EECC_FFMM_DETALLE")
  @TableGenerator(
      name = "SEQ_AFILIACION_EECC_FFMM_DETALLE",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.AfiliacionEECCFFMMDetalle",
      allocationSize = 1)
  private Long id;

  @Column(name = "NOMBRE_FONDO", length = 50)
  private String nombreFondo;

  @Column(name = "NUMERO_CONTRATO", length = 20)
  private String numeroContrato;

  @ManyToOne
  @JoinColumn(name = "AFILIACION_EECC_FFMM")
  private AfiliacionEECCFFMM afiliacionEECCFFMM;
}
