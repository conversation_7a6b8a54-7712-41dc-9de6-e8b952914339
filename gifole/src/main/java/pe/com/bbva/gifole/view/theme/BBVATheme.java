package pe.com.bbva.gifole.view.theme;

/** Constantes de tema BBVA con la paleta de colores oficial */
public final class BBVATheme {

  // Colores BBVA
  public static final String BBVA_NAVY = "#072146"; // Dark blue from BBVA logo
  public static final String BBVA_BLUE = "#004481"; // Primary blue from BBVA
  public static final String BBVA_LIGHT_BLUE = "#1973B8"; // Secondary blue
  public static final String BBVA_AQUA = "#02A5A5"; // Teal/aqua accent
  public static final String BBVA_WHITE = "#FFFFFF"; // White
  public static final String BBVA_GOLD = "#F8CD51"; // Gold accent from the star icon

  // CSS Custom Properties para Vaadin
  public static final String CSS_BBVA_NAVY = "var(--bbva-navy)";
  public static final String CSS_BBVA_BLUE = "var(--bbva-blue)";
  public static final String CSS_BBVA_LIGHT_BLUE = "var(--bbva-light-blue)";
  public static final String CSS_BBVA_AQUA = "var(--bbva-aqua)";
  public static final String CSS_BBVA_WHITE = "var(--bbva-white)";
  public static final String CSS_BBVA_GOLD = "var(--bbva-gold)";

  // Clases CSS de fondo
  public static final String BBVA_PRIMARY = "bbva-primary";
  public static final String BBVA_SECONDARY = "bbva-secondary";
  public static final String BBVA_ACCENT = "bbva-accent";
  public static final String BBVA_BACKGROUND = "bbva-background";
  public static final String BBVA_CARD = "bbva-card";

  // Clases CSS de texto
  public static final String BBVA_TEXT_PRIMARY = "bbva-text-primary";
  public static final String BBVA_TEXT_SECONDARY = "bbva-text-secondary";
  public static final String BBVA_TEXT_NAVY = "bbva-text-navy";
  public static final String BBVA_TEXT_AQUA = "bbva-text-aqua";
  public static final String BBVA_TEXT_GOLD = "bbva-text-gold";

  // Clases CSS de botones
  public static final String BBVA_BUTTON_PRIMARY = "bbva-button-primary";
  public static final String BBVA_BUTTON_SECONDARY = "bbva-button-secondary";

  private BBVATheme() {
    // Utility class
  }
}
