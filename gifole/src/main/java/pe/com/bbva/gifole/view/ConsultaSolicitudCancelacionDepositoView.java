package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.CancelacionProductoDetalleBean; // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.util.GifoleUtil; // Asumo que existe, ajusta según tu proyecto
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Solicitud Cancelación Depósito")
@Route(value = "reporte/solicitudes/cancelacion-deposito", layout = MainLayout.class)
public class ConsultaSolicitudCancelacionDepositoView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA_HORA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private DataTable<CancelacionProductoDetalleBean> dataTable;

    public ConsultaSolicitudCancelacionDepositoView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Solicitud Cancelación Depósito");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<CancelacionProductoDetalleBean>builder()
                .id("tabla-solicitud-cancelacion-deposito") // ID único para la tabla
                // Mapeo de columnas basado en la información proporcionada
                .column("codigoCentral", "Código Central", // Equivalente parcial a "numeroSolicitud"
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getCodigoCentral() : ""), "120px")
                .column("nombresApellidos", "Cliente", // Equivalente a "cliente" (no en DTO original, pero útil)
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getNombreCompletoCliente() : ""), "250px")
                .column("tipoDocumento", "Tipo Documento", // Parte de identificación del cliente
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getTipoDocumento() : ""), "120px")
                .column("numeroDocumento", "Número Documento", // Parte de identificación del cliente
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getNroDocumento() : ""), "120px")
                .column("fechaHoraRegistro", "Fecha Solicitud", // Equivalente a "fechaSolicitud"
                    bean -> bean.getCreacion() != null ? FORMATO_FECHA_HORA.format(bean.getCreacion()) : "", "170px")
                .column("subProducto", "Tipo Depósito", // Equivalente a "tipoDeposito"
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getSubProducto() : ""), "150px")
                 .column("nroContrato", "Número Depósito", // Equivalente a "numeroDeposito" (formateada)
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        GifoleUtil.obtenerNumeroCuentaFormateado(
                            bean.getCancelacionProductoBean().getNroContrato()) : ""), "170px")
                 .column("monto", "Monto", // Asumiendo que montoPrestamo o un campo similar existe
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        // Ajusta el nombre del getter si es diferente, ej: getMontoDeposito()
                        bean.getCancelacionProductoBean().getMontoPrestamo() : ""), "120px")
                .column("motivo", "Motivo", // Combinación de motivos de rechazo
                    bean -> {
                        if (bean.getCancelacionProductoBean() != null) {
                            String motivo1 = StringUtils.trimToEmpty(bean.getCancelacionProductoBean().getMotivoRechazo());
                            String motivo2 = StringUtils.trimToEmpty(bean.getCancelacionProductoBean().getOtroMotivoRechazo());
                            if (!motivo2.isEmpty()) {
                                return motivo1 + (motivo1.isEmpty() ? "" : " - ") + motivo2;
                            }
                            return motivo1;
                        }
                        return "";
                    }, "200px")
                .column("estado", "Estado", // Equivalente a "estado"
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getEstado() : ""), "120px")
                .column("canal", "Canal", // Información del canal
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getCanal() : ""), "100px")
                .column("correo", "Correo", // Información de contacto
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getCorreoCliente() : ""), "200px")
                .column("celularCliente", "Celular Cliente", // Información de contacto
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getCelularCliente() : ""), "120px")
                .column("registroModificacion", "Usuario Aprobación", // Equivalente a "usuarioAprobacion"
                    bean -> StringUtils.trimToEmpty(bean.getEditor()), "150px") // Del DetalleBean
                .column("fechaHoraModificacion", "Fecha Modificación",
                    bean -> bean.getEdicion() != null ? FORMATO_FECHA_HORA.format(bean.getEdicion()) : "", "170px") // Del DetalleBean
                // Puedes agregar más columnas aquí si las necesitas, siguiendo el mismo patrón
                // Datos (inicialmente vacío)
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}