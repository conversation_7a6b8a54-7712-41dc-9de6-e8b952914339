package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.CotizacionSeguroVehicularBean;

@SuppressWarnings("rawtypes")
@Component
public class CotizacionVehicularMapper implements RowMapper {

  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    CotizacionSeguroVehicularBean param = new CotizacionSeguroVehicularBean();

    param.setId(rs.getLong("ID"));
    param.setFechaRegistro(rs.getDate("FECHA_REGISTRO"));
    param.setTelefono(rs.getString("TELEFONO"));
    param.setHorarioContacto(rs.getString("HORARIOCONTACTO"));
    param.setNroCotizacion(rs.getString("NRO_COTIZACION"));
    param.setPlanElegido(rs.getString("PLAN_ELEGIDO"));
    param.setDivisaPlanElegido(rs.getString("DIVISA_PLAN_ELEGIDO"));
    param.setMontoPlanElegido(rs.getString("MONTO_PLAN_ELEGIDO"));
    param.setCanal(rs.getString("CANAL"));

    return param;
  }
}
