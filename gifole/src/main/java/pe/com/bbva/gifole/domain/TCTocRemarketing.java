package pe.com.bbva.gifole.domain;

import java.util.Date;
import java.util.List;

public class TCTocRemarketing {

  private Long id;
  private String codigoCentral;
  private String tipoDocumento;
  private String numeroDocumento;
  private String nombreCompleto;
  private String telefono;
  private String correo;
  private Date fechaRegistro;
  private String tipoTarjeta;
  private String canal;
  private String montoLimite;
  private String nombreTarjeta;
  private String tcea;
  private String tea;
  private String fechaPago;
  private String estado;
  private String tipoInformacion;
  private List<TCTocRemarketingOferta> listaTarjetaOneClickRemarketing;
  private String pasoAbandono;
  private String marcaPh;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public String getNumeroDocumento() {
    return numeroDocumento;
  }

  public void setNumeroDocumento(String numeroDocumento) {
    this.numeroDocumento = numeroDocumento;
  }

  public String getNombreCompleto() {
    return nombreCompleto;
  }

  public void setNombreCompleto(String nombreCompleto) {
    this.nombreCompleto = nombreCompleto;
  }

  public String getTelefono() {
    return telefono;
  }

  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public Date getFechaRegistro() {
    return fechaRegistro;
  }

  public void setFechaRegistro(Date fechaRegistro) {
    this.fechaRegistro = fechaRegistro;
  }

  public String getTipoTarjeta() {
    return tipoTarjeta;
  }

  public void setTipoTarjeta(String tipoTarjeta) {
    this.tipoTarjeta = tipoTarjeta;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public List<TCTocRemarketingOferta> getListaTarjetaOneClickRemarketing() {
    return listaTarjetaOneClickRemarketing;
  }

  public void setListaTarjetaOneClickRemarketing(
      List<TCTocRemarketingOferta> listaTarjetaOneClickRemarketing) {
    this.listaTarjetaOneClickRemarketing = listaTarjetaOneClickRemarketing;
  }

  public String getMontoLimite() {
    return montoLimite;
  }

  public void setMontoLimite(String montoLimite) {
    this.montoLimite = montoLimite;
  }

  public String getNombreTarjeta() {
    return nombreTarjeta;
  }

  public void setNombreTarjeta(String nombreTarjeta) {
    this.nombreTarjeta = nombreTarjeta;
  }

  public String getTcea() {
    return tcea;
  }

  public void setTcea(String tcea) {
    this.tcea = tcea;
  }

  public String getTea() {
    return tea;
  }

  public void setTea(String tea) {
    this.tea = tea;
  }

  public String getFechaPago() {
    return fechaPago;
  }

  public void setFechaPago(String fechaPago) {
    this.fechaPago = fechaPago;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public String getTipoInformacion() {
    return tipoInformacion;
  }

  public void setTipoInformacion(String tipoInformacion) {
    this.tipoInformacion = tipoInformacion;
  }

  public String getPasoAbandono() {
    return pasoAbandono;
  }

  public void setPasoAbandono(String pasoAbandono) {
    this.pasoAbandono = pasoAbandono;
  }

  public String getMarcaPh() {
    return marcaPh;
  }

  public void setMarcaPh(String marcaPh) {
    this.marcaPh = marcaPh;
  }
}
