package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import org.hibernate.annotations.Formula;

@Data
@Entity
@Table(name = "TC_TOC")
public class TCToc implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SeqGen")
  @SequenceGenerator(name = "SeqGen", sequenceName = "SQ_TC_TOC", allocationSize = 1)
  @Column(name = "ID")
  private Long id;

  @Column(name = "NOMBRES", length = 40)
  private String nombres;

  @Column(name = "APELLIDO_PATERNO", length = 40)
  private String paterno;

  @Column(name = "APELLIDO_MATERNO", length = 40)
  private String materno;

  @Column(name = "COD_CENTRAL", length = 8)
  private String codigoCentral;

  @Column(name = "COD_RESERVA_MOI", length = 30)
  private String codreservaMoi;

  @Column(name = "TARJETA", length = 40)
  private String tarjeta;

  @Column(name = "TIPO_TARJETA", length = 20)
  private String tipoTarjeta;

  @Column(name = "PROGRAMA_BENEF", length = 40)
  private String programaBenef;

  @Column(name = "CUENTA_CARGO", length = 20)
  private String cuentaCargo;

  @Column(name = "FECHA_PAGO", length = 20)
  private String fechaPago;

  @Column(name = "ENVIO_EC", length = 250)
  private String envioEc;

  @Column(name = "DIRECCION", length = 250)
  private String direccion;

  @Column(name = "TELEFONO", length = 9)
  private String telefono;

  @Column(name = "CORREO", length = 80)
  private String correo;

  @Column(name = "NRO_CONTRATO", length = 20)
  private String nroContrato;

  @Column(name = "NRO_TARJETA", length = 16)
  private String nroTarjeta;

  @Column(name = "LOCALIDAD", length = 10)
  private String localidad;

  @Column(name = "VALORACION")
  private Integer valoracion;

  @OneToOne
  @JoinColumn(name = "ESTADO_ACTUAL")
  private TCEstado estadoActual;

  @Column(name = "FECHA_REGISTRO")
  private Date fechaRegistro;

  @Column(name = "FECHA_MODIFICACION")
  private Date fechaModificacion;

  @Column(name = "CREADOR", length = 10)
  private String creador;

  @Column(name = "EDITOR", length = 10)
  private String editor;

  @Transient private Date fechaRegistro1;

  @Transient private Date fechaRegistro2;

  @Formula(value = "NOMBRES || ' ' || APELLIDO_PATERNO || ' ' || APELLIDO_MATERNO")
  private String nombreCompleto;

  @Column(name = "CANAL", length = 8)
  private String canal;

  @Transient private String estado;

  @Column(name = "LINEA_CREDITO", length = 12)
  private String linea;

  @OneToOne
  @JoinColumn(name = "TC_ADICIONAL_DETALLE")
  private TCAdicionalDetalle adicionalDetalle;

  @Transient private String combo;

  @Transient private String fechaModificacionFormat;

  @Column(name = "OFICINA_SOLICITANTE", length = 250)
  private String oficinaSolicitante;

  @Column(name = "EJECUTIVO", length = 100)
  private String ejecutivo;

  @Column(name = "OFICINA_RECOJO", length = 300)
  private String oficinaRecojo;

  @Column(name = "CORREO_EJECUTIVO", length = 100)
  private String correoEjecutivo;

  @Column(name = "PASAJERO_FRECUENTE", length = 11)
  private String pasajeroFrecuente;

  @Column(name = "BIN")
  private String bin;

  @Column(name = "MULTIBIN")
  private String multibin;

  @Column(name = "OFERTA_DATAZO", length = 30)
  private String ofertaDatazo;

  @Transient private String valorMotivo;

  @Column(name = "NRO_SEGURO", length = 20)
  private String nroSeguro;

  @Column(name = "FLAG_PROCESAR")
  private Integer flagProcesar;

  @Column(name = "TEA")
  private String tea;

  @Column(name = "TIPO_DOCUMENTO")
  private String tipoDocumento;

  @Column(name = "NRO_DOCUMENTO")
  private String nroDocumento;

  @Column(name = "FLAG_PROCESO_COY")
  private Integer flagProcesarCoy;

  @Column(name = "FLAG_INNOMINADA")
  private Integer flagInnominada;

  @Column(name = "CUOTA_FIJA")
  private String cuotaFija;

  @Column(name = "FLAG_NOTIFICACIONES")
  private String flagNotificaciones;

  @Column(name = "MARCA_PH")
  private Integer marcaPh;

  @Column(name = "DISPOSICION_EFECTIVO")
  private String disposicionEfectivo;

  @Transient private String tipoGarantia;
}
