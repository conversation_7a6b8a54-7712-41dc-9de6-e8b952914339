package pe.com.bbva.gifole.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
@PropertySource(
    value = "file:/opt/apps/gifole/properties/ldap_gifole.properties",
    ignoreResourceNotFound = true)
public class PropertiesExterno {

  private final Environment env;

  @Autowired
  public PropertiesExterno(Environment env) {
    this.env = env;
  }

  public String getProperty(String key) {
    return env.getProperty(key);
  }
}
