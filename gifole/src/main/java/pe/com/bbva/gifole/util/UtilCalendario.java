package pe.com.bbva.gifole.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;
import pe.com.bbva.gifole.common.enums.PatronFechaEnum;

public class UtilCalendario {

  public static Date obtenerFechaActualPeru() {
    Date fechaActual;
    try {
      SimpleDateFormat simpleDateFormatPeru =
          new SimpleDateFormat(PatronFechaEnum.FECHA_HORA_MINUTO_SEGUNDO.getPatron());
      fechaActual = simpleDateFormatPeru.parse(obtenerCadenaFechaActualPeru());
    } catch (ParseException e) {
      fechaActual = new Date();
    }
    return fechaActual;
  }

  public static String obtenerCadenaFechaActualPeru() {
    Calendar calendarActual = GregorianCalendar.getInstance();
    SimpleDateFormat simpleDateFormatPeru =
        new SimpleDateFormat(PatronFechaEnum.FECHA_HORA_MINUTO_SEGUNDO.getPatron());
    simpleDateFormatPeru.setTimeZone(TimeZone.getTimeZone("America/Lima"));

    String cadenaFecha = simpleDateFormatPeru.format(calendarActual.getTime());
    return cadenaFecha;
  }

  public static String obtenerCadenaFechaActualPeru(PatronFechaEnum formatoFecha) {
    Calendar calendarActual = GregorianCalendar.getInstance();
    SimpleDateFormat simpleDateFormatPeru = new SimpleDateFormat(formatoFecha.getPatron());
    simpleDateFormatPeru.setTimeZone(TimeZone.getTimeZone("America/Lima"));

    String cadenaFecha = simpleDateFormatPeru.format(calendarActual.getTime());
    return cadenaFecha;
  }

  public static Date transformarCadenaAFecha(String cadena, String patron) {
    try {
      SimpleDateFormat sdf = new SimpleDateFormat(patron);

      return sdf.parse(cadena);
    } catch (Exception e) {
      return null;
    }
  }

  public static String obtenerFechaConMasMenosDias(
      PatronFechaEnum formatoFecha, String nroDias, String operacion) {
    ZonedDateTime zdtAhora = ZonedDateTime.now(ZoneId.of("America/Lima"));
    DateTimeFormatter dtfFormateadorFechaArchivo =
        DateTimeFormatter.ofPattern(formatoFecha.getPatron());
    String sFechaArchivo = dtfFormateadorFechaArchivo.format(zdtAhora);

    if (operacion.equals(Constante.DATOS_GENERALES.PLUS)) {
      sFechaArchivo = dtfFormateadorFechaArchivo.format(zdtAhora.plusDays(Long.valueOf(nroDias)));
    } else if (operacion.equals(Constante.DATOS_GENERALES.MINUS)) {
      sFechaArchivo = dtfFormateadorFechaArchivo.format(zdtAhora.minusDays(Long.valueOf(nroDias)));
    }
    return sFechaArchivo;
  }
}
