package pe.com.bbva.gifole.view.components;

import com.vaadin.flow.component.Component;
import com.vaadin.flow.component.datepicker.DatePicker;
import com.vaadin.flow.component.html.Div;
import com.vaadin.flow.component.html.H3;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.NumberField;
import com.vaadin.flow.component.textfield.TextField;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * FormCard - Componente reutilizable para crear formularios en tarjetas Organiza inputs en filas
 * según la configuración especificada
 */
public class FormCard extends VerticalLayout {

  private final Map<String, Component> inputComponents;
  private final List<InputDefinition> inputDefinitions;
  private final int inputsPerRow;
  private final String title;

  /**
   * Constructor del FormCard
   *
   * @param title Título de la tarjeta
   * @param inputDefinitions Lista de definiciones de inputs
   * @param inputsPerRow Número de inputs por fila
   */
  public FormCard(String title, List<InputDefinition> inputDefinitions, int inputsPerRow) {
    this.title = title;
    this.inputDefinitions = new ArrayList<>(inputDefinitions);
    this.inputsPerRow = inputsPerRow;
    this.inputComponents = new LinkedHashMap<>();

    initializeCard();
  }

  /** Inicializa la tarjeta con estilos y componentes */
  private void initializeCard() {
    // Configurar el contenedor principal
    setSizeFull();
    setSpacing(true);
    setPadding(true);
    addClassName("bbva-form-card");

    // Crear y agregar el título si existe
    if (title != null && !title.trim().isEmpty()) {
      H3 cardTitle = new H3(title);
      cardTitle.addClassName("form-card-title");
      add(cardTitle);
    }

    // Crear el contenedor de inputs
    VerticalLayout inputsContainer = new VerticalLayout();
    inputsContainer.setSpacing(true);
    inputsContainer.setPadding(false);
    inputsContainer.addClassName("form-inputs-container");

    // Organizar inputs en filas
    createInputRows(inputsContainer);

    add(inputsContainer);
  }

  /** Crea las filas de inputs según la configuración */
  private void createInputRows(VerticalLayout container) {
    List<InputDefinition> remainingInputs = new ArrayList<>(inputDefinitions);

    while (!remainingInputs.isEmpty()) {
      HorizontalLayout row = new HorizontalLayout();
      row.setWidthFull();
      row.setSpacing(true);
      row.setAlignItems(FlexComponent.Alignment.END);
      row.addClassName("form-input-row");

      // Agregar inputs a la fila actual
      int inputsInThisRow = Math.min(inputsPerRow, remainingInputs.size());
      for (int i = 0; i < inputsInThisRow; i++) {
        InputDefinition inputDef = remainingInputs.remove(0);
        Component inputComponent = createInputComponent(inputDef);
        inputComponents.put(inputDef.getKey(), inputComponent);

        // Envolver el input en un contenedor para mejor control
        Div inputWrapper = new Div();
        inputWrapper.addClassName("form-input-wrapper");
        inputWrapper.setWidthFull();
        inputWrapper.add(inputComponent);

        row.add(inputWrapper);
        row.setFlexGrow(1, inputWrapper);
      }

      container.add(row);
    }
  }

  /** Crea un componente de input según su tipo */
  private Component createInputComponent(InputDefinition inputDef) {
    Component component;

    switch (inputDef.getType()) {
      case TEXT:
        TextField textField = new TextField(inputDef.getLabel());
        textField.setPlaceholder(inputDef.getPlaceholder());
        textField.setValue(
            inputDef.getDefaultValue() != null ? inputDef.getDefaultValue().toString() : "");
        if (inputDef.isRequired()) {
          textField.setRequired(true);
        }
        component = textField;
        break;

      case NUMBER:
        NumberField numberField = new NumberField(inputDef.getLabel());
        numberField.setPlaceholder(inputDef.getPlaceholder());
        if (inputDef.getDefaultValue() instanceof Number) {
          numberField.setValue(((Number) inputDef.getDefaultValue()).doubleValue());
        }
        if (inputDef.isRequired()) {
          numberField.setRequired(true);
        }
        component = numberField;
        break;

      case DATE:
        DatePicker datePicker = new DatePicker(inputDef.getLabel());
        datePicker.setPlaceholder(inputDef.getPlaceholder());
        if (inputDef.getDefaultValue() instanceof LocalDate) {
          datePicker.setValue((LocalDate) inputDef.getDefaultValue());
        }
        if (inputDef.isRequired()) {
          datePicker.setRequired(true);
        }
        component = datePicker;
        break;

      default:
        // Fallback a TextField
        TextField defaultField = new TextField(inputDef.getLabel());
        defaultField.setPlaceholder(inputDef.getPlaceholder());
        component = defaultField;
        break;
    }

    // Aplicar estilos comunes
    component.addClassName("form-input");
    component.getElement().setAttribute("data-key", inputDef.getKey());

    return component;
  }

  /** Obtiene los valores de todos los inputs como un Map */
  public Map<String, Object> getValues() {
    Map<String, Object> values = new HashMap<>();

    for (Map.Entry<String, Component> entry : inputComponents.entrySet()) {
      String key = entry.getKey();
      Component component = entry.getValue();
      Object value = extractValueFromComponent(component);
      values.put(key, value);
    }

    return values;
  }

  /** Extrae el valor de un componente según su tipo */
  private Object extractValueFromComponent(Component component) {
    if (component instanceof TextField) {
      return ((TextField) component).getValue();
    } else if (component instanceof NumberField) {
      return ((NumberField) component).getValue();
    } else if (component instanceof DatePicker) {
      return ((DatePicker) component).getValue();
    }
    return null;
  }

  /** Establece valores en los inputs */
  public void setValues(Map<String, Object> values) {
    for (Map.Entry<String, Object> entry : values.entrySet()) {
      String key = entry.getKey();
      Object value = entry.getValue();
      Component component = inputComponents.get(key);

      if (component != null) {
        setValueToComponent(component, value);
      }
    }
  }

  /** Establece un valor en un componente específico */
  private void setValueToComponent(Component component, Object value) {
    if (component instanceof TextField && value instanceof String) {
      ((TextField) component).setValue((String) value);
    } else if (component instanceof NumberField && value instanceof Number) {
      ((NumberField) component).setValue(((Number) value).doubleValue());
    } else if (component instanceof DatePicker && value instanceof LocalDate) {
      ((DatePicker) component).setValue((LocalDate) value);
    }
  }

  /** Limpia todos los inputs */
  public void clearValues() {
    for (Component component : inputComponents.values()) {
      if (component instanceof TextField) {
        ((TextField) component).clear();
      } else if (component instanceof NumberField) {
        ((NumberField) component).clear();
      } else if (component instanceof DatePicker) {
        ((DatePicker) component).clear();
      }
    }
  }

  /** Valida todos los inputs requeridos */
  public boolean isValid() {
    for (Component component : inputComponents.values()) {
      if (component instanceof TextField) {
        TextField field = (TextField) component;
        if (field.isRequired() && (field.getValue() == null || field.getValue().trim().isEmpty())) {
          return false;
        }
      } else if (component instanceof NumberField) {
        NumberField field = (NumberField) component;
        if (field.isRequired() && field.getValue() == null) {
          return false;
        }
      } else if (component instanceof DatePicker) {
        DatePicker field = (DatePicker) component;
        if (field.isRequired() && field.getValue() == null) {
          return false;
        }
      }
    }
    return true;
  }

  /** Definición de un input */
  public static class InputDefinition {
    private final String key;
    private final String label;
    private final InputType type;
    private String placeholder = "";
    private Object defaultValue;
    private boolean required = false;

    public InputDefinition(String key, String label, InputType type) {
      this.key = key;
      this.label = label;
      this.type = type;
    }

    public InputDefinition placeholder(String placeholder) {
      this.placeholder = placeholder;
      return this;
    }

    public InputDefinition defaultValue(Object defaultValue) {
      this.defaultValue = defaultValue;
      return this;
    }

    public InputDefinition required(boolean required) {
      this.required = required;
      return this;
    }

    public InputDefinition required() {
      return required(true);
    }

    // Getters
    public String getKey() {
      return key;
    }

    public String getLabel() {
      return label;
    }

    public InputType getType() {
      return type;
    }

    public String getPlaceholder() {
      return placeholder;
    }

    public Object getDefaultValue() {
      return defaultValue;
    }

    public boolean isRequired() {
      return required;
    }
  }

  /** Tipos de input disponibles */
  public enum InputType {
    TEXT,
    NUMBER,
    DATE
  }

  /** Builder para crear FormCard de forma fluida */
  public static class Builder {
    private String title;
    private final List<InputDefinition> inputs = new ArrayList<>();
    private int inputsPerRow = 2;

    public Builder title(String title) {
      this.title = title;
      return this;
    }

    public Builder inputsPerRow(int inputsPerRow) {
      this.inputsPerRow = Math.max(1, inputsPerRow);
      return this;
    }

    public Builder input(String key, String label, InputType type) {
      inputs.add(new InputDefinition(key, label, type));
      return this;
    }

    public Builder textInput(String key, String label) {
      return input(key, label, InputType.TEXT);
    }

    public Builder numberInput(String key, String label) {
      return input(key, label, InputType.NUMBER);
    }

    public Builder dateInput(String key, String label) {
      return input(key, label, InputType.DATE);
    }

    public Builder addInput(InputDefinition inputDefinition) {
      inputs.add(inputDefinition);
      return this;
    }

    public FormCard build() {
      return new FormCard(title, inputs, inputsPerRow);
    }
  }

  /** Crea un nuevo builder */
  public static Builder builder() {
    return new Builder();
  }
}
