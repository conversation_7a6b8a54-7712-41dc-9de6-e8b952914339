package pe.com.bbva.gifole.common.enums;

public enum PatronFechaEnum {
  ANIO_MES_DIA("yyyy-MM-dd"),
  ANIO_MES_DIA_2("yy/MM/dd"),
  DIA_MES_ANIO("dd-MM-yyyy"),
  DIA_MES_ANIO_UNIDO("ddMMyyyy"),
  DIA_MES_ANIO_UNIDO_ABREV("ddMMyy"),
  PERIODO("yyyyMM"),
  FECHA_TIEMPO("yyyy-MM-dd hh:mm:ss"),
  FECHA_DEFECTO("dd/MM/yyyy"),
  ZONA_HORARIA("yyyy-MM-dd'T'HH:mm:ss"),
  FECHA_HORA_MINUTO("dd/MM/yyyy hh:mm"),
  FECHA_HORA_MINUTO_SEGUNDO("dd/MM/yyyy HH:mm:ss"),
  FECHA_HORA_MINUTO_SEGUNDO_SQL("dd/MM/yyyy hh24:mi:ss"),
  HORA_24("HH:mm"),
  FECHA_24H_HORA_MINUTO("dd/MM/yyyy HH:mm"),
  FECHA_COMPLETA("dd 'de' MMMM 'del' yyyy"),
  FECHA_COMPLETA_24H_HORA_MINUTO("dd 'de' MMMM 'del' yyyy '-' hh:mm a"),
  FECHA_ANIO_CORTO_HORA_MINUTO_SEGUNDO("dd/MM/yy HH:mm:ss"),
  FECHA_DIA_MES_24H_HORA_MINUTO("dd/MM HH:mm"),
  ANIO_MES_DIA_NO_SEPARACION("yyyyMMdd"),
  FORMATO_FECHA_EXPORTAR("dd/MM/yyyy_HHmmss"),
  ANIO_MES_DIA_HORA_MINUTO_SEG("yyyyMMddHHmmss");

  private final String patron;

  private PatronFechaEnum(String patron) {
    this.patron = patron;
  }

  public String getPatron() {
    return patron;
  }
}
