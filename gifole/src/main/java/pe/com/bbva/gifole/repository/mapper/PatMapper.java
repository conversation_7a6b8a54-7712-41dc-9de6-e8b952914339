package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.PrestamoAlToqueBean;

@Component
public class PatMapper implements RowMapper<PrestamoAlToqueBean> {

  @Override
  public PrestamoAlToqueBean mapRow(ResultSet rs, int i) throws SQLException {

    PrestamoAlToqueBean prestamoAlToqueBean = new PrestamoAlToqueBean();
    prestamoAlToqueBean.setId(rs.getLong("ID"));
    prestamoAlToqueBean.setCodigoCentral(rs.getString("CODIGO_CENTRAL"));
    prestamoAlToqueBean.setTipoDocumento(rs.getString("TIPO_DOCUMENTO"));
    prestamoAlToqueBean.setNumeroDocumento(rs.getString("NUMERO_DOCUMENTO"));
    prestamoAlToqueBean.setNombreCompleto(rs.getString("NOMBRE_COMPLETO"));
    prestamoAlToqueBean.setTelefono(rs.getString("TELEFONO"));
    prestamoAlToqueBean.setCorreo(rs.getString("CORREO"));
    prestamoAlToqueBean.setMonto(rs.getString("MONTO"));
    prestamoAlToqueBean.setCuota(rs.getString("CUOTA"));
    prestamoAlToqueBean.setPlazo(rs.getString("PLAZO"));
    prestamoAlToqueBean.setTea(rs.getString("TEA"));
    prestamoAlToqueBean.setTcea(rs.getString("TCEA"));
    prestamoAlToqueBean.setSimulacion(rs.getString("SIMULACION"));
    prestamoAlToqueBean.setFechaRegistro(rs.getTimestamp("FECHA_REGISTRO"));
    prestamoAlToqueBean.setEstado(rs.getString("ESTADO"));
    prestamoAlToqueBean.setCanal(rs.getString("CANAL"));
    prestamoAlToqueBean.setNumeroContrato(rs.getString("NUMERO_CONTRATO"));

    String marcaPh = StringUtils.trimToEmpty(rs.getString("MARCA_PH"));
    if (marcaPh.equalsIgnoreCase("1")) {
      marcaPh = "SI";
    } else {
      marcaPh = "NO";
    }

    prestamoAlToqueBean.setMarcaPh(marcaPh);
    prestamoAlToqueBean.setPasoAbandono(rs.getString("PASO_ABANDONO"));

    return prestamoAlToqueBean;
  }
}
