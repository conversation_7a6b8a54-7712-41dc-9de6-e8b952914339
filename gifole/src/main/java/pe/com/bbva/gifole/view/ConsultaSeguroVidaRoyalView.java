package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.SeguroVidaRoyalBean;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Seguro Vida Royal")
@Route(value = "reporte/seguros/consulta-seguro-vida-royal", layout = MainLayout.class)
public class ConsultaSeguroVidaRoyalView extends VerticalLayout {

  public ConsultaSeguroVidaRoyalView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Seguro Vida Royal");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    DataTable<SeguroVidaRoyalBean> dataTable =
        DataTable.<SeguroVidaRoyalBean>builder()
            .id("tabla-seguro-vida")
            .column("fechaRegistro", "Fecha Registro", bean -> bean.getFechaRegistro(), "150px")
            .column("nombre", "Nombre", bean -> StringUtils.trimToEmpty(bean.getNombre()), "150px")
            .column(
                "apellido",
                "Apellido",
                bean -> StringUtils.trimToEmpty(bean.getApellido()),
                "150px")
            .column(
                "tipoDocumento",
                "Tipo Documento",
                bean -> StringUtils.trimToEmpty(bean.getTipoDocumento()),
                "120px")
            .column(
                "documento",
                "Número de Documento",
                bean -> StringUtils.trimToEmpty(bean.getDocumento()),
                "130px")
            .column(
                "indicadorSiEsCliente",
                "¿Es Cliente?",
                bean -> StringUtils.trimToEmpty(bean.getIndicadorSiEsCliente()),
                "100px")
            .column("correo", "Correo", bean -> StringUtils.trimToEmpty(bean.getCorreo()), "200px")
            .column(
                "telefono",
                "Teléfono",
                bean -> StringUtils.trimToEmpty(bean.getTelefono()),
                "120px")
            .column(
                "codigoOficina",
                "Código Oficina",
                bean -> StringUtils.trimToEmpty(bean.getCodigoOficina()),
                "120px")
            .column(
                "codigoUsuario",
                "Código Usuario",
                bean -> StringUtils.trimToEmpty(bean.getCodigoUsuario()),
                "120px")
            .column(
                "horarioContacto",
                "Horario de Contacto",
                bean -> StringUtils.trimToEmpty(bean.getHorarioContacto()),
                "140px")
            .column("canal", "Canal", bean -> StringUtils.trimToEmpty(bean.getCanal()), "100px")
            .column(
                "indicadorProcesado",
                "Indicador Procesado",
                bean -> StringUtils.trimToEmpty(bean.getIndicadorProcesado()),
                "130px")
            .column(
                "numeroContrato",
                "Número de Contrato",
                bean -> StringUtils.trimToEmpty(bean.getNroContrato()),
                "130px")
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
