package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.domain.BeneficioTcCu;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Beneficios")
@Route(value = "reporte/beneficios/consulta", layout = MainLayout.class)
public class ConsultaBeneficioView extends VerticalLayout {

  public ConsultaBeneficioView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Beneficios");
    title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);

    // Cargar datos de ejemplo (opcional)
    // loadSampleData();
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder, similar a las vistas anteriores
    DataTable<BeneficioTcCu> dataTable =
        DataTable.<BeneficioTcCu>builder()
            .id("tabla-beneficios") // ID único para la tabla
            .column("bin", "Bin", bean -> StringUtils.trimToEmpty(bean.getBin()), "68px")
            .column("beneficioId", "ID", bean -> StringUtils.trimToEmpty(bean.getId()), "70px")
            .column(
                "beneficioDescripcion",
                "Descripción del beneficio",
                bean -> StringUtils.trimToEmpty(bean.getDescripcion()),
                "1088px")
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío, se poblará después
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
