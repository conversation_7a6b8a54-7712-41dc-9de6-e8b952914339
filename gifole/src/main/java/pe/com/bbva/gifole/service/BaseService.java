package pe.com.bbva.gifole.service;

import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import pe.com.bbva.gifole.model.BaseEntity;

/** Servicio base con operaciones CRUD comunes */
public interface BaseService<T extends BaseEntity> {

  T save(T entity);

  Optional<T> findById(Long id);

  List<T> findAll();

  Page<T> findAll(Pageable pageable);

  void deleteById(Long id);

  boolean existsById(Long id);
}
