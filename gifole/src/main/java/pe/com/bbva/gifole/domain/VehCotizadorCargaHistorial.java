package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_COTIZADOR_CARGA_HISTORIAL")
public class VehCotizadorCargaHistorial implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_COTIZADOR_CARGA_HISTORIAL")
  @TableGenerator(
      name = "SEQ_COTIZADOR_CARGA_HISTORIAL",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.VehCotizadorCargaHistorial",
      allocationSize = 1)
  private Long id;

  @Column(name = "CODIGO", length = 40)
  private String codigo;

  @Column(name = "TIPO_CARGA", length = 50)
  private String tipoCarga;

  @Column(name = "NOMBRE", length = 50)
  private String nombre;

  @Column(name = "ESTADO", length = 1)
  private String estado;

  @Column(name = "ESTADO_CATEGORIA", length = 1)
  private String estadoCategoria;

  @Column(name = "ERROR", length = 200)
  private String error;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "CREADOR", nullable = false, insertable = true, updatable = false)
  private Usuario creador;

  @Column(name = "CREACION", nullable = false, insertable = true, updatable = false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date creacion;

  @Column(name = "MODIFICACION", nullable = true, insertable = true, updatable = true)
  @Temporal(TemporalType.TIMESTAMP)
  private Date modificacion;
}
