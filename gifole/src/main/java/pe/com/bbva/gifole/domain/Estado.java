package pe.com.bbva.gifole.domain;

import java.io.Serializable;

@SuppressWarnings("serial")
public class Estado implements Serializable {

  private String id;

  private String nombre;

  private String creacion;

  private String creador;

  private String observacion;

  private String textopedido;

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public String getCreacion() {
    return creacion;
  }

  public void setCreacion(String creacion) {
    this.creacion = creacion;
  }

  public String getCreador() {
    return creador;
  }

  public void setCreador(String creador) {
    this.creador = creador;
  }

  public String getObservacion() {
    return observacion;
  }

  public void setObservacion(String observacion) {
    this.observacion = observacion;
  }

  public String getTextopedido() {
    return textopedido;
  }

  public void setTextopedido(String textopedido) {
    this.textopedido = textopedido;
  }
}
