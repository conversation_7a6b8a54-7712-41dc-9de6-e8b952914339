package pe.com.bbva.gifole.util;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import pe.com.bbva.gifole.domain.Usuario;

public class GifoleUtil {

  public static Usuario obtenerUsuarioSesion() {
    Usuario usuario = null;
    if (RequestContextHolder.getRequestAttributes() != null) {
      ServletRequestAttributes requestAttributes =
          (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
      HttpServletRequest request = requestAttributes.getRequest();
      HttpSession session = request.getSession(false);
      if (session != null) {
        usuario = (Usuario) session.getAttribute("SESION_USUARIO");
      }
    }
    return usuario;
  }

  public static String obtenerNumeroCuentaFormateado(String nroCuenta) {

    if (nroCuenta != null && (nroCuenta.length() == 20)) {
      String part1 = nroCuenta.substring(0, 4);
      String part2 = nroCuenta.substring(4, 8);
      String part3 = nroCuenta.substring(10, nroCuenta.length());
      String nroContratoFormateado = part1 + "-" + part2 + "-" + part3;
      if (nroCuenta.indexOf("-") > -1) {
        nroContratoFormateado = nroCuenta;
      }
      return nroContratoFormateado;
    } else if (nroCuenta != null && (nroCuenta.length() == 18)) {
      String part1 = nroCuenta.substring(0, 4);
      String part2 = nroCuenta.substring(4, 8);
      String part3 = nroCuenta.substring(8, nroCuenta.length());
      String nroContratoFormateado = part1 + "-" + part2 + "-" + part3;
      return nroContratoFormateado;
    } else {
      return nroCuenta;
    }
  }

  public static String obtenerNumeroTarjetaFormateado(String nroTarjeta){		
		if (nroTarjeta != null && StringUtils.isNotBlank(nroTarjeta)) {
			try {
				String part1 = nroTarjeta.substring(0, 4);
				String part2 = nroTarjeta.substring(4, 8);
				String part3 = nroTarjeta.substring(8, 12);
				String part4 = nroTarjeta.substring(12, 16);
				return part1 + "-" + part2 + "-" + part3 + "-" + part4;
			} catch(Exception e) {
				return StringUtils.EMPTY;
			}
		} else {
			return StringUtils.EMPTY;
		}
	}

  public static String ofuscarCuenta(Object bloque, int caracteresleft, int caracteresRight) {
		String bloqueFinal=(String)bloque;
		try {
			if(RequestContextHolder.getRequestAttributes()!=null){
	            ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder
	                            .currentRequestAttributes();
	            HttpServletRequest request = requestAttributes.getRequest();
	            HttpSession session = request.getSession(false);
	            if(session == null || session.getAttribute("SESION_DESCIFRAR_OFUSCADO_CUENTAS") == null)
	            {
	    			if(!StringUtils.isBlank(bloqueFinal)){
	    				String bloqueLeft = StringUtils.left(bloqueFinal, caracteresleft);
	    				String bloqueRight = StringUtils.right(bloqueFinal, caracteresRight);
	    				bloqueFinal = bloqueLeft + "********" + bloqueRight;
	    			}
	            }
	        }
		} catch(Exception e) {
			
		}
		
		return bloqueFinal;
	}
}
