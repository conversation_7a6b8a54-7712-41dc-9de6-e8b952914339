package pe.com.bbva.gifole.util;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import pe.com.bbva.gifole.domain.Usuario;

public class GifoleUtil {

  public static Usuario obtenerUsuarioSesion() {
    Usuario usuario = null;
    if (RequestContextHolder.getRequestAttributes() != null) {
      ServletRequestAttributes requestAttributes =
          (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
      HttpServletRequest request = requestAttributes.getRequest();
      HttpSession session = request.getSession(false);
      if (session != null) {
        usuario = (Usuario) session.getAttribute("SESION_USUARIO");
      }
    }
    return usuario;
  }
}
