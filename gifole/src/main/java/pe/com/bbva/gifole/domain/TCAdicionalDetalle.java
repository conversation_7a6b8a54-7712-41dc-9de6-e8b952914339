package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.hibernate.annotations.Formula;

@Data
@Entity
@Table(name = "TC_ADICIONAL_DETALLE")
@SuppressWarnings("serial")
public class TCAdicionalDetalle implements Serializable {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SeqGenAdiDet")
  @SequenceGenerator(
      name = "SeqGenAdiDet",
      sequenceName = "SQ_TC_ADICIONAL_DETALLE",
      allocationSize = 1)
  @Column(name = "ID")
  private Long id;

  @Column(name = "NOMBRES", length = 40)
  private String nombres;

  @Column(name = "APELLIDO_PATERNO", length = 40)
  private String apellidoPaterno;

  @Column(name = "APELLIDO_MATERNO", length = 40)
  private String apellidoMaterno;

  @Column(name = "TIPO_DOCUMENTO", length = 30)
  private String tipoDocumento;

  @Column(name = "DOCUMENTO", length = 30)
  private String documento;

  @Column(name = "ESTADO_CIVIL", length = 30)
  private String estadoCivil;

  @Column(name = "VINCULO", length = 30)
  private String vinculo;

  @Column(name = "OCUPACION", length = 60)
  private String ocupacion;

  @Column(name = "LIMITE_CREDITO")
  private Integer limiteCredito;

  @Column(name = "NUMERO_CONTACTO", length = 20)
  private String numeroContacto;

  @Column(name = "CORREO", length = 80)
  private String correo;

  @Column(name = "COD_CLIENTE", length = 10)
  private String codCliente;

  @Column(name = "TIPO_CLIENTE", length = 1)
  private String tipoCliente;

  @Column(name = "NRO_TARJETA", length = 20)
  private String nroTarjeta;

  @Column(name = "NRO_CONTRATO", length = 20)
  private String nroContrato;

  @Column(name = "FLAG_PROCESAR")
  private Integer flagProcesar;

  @OneToOne
  @JoinColumn(name = "ESTADO_ACTUAL")
  private TCEstado estadoActual;

  // bi-directional many-to-one association to TC_ADICIONAL
  @ManyToOne
  @JoinColumn(name = "TC_ADICIONAL")
  private TCAdicional tcAdicional;

  @Transient private List<TCAdicionalDetEstado> tcAdicionalDetEstado;

  @Column(name = "FECHA_MODIFICACION")
  private Date fechaModificacion;

  @Column(name = "DIRECCION_ENTREGA", length = 250)
  private String direccionEntrega;

  @Column(name = "OFICINA_RECOJO", length = 300)
  private String oficinaRecojo;

  @Transient private String localidad;

  @Transient private String estado;

  @Formula(value = "NOMBRES || ' ' || APELLIDO_PATERNO || ' ' || APELLIDO_MATERNO")
  private String nombreCompleto;

  @Transient private String combo;

  @Transient private String fechaModificacionFormat;

  @Transient private String valorMotivo;
}
