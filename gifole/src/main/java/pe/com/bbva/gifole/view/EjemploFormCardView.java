package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.Map;
import pe.com.bbva.gifole.view.components.FormCard;

@PageTitle("Ejemplo FormCard")
@Route(value = "ejemplo-formcard", layout = MainLayout.class)
public class EjemploFormCardView extends VerticalLayout {

  private FormCard formCard1;
  private FormCard formCard2;
  private FormCard formCard3;

  public EjemploFormCardView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Ejemplos de FormCard");
    title.addClassName("bbva-grid-title");

    // Crear diferentes ejemplos de FormCard
    createFormCard1(); // 2 inputs por fila
    createFormCard2(); // 3 inputs por fila
    createFormCard3(); // 1 input por fila

    // Botón para obtener valores
    Button getValuesButton = new Button("Obtener Valores de Todos los Formularios");
    getValuesButton.addClassName("bbva-button-primary");
    getValuesButton.addClickListener(e -> showAllValues());

    mainLayout.add(title, formCard1, formCard2, formCard3, getValuesButton);
    add(mainLayout);
  }

  /** Ejemplo 1: FormCard con 2 inputs por fila */
  private void createFormCard1() {
    formCard1 =
        FormCard.builder()
            .title("Datos Personales (2 inputs por fila)")
            .inputsPerRow(2)
            .textInput("nombres", "Nombres")
            .textInput("apellidos", "Apellidos")
            .textInput("documento", "Número de Documento")
            .dateInput("fechaNacimiento", "Fecha de Nacimiento")
            .textInput("email", "Correo Electrónico")
            .textInput("telefono", "Teléfono")
            .build();
  }

  /** Ejemplo 2: FormCard con 3 inputs por fila */
  private void createFormCard2() {
    formCard2 =
        FormCard.builder()
            .title("Información Financiera (3 inputs por fila)")
            .inputsPerRow(3)
            .numberInput("ingresos", "Ingresos Mensuales")
            .numberInput("egresos", "Egresos Mensuales")
            .numberInput("patrimonio", "Patrimonio")
            .textInput("banco", "Banco Principal")
            .textInput("tipoCuenta", "Tipo de Cuenta")
            .numberInput("saldo", "Saldo Promedio")
            .build();
  }

  /** Ejemplo 3: FormCard con 1 input por fila */
  private void createFormCard3() {
    formCard3 =
        FormCard.builder()
            .title("Observaciones (1 input por fila)")
            .inputsPerRow(1)
            .textInput("direccion", "Dirección Completa")
            .textInput("referencias", "Referencias")
            .textInput("observaciones", "Observaciones Adicionales")
            .build();
  }

  /** Muestra los valores de todos los formularios */
  private void showAllValues() {
    StringBuilder message = new StringBuilder();

    // Valores del FormCard 1
    Map<String, Object> values1 = formCard1.getValues();
    message.append("📋 DATOS PERSONALES:\n");
    values1.forEach(
        (key, value) -> message.append("• ").append(key).append(": ").append(value).append("\n"));

    // Valores del FormCard 2
    Map<String, Object> values2 = formCard2.getValues();
    message.append("\n💰 INFORMACIÓN FINANCIERA:\n");
    values2.forEach(
        (key, value) -> message.append("• ").append(key).append(": ").append(value).append("\n"));

    // Valores del FormCard 3
    Map<String, Object> values3 = formCard3.getValues();
    message.append("\n📝 OBSERVACIONES:\n");
    values3.forEach(
        (key, value) -> message.append("• ").append(key).append(": ").append(value).append("\n"));

    // Mostrar notificación con los valores
    Notification notification =
        Notification.show(message.toString(), 10000, Notification.Position.TOP_CENTER);
    notification.addClassName("bbva-notification");
  }
}
