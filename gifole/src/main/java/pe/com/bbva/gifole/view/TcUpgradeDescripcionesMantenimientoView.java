package pe.com.bbva.gifole.view; // Ajusta el paquete según tu estructura

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import java.util.List;

import pe.com.bbva.gifole.common.bean.BeneficiosUpgradeBean; // Asegúrate de la ruta correcta
import pe.com.bbva.gifole.view.components.DataTable; // Asegúrate de la ruta correcta

@PageTitle("Consulta Descripciones Upgrade") // Título basado en el nombre del UI original
@Route(value = "reporte/tarjetas/consulta-descripciones-upgrade", layout = MainLayout.class) // Ajusta la ruta y layout
public class TcUpgradeDescripcionesMantenimientoView extends VerticalLayout {

    public TcUpgradeDescripcionesMantenimientoView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Descripciones Upgrade");
        title.addClassName("bbva-grid-title");

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Simulando datos vacíos inicialmente, igual que en el ejemplo original
        List<BeneficiosUpgradeBean> items = new ArrayList<>();

        // Construir el DataTable usando el Builder con los MISMOS CAMPOS
        // Basado en obtenerArrayCampos() y la configuración de columnas
        DataTable<BeneficiosUpgradeBean> dataTable =
            DataTable.<BeneficiosUpgradeBean>builder()
                .id("descripciones-upgrade-table")
                .items(items) // Empieza sin datos
                .pageSize(10) // Puedes ajustar el tamaño de página
                // Campo: Bin (CAMPO_BIN)
                .column("bin", "Bin", BeneficiosUpgradeBean::getBin, "100px") // Ancho basado en UI_COLUMN_DATATABLE_WIDTH_100
                // Campo: Descripcion (CAMPO_DESCRIPCION)
                .column("descripcion", "Descripción General", BeneficiosUpgradeBean::getDescripcion, "250px") // Ancho basado en UI_COLUMN_DATATABLE_WIDTH_250
                // Campo: Descripcion Detallada (CAMPO_DESCRIPCION_DETALLADA)
                .column("descripcionDetallada", "Descripción Detallada", BeneficiosUpgradeBean::getDescripcionDetallada, "270px") // Ancho basado en UI_COLUMN_DATATABLE_WIDTH_270
                // Campo: Estado (CAMPO_ESTADO)
                .column("estado", "Estado", BeneficiosUpgradeBean::getEstado, "100px") // Ancho basado en UI_COLUMN_DATATABLE_WIDTH_100
                .build();

        card.add(dataTable);
        return card;
    }
}