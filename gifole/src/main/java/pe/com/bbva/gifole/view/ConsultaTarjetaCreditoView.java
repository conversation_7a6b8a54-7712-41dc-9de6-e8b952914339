package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;

import pe.com.bbva.gifole.domain.TCTocDetEstado;
import pe.com.bbva.gifole.view.components.DataTable;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
// import pe.com.bbva.gifole.util.Constante; // Descomenta si usas constantes específicas
// import pe.com.bbva.gifole.util.TipoGarantiaTarjetaEnum; // Descomenta si usas enums específicos

@PageTitle("Consulta Tarjeta Crédito")
@Route(value = "reporte/tarjetas/credito", layout = MainLayout.class)
public class ConsultaTarjetaCreditoView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy");

    // Constantes/Enums de ejemplo (reemplaza con las reales de tu proyecto)
    // private static final String NOMBREDISPOSICIONEFECTIVO = "disposicionEfectivo"; // Reemplaza con la constante real
    // private static final String CAMPO_TIPO_GARANTIA = "tipoGarantia"; // Reemplaza con Constante.CANCELACION_TARJETA.CAMPO_TIPO_GARANTIA

    private final List<TCTocDetEstado> allData = new ArrayList<>();
    private DataTable<TCTocDetEstado> dataTable;

    public ConsultaTarjetaCreditoView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Tarjeta Crédito");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, basado SOLAMENTE en el fragmento proporcionado
        dataTable =
            DataTable.<TCTocDetEstado>builder()
                .id("tabla-tarjeta-credito") // ID único para la tabla
                // Mapeo de columnas basado EXCLUSIVAMENTE en el código proporcionado
                .column("codigo", "Código Central", // De tcToc.codigoCentral
                    bean -> StringUtils.trimToEmpty(
                        bean.getTcToc() != null ? bean.getTcToc().getCodigoCentral() : ""), "120px")
                .column("nombre", "Nombre", // De tcToc.nombres
                    bean -> StringUtils.trimToEmpty(
                        bean.getTcToc() != null ? bean.getTcToc().getNombres() : ""), "200px")
                .column("fechaRegistro", "Fecha Registro", // De tcToc.fechaRegistro
                    bean -> {
                        if (bean.getTcToc() != null && bean.getTcToc().getFechaRegistro() != null) {
                            return FORMATO_FECHA.format(bean.getTcToc().getFechaRegistro());
                        }
                        return "";
                    }, "120px")
                .column("tarjeta", "Tarjeta", // De tcToc.tarjeta
                    bean -> StringUtils.trimToEmpty(
                        bean.getTcToc() != null ? bean.getTcToc().getTarjeta() : ""), "150px")
                .column("programabeneficios", "Programa Beneficios", // De tcToc.programaBenef
                    bean -> StringUtils.trimToEmpty(
                        bean.getTcToc() != null ? bean.getTcToc().getProgramaBenef() : ""), "150px")
                .column("fechaPago", "Fecha Pago", // De tcToc.fechaPago
                    bean -> {
                        if (bean.getTcToc() != null && bean.getTcToc().getFechaPago() != null) {
                            return FORMATO_FECHA.format(bean.getTcToc().getFechaPago());
                        }
                        return "";
                    }, "120px")
                .column("envioEC", "Envío EC", // De tcToc.envioEc
                    bean -> StringUtils.trimToEmpty(
                        bean.getTcToc() != null ? bean.getTcToc().getEnvioEc() : ""), "100px")
                .column("tipoEntrega", "Tipo Entrega", // Lógica condicional basada en tcToc.oficinaRecojo
                    bean -> {
                        if (bean.getTcToc() != null) {
                            if (StringUtils.isBlank(bean.getTcToc().getOficinaRecojo())) {
                                return "Domicilio";
                            } else {
                                return "Oficina";
                            }
                        }
                        return "";
                    }, "120px")
                .column("direccion", "Dirección", // De tcToc.direccion
                    bean -> StringUtils.trimToEmpty(
                        bean.getTcToc() != null ? bean.getTcToc().getDireccion() : ""), "250px")
                .column("telefono", "Teléfono", // De tcToc.telefono
                    bean -> StringUtils.trimToEmpty(
                        bean.getTcToc() != null ? bean.getTcToc().getTelefono() : ""), "120px")
                .column("ofertaDatazo", "Oferta Datazo", // De tcToc.ofertaDatazo
                    bean -> StringUtils.trimToEmpty(
                        bean.getTcToc() != null ? bean.getTcToc().getOfertaDatazo() : ""), "120px")
                // Asumiendo NOMBREDISPOSICIONEFECTIVO es una constante con valor "disposicionEfectivo"
                .column("disposicionEfectivo", "Disposición Efectivo", // De tcToc.disposicionEfectivo
                    bean -> StringUtils.trimToEmpty(
                        bean.getTcToc() != null ? bean.getTcToc().getDisposicionEfectivo() : ""), "150px")
                // Asumiendo CAMPO_TIPO_GARANTIA es una constante y TipoGarantiaTarjetaEnum existe
                .column("tipoGarantia", "Tipo Garantía", // De tcToc.tipoGarantia (con transformación)
                    bean -> {
                        if (bean.getTcToc() != null) {
                            String tipoGarantia = bean.getTcToc().getTipoGarantia();
                            // Ajusta esta lógica según tus enums/constantes reales
                            // Ejemplo placeholder:
                            if ("TARJETA_RESPALDADA".equalsIgnoreCase(tipoGarantia)) {
                                return "Tarjeta Respaldo"; // Ejemplo de descripción
                            }
                            // Agrega más casos si es necesario
                            return StringUtils.trimToEmpty(tipoGarantia);
                        }
                        return "";
                    }, "150px")
                // Datos (inicialmente vacío)
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}