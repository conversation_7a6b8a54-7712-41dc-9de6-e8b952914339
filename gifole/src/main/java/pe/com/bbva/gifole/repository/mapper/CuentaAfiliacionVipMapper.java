package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.AperturaCuentaBean;

@SuppressWarnings("rawtypes")
@Component
public class CuentaAfiliacionVipMapper implements RowMapper {

  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    AperturaCuentaBean param = new AperturaCuentaBean();
    param.setId(rs.getLong("ID"));
    param.setCodigoCentral(rs.getString("CODIGO_CENTRAL"));
    param.setNombreCompleto(rs.getString("NOMBRE_COMPLETO"));
    param.setFechaRegistro(rs.getDate("FECHA_REGISTRO"));
    param.setNroCuenta(rs.getString("NRO_CUENTA"));
    param.setTipoMoneda(rs.getString("TIPO_MONEDA"));
    param.setCanal(rs.getString("CANAL"));

    return param;
  }
}
