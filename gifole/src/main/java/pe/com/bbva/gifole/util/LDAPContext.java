package pe.com.bbva.gifole.util;

import java.util.Properties;
import javax.naming.NamingException;
import javax.naming.ldap.InitialLdapContext;

public class LDAPContext {
  private static InitialLdapContext ldapInitialContext;

  private LDAPContext() {
    // Constructor privado para evitar la creación de instancias directamente
  }

  public static synchronized InitialLdapContext getInstance(Properties envLDAP)
      throws NamingException {
    if (ldapInitialContext == null) {
      ldapInitialContext = new InitialLdapContext(envLDAP, null);
    }
    return ldapInitialContext;
  }

  public static synchronized void closeConnection() throws NamingException {
    if (ldapInitialContext != null) {
      try {
        ldapInitialContext.close();
      } catch (NamingException e) {
        throw new RuntimeException(e);
      }
      ldapInitialContext = null;
    }
  }
}
