package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.AperturaCuentaDetalleBean; // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.util.Constante; // Asumo que existe, ajusta según tu proyecto
import pe.com.bbva.gifole.util.GifoleUtil; // Asumo que existe, ajusta según tu proyecto
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Solicitud Apertura Cuenta")
@Route(value = "reporte/solicitudes/apertura-cuenta", layout = MainLayout.class)
public class ConsultaSolicitudAperturaCuentaView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<AperturaCuentaDetalleBean> allData = new ArrayList<>();
    private DataTable<AperturaCuentaDetalleBean> dataTable;

    public ConsultaSolicitudAperturaCuentaView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Solicitud Apertura Cuenta");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<AperturaCuentaDetalleBean>builder()
                .id("tabla-apertura-cuenta") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaAperturaCuentaUI)
                .column("codigoCentral", "Código Central",
                    bean -> StringUtils.trimToEmpty(
                        bean.getAperturaCuentaBean() != null ?
                        bean.getAperturaCuentaBean().getCodigoCentral() : ""), "100px")
                .column("nombreCompleto", "Cliente", // Equivalente a "cliente" del DTO
                    bean -> StringUtils.trimToEmpty(
                        bean.getAperturaCuentaBean() != null ?
                        bean.getAperturaCuentaBean().getNombreCompleto() : ""), "250px")
                .column("fechaRegistro", "Fecha Solicitud", // Equivalente a "fechaSolicitud" del DTO
                    bean -> bean.getAperturaCuentaBean() != null && bean.getAperturaCuentaBean().getFechaRegistro() != null ?
                            FORMATO_FECHA.format(bean.getAperturaCuentaBean().getFechaRegistro()) : "", "150px")
                .column("tipoCuenta", "Tipo Cuenta", // Equivalente a "tipoCuenta" del DTO
                    bean -> StringUtils.trimToEmpty(
                        bean.getAperturaCuentaBean() != null ?
                        bean.getAperturaCuentaBean().getTipoCuenta() : ""), "120px")
                .column("nroCuenta", "Número Cuenta", // Formateado y ofuscado
                    bean -> {
                        if (bean.getAperturaCuentaBean() != null) {
                            String nroCuenta = bean.getAperturaCuentaBean().getNroCuenta();
                            if (nroCuenta != null) {
                                if (nroCuenta.length() == 20) {
                                    String part1 = nroCuenta.substring(0, 4);
                                    String part2 = nroCuenta.substring(4, 8);
                                    String part3 = nroCuenta.substring(10, 20);
                                    String cuenta = part1 + "-" + part2 + "-" + part3;
                                    return GifoleUtil.ofuscarCuenta(cuenta, 4, 4);
                                } else {
                                    return GifoleUtil.ofuscarCuenta(nroCuenta, 4, 4);
                                }
                            }
                        }
                        return "";
                    }, "170px")
                .column("tipoMoneda", "Tipo Moneda",
                    bean -> StringUtils.trimToEmpty(
                        bean.getAperturaCuentaBean() != null ?
                        bean.getAperturaCuentaBean().getTipoMoneda() : ""), "100px")
                .column("tarjetaAsociada", "Tarjeta Asociada", // Formateada
                    bean -> {
                        if (bean.getAperturaCuentaBean() != null) {
                            String nroTarjeta = bean.getAperturaCuentaBean().getNroTarjetaAsociada();
                            if (nroTarjeta != null && nroTarjeta.length() == 16) {
                                String part1 = nroTarjeta.substring(0, 4);
                                String part2 = nroTarjeta.substring(4, 8);
                                String part3 = nroTarjeta.substring(8, 12);
                                String part4 = nroTarjeta.substring(12, 16);
                                return part1 + "-" + part2 + "-" + part3 + "-" + part4;
                            } else {
                                return StringUtils.trimToEmpty(nroTarjeta);
                            }
                        }
                        return "";
                    }, "170px")
                .column("email", "Correo", // Equivalente a parte de "documentoIdentidad" y "correo"
                    bean -> StringUtils.trimToEmpty(
                        bean.getAperturaCuentaBean() != null ?
                        bean.getAperturaCuentaBean().getEmail() : ""), "200px")
                .column("telefonoContacto", "Teléfono Contacto",
                    bean -> StringUtils.trimToEmpty(
                        bean.getAperturaCuentaBean() != null ?
                        bean.getAperturaCuentaBean().getTelefonoContacto() : ""), "120px")
                .column("observacion", "Observaciones", // Equivalente a "observaciones" del DTO
                    bean -> StringUtils.trimToEmpty(
                        bean.getAperturaCuentaBean() != null ?
                        bean.getAperturaCuentaBean().getObservacion() : ""), "200px")
                .column("fechaModificacion", "Fecha Modificación",
                    bean -> bean.getCreacion() != null ? FORMATO_FECHA.format(bean.getCreacion()) : "", "150px")
                .column("canal", "Canal", // Mapeado a nombres de canal
                    bean -> {
                        if (bean.getAperturaCuentaBean() != null) {
                            String canal = bean.getAperturaCuentaBean().getCanal();
                            if (canal == null)
                                return Constante.CANAL.NOMBRE_BANCA_POR_INTERNET; // Asegúrate de tener esta constante
                            else if (Constante.CANAL.BANCA_POR_INTERNET_UX.equals(canal)) // Asegúrate de tener esta constante
                                return Constante.CANAL.NOMBRE_BANCA_POR_INTERNET;
                            else
                                return canal;
                        }
                        return "";
                    }, "120px")
                .column("cuentaEstado", "Estado", // Equivalente a "estado" del DTO (mapeado de estadoPremio)
                    bean -> {
                        if (bean.getAperturaCuentaBean() != null) {
                            String estadoPremio = bean.getAperturaCuentaBean().getEstadoPremio();
                            if ("R".equals(estadoPremio)) {
                                return "REGISTRADO";
                            } else if ("D".equals(estadoPremio)) {
                                return "DEPÓSITO PENDIENTE";
                            } else if ("E".equals(estadoPremio)) {
                                return "SOLICITUD EXPIRADA";
                            } else if ("A".equals(estadoPremio)) {
                                return "APROBADO";
                            } else {
                                return "REGISTRADO";
                            }
                        }
                        return "REGISTRADO";
                    }, "180px")
                .column("indPremio", "Indicador Premio",
                    bean -> StringUtils.trimToEmpty(
                        bean.getAperturaCuentaBean() != null ?
                        bean.getAperturaCuentaBean().getIndPremio() : ""), "130px")
                .column("nombrePremio", "Nombre Premio",
                    bean -> StringUtils.trimToEmpty(
                        bean.getAperturaCuentaBean() != null ?
                        bean.getAperturaCuentaBean().getNombrePremio() : ""), "150px")
                .column("afiliacionVip", "Afiliación VIP", // Convertido a "Si"/""
                    bean -> {
                        if (bean.getAperturaCuentaBean() != null && bean.getAperturaCuentaBean().getFlagAfiliacionVip() == 1) {
                            return "Si";
                        }
                        return "";
                    }, "120px")
                .column("rbaRiesgo", "RBA Riesgo",
                    bean -> StringUtils.trimToEmpty(
                        bean.getAperturaCuentaBean() != null ?
                        String.valueOf(bean.getAperturaCuentaBean().getRbaRiesgo()) : ""), "100px")
                .column("rbaDiligencia", "RBA Diligencia",
                    bean -> StringUtils.trimToEmpty(
                        bean.getAperturaCuentaBean() != null ?
                        String.valueOf(bean.getAperturaCuentaBean().getRbaDiligencia()) : ""), "120px")
                // Puedes agregar más columnas aquí si las necesitas, siguiendo el mismo patrón
                // Nota: La columna "detEstado" con botones requiere una implementación más compleja
                // que generalmente se maneja fuera del DataTable básico o con componentes personalizados.
                // Datos (inicialmente vacío)
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }
}