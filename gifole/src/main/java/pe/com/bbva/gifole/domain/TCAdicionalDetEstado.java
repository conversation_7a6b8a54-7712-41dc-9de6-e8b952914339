package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "TC_ADICIONAL_DET_ESTADO")
public class TCAdicionalDetEstado implements Serializable {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SeqGenAdiDetEst")
  @SequenceGenerator(
      name = "SeqGenAdiDetEst",
      sequenceName = "SQ_TC_ADICIONAL_DET_ESTADO",
      allocationSize = 1)
  @Column(name = "ID")
  private Long id;

  @Column(name = "OBSERVACION", length = 150)
  private String observacion;

  @Column(name = "CREACION")
  private Date creacion;

  @Column(name = "EDICION")
  private Date edicion;

  @Column(name = "CREADOR", length = 20)
  private String creador;

  @Column(name = "EDITOR", length = 20)
  private String editor;

  // bidirectional many-to-one association to TC_ADICIONAL_DETALLE
  @ManyToOne
  @JoinColumn(name = "TC_ADICIONAL_DETALLE")
  private TCAdicionalDetalle tcAdicionalDetalle;

  @ManyToOne
  @JoinColumn(name = "TC_ESTADO")
  private TCEstado tcEstado;

  @ManyToOne
  @JoinColumn(name = "TC_MOTIVO")
  private TCMotivo tcMotivo;
}
