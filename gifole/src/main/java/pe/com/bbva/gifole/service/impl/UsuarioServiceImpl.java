package pe.com.bbva.gifole.service.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.*;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import pe.com.bbva.gifole.domain.SisPerfil;
import pe.com.bbva.gifole.domain.Usuario;
import pe.com.bbva.gifole.repository.UsuarioRepository;
import pe.com.bbva.gifole.service.UsuarioService;

/** Implementacion del servicio para operaciones relacionadas con Usuario */
@Service
@RequiredArgsConstructor
public class UsuarioServiceImpl implements UsuarioService {

  private static final Logger logger = LogManager.getLogger(UsuarioServiceImpl.class);

  private final UsuarioRepository usuarioRepository;

  private final EntityManager entityManager;

  @Override
  @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
  public void guardarUsuario(Usuario usuario) {
    if (usuario.getId() == null) usuarioRepository.crearUsuario(usuario);
    else usuarioRepository.actualizarUsuario(usuario);
  }

  @Override
  @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
  public void eliminarUsuario(Long id) {
    usuarioRepository.eliminarUsuario(id);
  }

  @Override
  public List<Usuario> buscarUsuario(Usuario usuario) {
    CriteriaBuilder cb = entityManager.getCriteriaBuilder();
    CriteriaQuery<Usuario> cq = cb.createQuery(Usuario.class);
    Root<Usuario> root = cq.from(Usuario.class);

    // Crear predicados dinámicos
    Predicate predicate = cb.conjunction();

    if (usuario != null) {
      if (usuario.getId() != null && usuario.getId() > 0) {
        predicate = cb.and(predicate, cb.equal(root.get("id"), usuario.getId()));
      }

      if (StringUtils.isNotBlank(usuario.getRegistro())) {
        predicate =
            cb.and(
                predicate,
                cb.like(
                    cb.lower(root.get("registro")),
                    "%" + usuario.getRegistro().toLowerCase() + "%"));
      }

      if (StringUtils.isNotBlank(usuario.getNombre())) {
        predicate =
            cb.and(
                predicate,
                cb.like(
                    cb.lower(root.get("nombre")), "%" + usuario.getNombre().toLowerCase() + "%"));
      }

      if (StringUtils.isNotBlank(usuario.getPaterno())) {
        predicate =
            cb.and(
                predicate,
                cb.like(
                    cb.lower(root.get("paterno")), "%" + usuario.getPaterno().toLowerCase() + "%"));
      }

      if (StringUtils.isNotBlank(usuario.getMaterno())) {
        predicate =
            cb.and(
                predicate,
                cb.like(
                    cb.lower(root.get("materno")), "%" + usuario.getMaterno().toLowerCase() + "%"));
      }

      if (usuario.getSisPerfil() != null
          && StringUtils.isNotBlank(usuario.getSisPerfil().getDescripcion())) {
        Join<Usuario, SisPerfil> perfilJoin = root.join("sisPerfil");
        predicate =
            cb.and(
                predicate,
                cb.like(
                    cb.lower(perfilJoin.get("descripcion")),
                    "%" + usuario.getSisPerfil().getDescripcion().toLowerCase() + "%"));
      }
    }

    cq.where(predicate);
    return entityManager.createQuery(cq).getResultList();
  }

  /*
   * @Override public List<Usuario> obtenerUsuario(usuario usuario) {
   * logger.debug("Iniciando busqueda de usuarios con criterios: {}", usuario);
   *
   * Usuario usuario = new Usuario(); usuario.setRegistro(usuario.getRegistro());
   * usuarioRepository.buscarUsuario(usuario);
   *
   * logger.debug("Busqueda completada - metodo pendiente de implementacion"); return null; }
   */

}
