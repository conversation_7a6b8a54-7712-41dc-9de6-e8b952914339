package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.VehCotizacion;
import pe.com.bbva.gifole.domain.VehCotizacionDetalle;
import pe.com.bbva.gifole.domain.VehMarca;
import pe.com.bbva.gifole.domain.VehModelo;
import pe.com.bbva.gifole.domain.VehPlan;
import pe.com.bbva.gifole.domain.VehTipo;

@Component
public class VehCotizacionDetalleMapper implements RowMapper<VehCotizacionDetalle> {

  @Override
  public VehCotizacionDetalle mapRow(ResultSet rs, int i) throws SQLException {
    VehCotizacionDetalle cotizacionDetalle = new VehCotizacionDetalle();
    cotizacionDetalle.setPlan(new VehPlan());
    cotizacionDetalle.getPlan().setCodigo(rs.getString("PLAN"));
    cotizacionDetalle.setCuota(rs.getString("CUOTA"));
    cotizacionDetalle.setCuotaAnual(rs.getBigDecimal("CUOTA_ANUAL"));
    /*
     * cotizacionDetalle.setCuotaMensualP1(rs.getBigDecimal("CUOTA_MENSUAL_P1"));
     * cotizacionDetalle.setCuotaMensualP2(rs.getBigDecimal("CUOTA_MENSUAL_P2"));
     * cotizacionDetalle.setCuotaMensualP3(rs.getBigDecimal("CUOTA_MENSUAL_P3"));
     */
    cotizacionDetalle.setCotizacion(new VehCotizacion());
    cotizacionDetalle.getCotizacion().setId(rs.getLong("ID"));
    cotizacionDetalle.getCotizacion().setNumPlaca(rs.getString("NUM_PLACA"));
    cotizacionDetalle.getCotizacion().setMarca(new VehMarca());
    cotizacionDetalle.getCotizacion().getMarca().setNombre(rs.getString("MARCA"));
    cotizacionDetalle.getCotizacion().setModelo(new VehModelo());
    cotizacionDetalle.getCotizacion().getModelo().setNombre(rs.getString("MODELO"));
    cotizacionDetalle.getCotizacion().setAnhoFabricacion(rs.getString("ANHO_FABRICACION"));
    cotizacionDetalle.getCotizacion().setCambioGas(rs.getString("CAMBIO_GAS"));
    cotizacionDetalle.setValor(rs.getString("VALOR"));
    cotizacionDetalle.getCotizacion().setNombre(rs.getString("NOMBRE"));
    cotizacionDetalle.getCotizacion().setTipoDocumento(rs.getString("TIPO_DOCUMENTO"));
    cotizacionDetalle.getCotizacion().setDocumento(rs.getString("DOCUMENTO"));
    cotizacionDetalle.getCotizacion().setCorreo(rs.getString("CORREO"));
    cotizacionDetalle.getCotizacion().setTelefono(rs.getString("TELEFONO"));
    cotizacionDetalle.getCotizacion().setFechaRegistro(rs.getTimestamp("FECHA_REGISTRO"));
    cotizacionDetalle.getCotizacion().setAutorizacion(rs.getString("AUTORIZACION"));
    cotizacionDetalle.getCotizacion().setTipo(new VehTipo());
    cotizacionDetalle.getCotizacion().setLugarRegistro(rs.getString("LUGAR_REGISTRO"));
    cotizacionDetalle.getCotizacion().getTipo().setNombre(rs.getString("TIPO"));
    cotizacionDetalle.getCotizacion().setHorario(rs.getString("HORARIO"));

    cotizacionDetalle.getCotizacion().setNroCotizacion(rs.getString("NRO_COTIZACION"));
    cotizacionDetalle.getCotizacion().setPlanElegido(rs.getString("PLAN_ELEGIDO"));
    cotizacionDetalle.getCotizacion().setDivisaPlanElegido(rs.getString("DIVISA_PLAN_ELEGIDO"));
    cotizacionDetalle.getCotizacion().setMontoPlanElegido(rs.getString("MONTO_PLAN_ELEGIDO"));
    cotizacionDetalle.getCotizacion().setCanal(rs.getString("CANAL"));

    return cotizacionDetalle;
  }
}
