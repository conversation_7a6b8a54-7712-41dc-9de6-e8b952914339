package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class BeneficiosUpgradeBean implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;
  private String bin;
  private String codigo;
  private String descripcion;
  private String descripcionDetallada;
  private String estado;
  private String creador;
  private String fechaCreacion;
  private String editor;
  private String fechaModificacion;
  private Date fechaRegistro1;
  private Date fechaRegistro2;
  private boolean indicadorBeneficiario;

  /** */
  public BeneficiosUpgradeBean() {
    super();
  }

  /**
   * @param id
   */
  public BeneficiosUpgradeBean(Long id) {
    super();
    this.id = id;
  }

  /**
   * @param estado
   * @param indicadorBeneficiario
   */
  public BeneficiosUpgradeBean(String estado, boolean indicadorBeneficiario) {
    super();
    this.estado = estado;
    this.indicadorBeneficiario = indicadorBeneficiario;
  }

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getBin() {
    return bin;
  }

  public void setBin(String bin) {
    this.bin = bin;
  }

  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public String getDescripcion() {
    return descripcion;
  }

  public void setDescripcion(String descripcion) {
    this.descripcion = descripcion;
  }

  public String getDescripcionDetallada() {
    return descripcionDetallada;
  }

  public void setDescripcionDetallada(String descripcionDetallada) {
    this.descripcionDetallada = descripcionDetallada;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public String getCreador() {
    return creador;
  }

  public void setCreador(String creador) {
    this.creador = creador;
  }

  public String getFechaCreacion() {
    return fechaCreacion;
  }

  public void setFechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
  }

  public String getEditor() {
    return editor;
  }

  public void setEditor(String editor) {
    this.editor = editor;
  }

  public String getFechaModificacion() {
    return fechaModificacion;
  }

  public void setFechaModificacion(String fechaModificacion) {
    this.fechaModificacion = fechaModificacion;
  }

  public Date getFechaRegistro1() {
    return fechaRegistro1;
  }

  public void setFechaRegistro1(Date fechaRegistro1) {
    this.fechaRegistro1 = fechaRegistro1;
  }

  public Date getFechaRegistro2() {
    return fechaRegistro2;
  }

  public void setFechaRegistro2(Date fechaRegistro2) {
    this.fechaRegistro2 = fechaRegistro2;
  }

  public boolean isIndicadorBeneficiario() {
    return indicadorBeneficiario;
  }

  public void setIndicadorBeneficiario(boolean indicadorBeneficiario) {
    this.indicadorBeneficiario = indicadorBeneficiario;
  }
}
