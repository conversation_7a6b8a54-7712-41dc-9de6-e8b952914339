package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.Seguro;

@Component
public class SeguroMapper implements RowMapper<Seguro> {

  @Override
  public Seguro mapRow(ResultSet rs, int i) throws SQLException {
    Seguro seguro = new Seguro();
    seguro.setId(rs.getLong("ID"));
    seguro.setNombre(rs.getString("NOMBRE"));
    seguro.setApellido(rs.getString("APELLIDO"));
    seguro.setTipoDocumento(rs.getString("TIPO_DOCUMENTO"));
    seguro.setDocumento(rs.getString("DOCUMENTO"));
    seguro.setDepartamento(rs.getString("DEPARTAMENTO"));
    seguro.setCorreo(rs.getString("CORREO"));
    seguro.setTelefono(rs.getString("TELEFONO"));
    seguro.setHorario(rs.getString("HORARIO"));
    seguro.setAutorizacion(rs.getString("AUTORIZACION"));
    seguro.setFechaRegistro(rs.getTimestamp("FECHA_REGISTRO"));
    seguro.setTipoSeguro(rs.getString("TIPO_SEGURO"));
    return seguro;
  }
}
