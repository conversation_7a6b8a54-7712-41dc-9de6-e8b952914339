package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.TCTocCarretera;

@Component
public class TOCCarreteraMapper implements RowMapper<TCTocCarretera> {

  @Override
  public TCTocCarretera mapRow(ResultSet rs, int i) throws SQLException {

    TCTocCarretera tcTocCarretera = new TCTocCarretera();
    tcTocCarretera.setId(rs.getLong("ID"));
    tcTocCarretera.setIdArchivo(rs.getString("ID_ARCHIVO"));
    tcTocCarretera.setCodigoCentral(rs.getString("COD_CENTRAL"));
    tcTocCarretera.setTipoDocumento(rs.getString("TIP_DOCUMENTO"));
    tcTocCarretera.setNumeroDocumento(rs.getString("NUM_DOCUMENTO"));
    tcTocCarretera.setNombres(rs.getString("NOMBRES"));
    tcTocCarretera.setApellidoPaterno(rs.getString("APE_PATERNO"));
    tcTocCarretera.setApellidoMaterno(rs.getString("APE_MATERNO"));
    tcTocCarretera.setCelular(rs.getString("CELULAR"));
    tcTocCarretera.setCorreo(rs.getString("CORREO"));
    tcTocCarretera.setRptaLpdp(rs.getString("RPTA_LPDP"));
    tcTocCarretera.setMoi2TitleId(rs.getString("MOI2_TITLEID"));
    tcTocCarretera.setBeneficio(rs.getString("BENEFICIO"));
    tcTocCarretera.setMoi2Limitadj(rs.getString("MOI2_LIMITADJ"));
    tcTocCarretera.setMoi2Workplaceval(rs.getString("MOI2_WORKPLACEVAL"));
    tcTocCarretera.setMoi2Addresseval(rs.getString("MOI2_ADDRESSVAL"));
    tcTocCarretera.setEstado(rs.getString("ESTADO"));
    tcTocCarretera.setMotivoFin(rs.getString("MOTIVO_FIN"));
    tcTocCarretera.setMotivoRechazo(rs.getString("MOTIVO_RECHAZO"));
    tcTocCarretera.setFechaLead(rs.getString("FECHA_LEAD"));
    tcTocCarretera.setTipoFlujo(rs.getString("TIPO_FLUJO"));
    tcTocCarretera.setTipoLead(rs.getString("TIPO_LEAD"));
    tcTocCarretera.setHoraContacto(rs.getString("HOR_CONTACTO"));
    tcTocCarretera.setMotivoContacto(rs.getString("MOTIVO_CONTACTO"));
    tcTocCarretera.setPasoMotor(rs.getString("PASO_MOTOR"));
    tcTocCarretera.setFlagOferta(rs.getString("FLAG_OFERTAS"));
    tcTocCarretera.setRespuestaMotor(rs.getString("RESPUESTA_MOTOR"));
    tcTocCarretera.setCodClasificacion(rs.getString("COD_CLASIFICACION"));
    tcTocCarretera.setClasificacion(rs.getString("CLASIFICACION"));
    tcTocCarretera.setReglaRechazo(rs.getString("REGLA_RECHAZO"));
    tcTocCarretera.setDescripcion(rs.getString("DESCRIPCION"));
    tcTocCarretera.setResultadoEvaluacion(rs.getString("RESULTADO_EVALUACION"));
    tcTocCarretera.setModeloCelular(rs.getString("MODELO_CELULAR"));
    tcTocCarretera.setSistemaOperativo(rs.getString("SISTEMA_OPERATIVO"));
    tcTocCarretera.setIp(rs.getString("IP"));
    tcTocCarretera.setNavegador(rs.getString("NAVEGADOR"));
    tcTocCarretera.setOrigen(rs.getString("ORIGEN"));
    tcTocCarretera.setCodTinker(rs.getString("CODIGO_TINKER"));
    tcTocCarretera.setFechaRegistro(rs.getString("FECHA_REGISTRO"));
    return tcTocCarretera;
  }
}
