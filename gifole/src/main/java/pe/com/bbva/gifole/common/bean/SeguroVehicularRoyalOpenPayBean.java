package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class SeguroVehicularRoyalOpenPayBean implements Serializable {

  private static final long serialVersionUID = 1L;
  private Long id;
  private String cotizacionCodigo;
  private String cotizacionStep;
  private String solicitudFechaRegistro;
  private String horarioContacto;
  private String documentoNumero;
  private String documentoTipo;
  private String nombre;
  private String apellido;
  private String correo;
  private String telefono;
  private String vehiculoPlaca;
  private String vehiculoMarca;
  private String vehiculoModelo;
  private String vehiculoFechafabricacion;
  private String vehiculoValorMonetario;
  private String vehiculoMonedaValorMonetario;
  private String vehiculoConversionMotor;
  private String vehiculoTipoCirculacion;
  private String primaMonto;
  private String primaMontoMoneda;
  private String primaTotalMonto;
  private String primaTotalMontoMoneda;
  private String primaPeriodo;
  private String planName;
  private String informacionBanco;
  private String codigoOficina;
  private String codigoUsuario;
  private String canal;
  private Date fechaRegistroDesde;
  private Date fechaRegistroHasta;
  private String idProducto;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getCotizacionCodigo() {
    return cotizacionCodigo;
  }

  public void setCotizacionCodigo(String cotizacionCodigo) {
    this.cotizacionCodigo = cotizacionCodigo;
  }

  public String getCotizacionStep() {
    return cotizacionStep;
  }

  public void setCotizacionStep(String cotizacionStep) {
    this.cotizacionStep = cotizacionStep;
  }

  public String getSolicitudFechaRegistro() {
    return solicitudFechaRegistro;
  }

  public void setSolicitudFechaRegistro(String solicitudFechaRegistro) {
    this.solicitudFechaRegistro = solicitudFechaRegistro;
  }

  public String getHorarioContacto() {
    return horarioContacto;
  }

  public void setHorarioContacto(String horarioContacto) {
    this.horarioContacto = horarioContacto;
  }

  public String getDocumentoNumero() {
    return documentoNumero;
  }

  public void setDocumentoNumero(String documentoNumero) {
    this.documentoNumero = documentoNumero;
  }

  public String getDocumentoTipo() {
    return documentoTipo;
  }

  public void setDocumentoTipo(String documentoTipo) {
    this.documentoTipo = documentoTipo;
  }

  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public String getApellido() {
    return apellido;
  }

  public void setApellido(String apellido) {
    this.apellido = apellido;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public String getTelefono() {
    return telefono;
  }

  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  public String getVehiculoPlaca() {
    return vehiculoPlaca;
  }

  public void setVehiculoPlaca(String vehiculoPlaca) {
    this.vehiculoPlaca = vehiculoPlaca;
  }

  public String getVehiculoMarca() {
    return vehiculoMarca;
  }

  public void setVehiculoMarca(String vehiculoMarca) {
    this.vehiculoMarca = vehiculoMarca;
  }

  public String getVehiculoModelo() {
    return vehiculoModelo;
  }

  public void setVehiculoModelo(String vehiculoModelo) {
    this.vehiculoModelo = vehiculoModelo;
  }

  public String getVehiculoFechafabricacion() {
    return vehiculoFechafabricacion;
  }

  public void setVehiculoFechafabricacion(String vehiculoFechafabricacion) {
    this.vehiculoFechafabricacion = vehiculoFechafabricacion;
  }

  public String getVehiculoValorMonetario() {
    return vehiculoValorMonetario;
  }

  public void setVehiculoValorMonetario(String vehiculoValorMonetario) {
    this.vehiculoValorMonetario = vehiculoValorMonetario;
  }

  public String getVehiculoMonedaValorMonetario() {
    return vehiculoMonedaValorMonetario;
  }

  public void setVehiculoMonedaValorMonetario(String vehiculoMonedaValorMonetario) {
    this.vehiculoMonedaValorMonetario = vehiculoMonedaValorMonetario;
  }

  public String getVehiculoConversionMotor() {
    return vehiculoConversionMotor;
  }

  public void setVehiculoConversionMotor(String vehiculoConversionMotor) {
    this.vehiculoConversionMotor = vehiculoConversionMotor;
  }

  public String getVehiculoTipoCirculacion() {
    return vehiculoTipoCirculacion;
  }

  public void setVehiculoTipoCirculacion(String vehiculoTipoCirculacion) {
    this.vehiculoTipoCirculacion = vehiculoTipoCirculacion;
  }

  public String getPrimaMonto() {
    return primaMonto;
  }

  public void setPrimaMonto(String primaMonto) {
    this.primaMonto = primaMonto;
  }

  public String getPrimaMontoMoneda() {
    return primaMontoMoneda;
  }

  public void setPrimaMontoMoneda(String primaMontoMoneda) {
    this.primaMontoMoneda = primaMontoMoneda;
  }

  public String getPrimaTotalMonto() {
    return primaTotalMonto;
  }

  public void setPrimaTotalMonto(String primaTotalMonto) {
    this.primaTotalMonto = primaTotalMonto;
  }

  public String getPrimaTotalMontoMoneda() {
    return primaTotalMontoMoneda;
  }

  public void setPrimaTotalMontoMoneda(String primaTotalMontoMoneda) {
    this.primaTotalMontoMoneda = primaTotalMontoMoneda;
  }

  public String getPlanName() {
    return planName;
  }

  public void setPlanName(String planName) {
    this.planName = planName;
  }

  public String getPrimaPeriodo() {
    return primaPeriodo;
  }

  public void setPrimaPeriodo(String primaPeriodo) {
    this.primaPeriodo = primaPeriodo;
  }

  public String getInformacionBanco() {
    return informacionBanco;
  }

  public void setInformacionBanco(String informacionBanco) {
    this.informacionBanco = informacionBanco;
  }

  public String getCodigoOficina() {
    return codigoOficina;
  }

  public void setCodigoOficina(String codigoOficina) {
    this.codigoOficina = codigoOficina;
  }

  public String getCodigoUsuario() {
    return codigoUsuario;
  }

  public void setCodigoUsuario(String codigoUsuario) {
    this.codigoUsuario = codigoUsuario;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public Date getFechaRegistroDesde() {
    return fechaRegistroDesde;
  }

  public void setFechaRegistroDesde(Date fechaRegistroDesde) {
    this.fechaRegistroDesde = fechaRegistroDesde;
  }

  public Date getFechaRegistroHasta() {
    return fechaRegistroHasta;
  }

  public void setFechaRegistroHasta(Date fechaRegistroHasta) {
    this.fechaRegistroHasta = fechaRegistroHasta;
  }

  public String getIdProducto() {
    return idProducto;
  }

  public void setIdProducto(String idProducto) {
    this.idProducto = idProducto;
  }
}
