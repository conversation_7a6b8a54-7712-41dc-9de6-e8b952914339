package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import pe.com.bbva.gifole.common.bean.RaspaYGanaDetalleBean;
import pe.com.bbva.gifole.util.Utilitario;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Raspa y Gana")
@Route(value = "reporte/otros/consulta-raspa-y-gana", layout = MainLayout.class)
public class ConsultaRaspaYGanaView extends VerticalLayout {

  public ConsultaRaspaYGanaView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Raspa y Gana");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    DataTable<RaspaYGanaDetalleBean> dataTable =
        DataTable.<RaspaYGanaDetalleBean>builder()
            .id("tabla-raspa-y-gana")
            .column(
                "codigoCentral",
                "Código de Cliente",
                detalle -> detalle.getRaspaYGanaBean().getCodigoCentral(),
                "100px")
            .column(
                "tipoDocumento",
                "Tipo Documento",
                detalle -> detalle.getRaspaYGanaBean().getTipoDocumento(),
                "60px")
            .column(
                "numeroDocumento",
                "Número de Documento",
                detalle -> detalle.getRaspaYGanaBean().getNroDocumento(),
                "80px")
            .column(
                "nombresApellidos",
                "Nombre Cliente",
                detalle -> detalle.getRaspaYGanaBean().getNombreCompletoCliente(),
                "280px")
            .column(
                "fechaHoraRegistro",
                "Fecha Registro",
                detalle -> Utilitario.formatearFecha(detalle.getCreacion(), "dd/MM/yyyy HH:mm"),
                "170px")
            .column(
                "metodoPago",
                "Método de Pago",
                detalle -> detalle.getRaspaYGanaBean().getMetodoPago(),
                "100px")
            .column(
                "numeroCuenta",
                "Número de Cuenta",
                detalle -> detalle.getRaspaYGanaBean().getNumeroCuenta(),
                "100px")
            .column(
                "numeroTarjeta",
                "Número de Tarjeta",
                detalle -> detalle.getRaspaYGanaBean().getNumeroTarjeta(),
                "100px")
            .column(
                "servicio",
                "Tipo de Servicio",
                detalle -> detalle.getRaspaYGanaBean().getServicio(),
                "100px")
            .column(
                "empresa", "Empresa", detalle -> detalle.getRaspaYGanaBean().getEmpresa(), "100px")
            .column("divisa", "Divisa", detalle -> detalle.getRaspaYGanaBean().getDivisa(), "100px")
            .column(
                "codigoSorteo",
                "Código Raspa y Gana",
                detalle -> detalle.getRaspaYGanaBean().getCodigoSorteo(),
                "100px")
            .column(
                "deeplink",
                "¿Operación por Deep Link?",
                detalle -> detalle.getRaspaYGanaBean().getDeeplink(),
                "100px")
            .column("canal", "Canal", detalle -> detalle.getRaspaYGanaBean().getCanal(), "70px")
            .column("correo", "Correo", detalle -> detalle.getRaspaYGanaBean().getCorreo(), "200px")
            .column(
                "telefono",
                "Teléfono de contacto",
                detalle -> detalle.getRaspaYGanaBean().getTelefono(),
                "100px")
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
