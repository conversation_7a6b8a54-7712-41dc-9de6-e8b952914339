package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.SisAuditoriaBean;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Auditoría Tarifario")
@Route(value = "reporte/auditoria-tarifario", layout = MainLayout.class)
public class ConsultaAuditoriaTarifarioView extends VerticalLayout {

  public ConsultaAuditoriaTarifarioView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Auditoría Tarifario");
    title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder, similar a ConsultaVentaSeguroRoyalView
    DataTable<SisAuditoriaBean> dataTable =
        DataTable.<SisAuditoriaBean>builder()
            .id("tabla-auditoria-tarifario") // ID único para la tabla
            .column(
                "usuario", "Usuario", bean -> StringUtils.trimToEmpty(bean.getUsuario()), "160px")
            .column(
                "fecha",
                "Fecha",
                bean -> bean.getFecha() != null ? bean.getFecha().toString() : "",
                "160px") // Manejo de null
            .column(
                "id_registro",
                "ID Registro",
                bean -> StringUtils.trimToEmpty(bean.getId_registro()),
                "200px") // Corregido el nombre del getter
            .column(
                "data",
                "Data",
                bean -> StringUtils.trimToEmpty(bean.getData()),
                "10500px") // Ancho grande para 'data' como en el original
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
