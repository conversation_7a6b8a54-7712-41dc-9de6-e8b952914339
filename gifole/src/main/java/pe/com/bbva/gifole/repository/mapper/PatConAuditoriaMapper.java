package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.PrestamoAlToqueBean;

@Component
public class PatConAuditoriaMapper implements RowMapper<PrestamoAlToqueBean> {

  @Override
  public PrestamoAlToqueBean mapRow(ResultSet rs, int i) throws SQLException {

    PrestamoAlToqueBean prestamoAlToqueBean = new PrestamoAlToqueBean();
    prestamoAlToqueBean.setId(rs.getLong("ID"));
    prestamoAlToqueBean.setCodigoCentral(rs.getString("CODIGO_CENTRAL"));
    prestamoAlToqueBean.setNombreCompleto(rs.getString("NOMBRE_COMPLETO"));
    prestamoAlToqueBean.setCorreo(rs.getString("CORREO"));
    prestamoAlToqueBean.setNumeroContrato(rs.getString("NUMERO_CONTRATO"));
    prestamoAlToqueBean.setFechaRegistro(rs.getTimestamp("FECHA_CAMBIO_ESTADO"));
    prestamoAlToqueBean.setEstado(rs.getString("ESTADO"));
    prestamoAlToqueBean.setCanal(rs.getString("CANAL"));
    prestamoAlToqueBean.setCorreoExtorno(rs.getString("CORREO_EXTORNO"));

    return prestamoAlToqueBean;
  }
}
