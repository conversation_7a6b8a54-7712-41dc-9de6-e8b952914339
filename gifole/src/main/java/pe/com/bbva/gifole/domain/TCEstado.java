package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "TC_ESTADO")
public class TCEstado implements Serializable {
  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_TC_ESTADO")
  @TableGenerator(
      name = "SEQ_TC_ESTADO",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.TCEstado",
      allocationSize = 1)
  private Long id;

  @Column(name = "NOMBRE", length = 40)
  private String nombre;

  @Column(name = "CREACION")
  private Date creacion;

  @Column(name = "CREADOR", length = 20)
  private String creador;

  @Column(name = "OBSERVACION", length = 200)
  private String observacion;
}
