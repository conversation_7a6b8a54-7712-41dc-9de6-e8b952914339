package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.datepicker.DatePicker;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;

@PageTitle("Extorno Período de Gracia")
@Route(value = "extorno-periodo-gracia", layout = MainLayout.class)
public class ExtornoPeriodoGraciaView extends VerticalLayout {

  // Filtros
  private final DatePicker fechaDesdeField = new DatePicker("Desde");
  private final DatePicker fechaHastaField = new DatePicker("Hasta");
  private final TextField codigoCentralField = new TextField("Código central");
  private final TextField numeroContratoField = new TextField("Número de contrato");
  private final TextField estadoExtornoField = new TextField("Estado extorno");

  public ExtornoPeriodoGraciaView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Filtros de Extorno de período de gracia");
    title.addClassName("bbva-grid-title");

    // Panel de filtros
    HorizontalLayout filtersPanel = createFiltersPanel();

    mainLayout.add(title, filtersPanel);
    add(mainLayout);
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.END);

    fechaDesdeField.addClassName("bbva-input-floating");
    fechaDesdeField.setWidth("150px");

    fechaHastaField.addClassName("bbva-input-floating");
    fechaHastaField.setWidth("150px");

    codigoCentralField.addClassName("bbva-input-floating");
    codigoCentralField.setWidth("180px");
    codigoCentralField.setPlaceholder("Ingrese código");

    numeroContratoField.addClassName("bbva-input-floating");
    numeroContratoField.setWidth("220px");
    numeroContratoField.setPlaceholder("Ingrese contrato");

    estadoExtornoField.addClassName("bbva-input-floating");
    estadoExtornoField.setWidth("150px");
    estadoExtornoField.setPlaceholder("Ej: REGISTRADO");

    filtersPanel.add(
        fechaDesdeField,
        fechaHastaField,
        codigoCentralField,
        numeroContratoField,
        estadoExtornoField);

    return filtersPanel;
  }
}
