package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import pe.com.bbva.gifole.common.bean.PrestamoAlToqueBean;

@PageTitle("Extorno Período de Gracia")
@Route(value = "extorno-periodo-gracia", layout = MainLayout.class)
public class ExtornoPeriodoGraciaView extends VerticalLayout {

  // Constantes
  private static final String REGISTRADO = "REGISTRADO";
  private static final String PROCESADO = "PROCESADO";
  private static final String EXTORNADO = "EXTORNADO";
  private static final String BANCA_POR_INTERNET = "BANCA POR INTERNET";
  private static final String ZONA_PUB = "ZONA_PUB";
  private static final String PROCESAR_EXTORNO = "Procesar Extorno";

  private final Grid<PrestamoAlToqueBean> grid = new Grid<>(PrestamoAlToqueBean.class, false);
  private final List<PrestamoAlToqueBean> extornosList = new ArrayList<>();

  public ExtornoPeriodoGraciaView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    // Layout principal
    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Card para el Grid
    VerticalLayout gridCard = createGridCard();

    mainLayout.add(gridCard);
    mainLayout.setFlexGrow(1, gridCard);

    add(mainLayout);

    // Cargar datos de ejemplo
    loadSampleData();
  }

  private VerticalLayout createGridCard() {
    VerticalLayout gridCard = new VerticalLayout();
    gridCard.setSizeFull();
    gridCard.addClassName("bbva-grid-card");

    H2 gridTitle = new H2("Bandeja de Extornos de período de gracia");
    gridTitle.addClassName("bbva-grid-title");

    configureGrid();
    gridCard.add(gridTitle, grid);

    return gridCard;
  }

  private void configureGrid() {
    grid.setSizeFull();
    grid.addClassName("extorno-periodo-gracia-grid");

    // Configurar columnas con anchos automáticos
    grid.addColumn(prestamo -> formatDate(prestamo.getFechaRegistro()))
        .setHeader("Fecha Registro")
        .setSortable(true);

    grid.addColumn(PrestamoAlToqueBean::getCodigoCentral)
        .setHeader("Cod Central")
        .setSortable(true);

    grid.addColumn(PrestamoAlToqueBean::getNombreCompleto)
        .setHeader("Nombres Titular")
        .setSortable(true);

    grid.addColumn(PrestamoAlToqueBean::getCorreoExtorno).setHeader("Correo").setSortable(true);

    grid.addColumn(PrestamoAlToqueBean::getNumeroContrato).setHeader("Contrato").setSortable(true);

    grid.addColumn(PrestamoAlToqueBean::getEstadoExtorno)
        .setHeader("Estado extorno período de gracia")
        .setSortable(true);

    // Columna de acciones con botón Procesar Extorno
    grid.addComponentColumn(this::createProcessExtornoButton).setHeader("Acciones");

    // Configurar selección
    grid.setSelectionMode(Grid.SelectionMode.SINGLE);
  }

  private Button createProcessExtornoButton(PrestamoAlToqueBean prestamo) {
    Button processButton = new Button(PROCESAR_EXTORNO);
    processButton.addThemeVariants(ButtonVariant.LUMO_ERROR, ButtonVariant.LUMO_SMALL);
    processButton.addClassName("process-extorno-button");

    // Solo habilitar si el estado permite extorno
    boolean canExtorno =
        PROCESADO.equals(prestamo.getEstadoPeriodoGracia())
            && !EXTORNADO.equals(prestamo.getEstadoExtorno());
    processButton.setEnabled(canExtorno);

    processButton.addClickListener(e -> procesarExtorno(prestamo));

    return processButton;
  }

  private void procesarExtorno(PrestamoAlToqueBean prestamo) {
    // Simular procesamiento de extorno
    prestamo.setEstadoExtorno(EXTORNADO);
    prestamo.setFechaExtorno(new Date());

    grid.getDataProvider().refreshItem(prestamo);

    Notification.show("Extorno procesado exitosamente para: " + prestamo.getNombreCompleto())
        .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
  }

  private String formatDate(Date date) {
    if (date == null) return "";
    SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
    return sdf.format(date);
  }

  private void loadSampleData() {
    // Datos de ejemplo para extornos de período de gracia
    // Algunos con período de gracia ya procesado que pueden ser extornados

    PrestamoAlToqueBean item1 =
        createExtornoItem(
            createDate(2021, 3, 15, 10, 30, 0),
            "77780175",
            "SEBASTIAN ROCA VIZCARRA",
            "<EMAIL>",
            "001108149600015703615",
            PROCESADO,
            REGISTRADO);
    setItemCanal(item1, BANCA_POR_INTERNET);
    extornosList.add(item1);

    PrestamoAlToqueBean item2 =
        createExtornoItem(
            createDate(2021, 4, 20, 14, 45, 30),
            "67167037",
            "JONATHAN LEON CRUZ",
            "<EMAIL>",
            "001108149600055864212",
            PROCESADO,
            REGISTRADO);
    setItemCanal(item2, BANCA_POR_INTERNET);
    extornosList.add(item2);

    PrestamoAlToqueBean item3 =
        createExtornoItem(
            createDate(2021, 5, 10, 9, 15, 45),
            "77781327",
            "FERNANDA UNO CASTILLO LEON",
            "<EMAIL>",
            "001108141596000262417",
            PROCESADO,
            EXTORNADO);
    setItemCanal(item3, ZONA_PUB);
    extornosList.add(item3);

    PrestamoAlToqueBean item4 =
        createExtornoItem(
            createDate(2021, 6, 5, 16, 20, 10),
            "12345672",
            "MARIA PAREDES SANCHEZ",
            "<EMAIL>",
            "001108141596001554212",
            PROCESADO,
            REGISTRADO);
    setItemCanal(item4, BANCA_POR_INTERNET);
    extornosList.add(item4);

    PrestamoAlToqueBean item5 =
        createExtornoItem(
            createDate(2021, 7, 12, 11, 30, 25),
            "88888888",
            "DIEGO REYNOSO NISHIDA",
            "<EMAIL>",
            "001108140200615889874",
            PROCESADO,
            EXTORNADO);
    setItemCanal(item5, BANCA_POR_INTERNET);
    extornosList.add(item5);

    // Configurar grid con todos los datos
    grid.setItems(extornosList);
  }

  private PrestamoAlToqueBean createExtornoItem(
      Date fechaRegistro,
      String codigoCentral,
      String nombreCompleto,
      String correoExtorno,
      String numeroContrato,
      String estadoPeriodoGracia,
      String estadoExtorno) {
    PrestamoAlToqueBean item = new PrestamoAlToqueBean();
    item.setFechaRegistro(fechaRegistro);
    item.setCodigoCentral(codigoCentral);
    item.setNombreCompleto(nombreCompleto);
    item.setCorreoExtorno(correoExtorno);
    item.setNumeroContrato(numeroContrato);
    item.setEstadoPeriodoGracia(estadoPeriodoGracia);
    item.setEstadoExtorno(estadoExtorno);
    return item;
  }

  private void setItemCanal(PrestamoAlToqueBean item, String canal) {
    item.setCanal(canal);
  }

  @SuppressWarnings("deprecation")
  private Date createDate(int year, int month, int day, int hour, int minute, int second) {
    return new Date(year - 1900, month - 1, day, hour, minute, second);
  }
}
