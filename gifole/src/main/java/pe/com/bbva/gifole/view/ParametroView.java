package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.formlayout.FormLayout;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.IntegerField;
import com.vaadin.flow.component.textfield.TextArea;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.data.binder.Binder;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import java.util.List;
import pe.com.bbva.gifole.domain.Parametro;

@PageTitle("Mantenimiento de Parámetros")
@Route(value = "parametro", layout = MainLayout.class)
public class ParametroView extends VerticalLayout {

  private final Grid<Parametro> grid = new Grid<>(Parametro.class, false);
  private final Binder<Parametro> binder = new Binder<>(Parametro.class);
  private final List<Parametro> parametros = new ArrayList<>();

  // Form fields
  private final TextField codigo = new TextField("Código");
  private final TextField nombre = new TextField("Nombre");
  private final TextField valor = new TextField("Valor");
  private final ComboBox<String> tipo = new ComboBox<>("Tipo");
  private final IntegerField orden = new IntegerField("Orden");
  private final ComboBox<String> estado = new ComboBox<>("Estado");
  private final TextArea descripcion = new TextArea("Descripción");

  public ParametroView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    // Layout principal con clase CSS
    HorizontalLayout mainLayout = new HorizontalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);

    // Card para el Grid
    VerticalLayout gridCard = new VerticalLayout();
    gridCard.setSizeFull();
    gridCard.addClassName("bbva-grid-card");

    H2 gridTitle = new H2("Lista de Parametros");
    gridTitle.addClassName("bbva-grid-title");
    configureGrid();
    gridCard.add(gridTitle, grid);

    // Card para el Formulario
    VerticalLayout formCard = new VerticalLayout();
    formCard.setWidth("500px");
    formCard.addClassName("bbva-card");

    VerticalLayout formLayout = createForm();
    formCard.add(formLayout);

    mainLayout.add(gridCard, formCard);
    mainLayout.setFlexGrow(2, gridCard);
    mainLayout.setFlexGrow(1, formCard);

    add(mainLayout);

    // Cargar datos de ejemplo
    loadSampleData();
  }

  private void configureGrid() {
    grid.setSizeFull();
    grid.addColumn(Parametro::getCodigo).setHeader("Código").setSortable(true);
    grid.addColumn(Parametro::getNombre).setHeader("Nombre").setSortable(true);
    grid.addColumn(Parametro::getValor).setHeader("Valor");
    grid.addColumn(Parametro::getTipo).setHeader("Tipo").setSortable(true);
    grid.addColumn(Parametro::getOrden).setHeader("Orden").setSortable(true);
    grid.addColumn(Parametro::getEstado).setHeader("Estado").setSortable(true);

    grid.asSingleSelect()
        .addValueChangeListener(
            event -> {
              if (event.getValue() != null) {
                binder.setBean(event.getValue());
              } else {
                binder.setBean(new Parametro());
              }
            });
  }

  private VerticalLayout createForm() {
    VerticalLayout formWrapper = new VerticalLayout();
    formWrapper.setWidth("400px");
    formWrapper.setPadding(true);

    H2 title = new H2("Gestionar Parámetro");
    title.addClassName("bbva-page-title");

    FormLayout form = new FormLayout();

    // Configurar ComboBoxes
    tipo.setItems("Configuración", "Parámetro", "Constante", "Variable");
    estado.setItems("Activo", "Inactivo");

    // Configurar campos
    descripcion.setWidthFull();
    descripcion.setHeight("80px");
    orden.setMin(1);
    orden.setMax(999);
    orden.setStepButtonsVisible(true);

    form.add(codigo, nombre, valor, tipo, orden, estado, descripcion);
    form.setColspan(descripcion, 2); // Descripción ocupa 2 columnas

    // Vincular campos al binder
    binder.bindInstanceFields(this);
    binder.setBean(new Parametro());

    // Botones
    Button saveButton = new Button("Guardar", e -> saveParametro());
    saveButton.addThemeVariants(ButtonVariant.LUMO_PRIMARY);

    Button cancelButton = new Button("Cancelar", e -> binder.setBean(new Parametro()));

    HorizontalLayout buttonLayout = new HorizontalLayout(saveButton, cancelButton);

    formWrapper.add(title, form, buttonLayout);
    return formWrapper;
  }

  private void saveParametro() {
    Parametro parametro = binder.getBean();
    if (parametro != null) {
      if (!parametros.contains(parametro)) {
        parametros.add(parametro);
      } // else: es una edición, que el binder maneja en la instancia
      grid.setItems(parametros);
      binder.setBean(new Parametro()); // Limpiar formulario
    }
  }

  private void loadSampleData() {
    Parametro parametro1 = new Parametro();
    parametro1.setCodigo("API_URL");
    parametro1.setNombre("URL del API de Servicios");
    parametro1.setValor("https://api.example.com/v1");
    parametro1.setTipo("Configuración");
    parametro1.setOrden(1);
    parametro1.setEstado("Activo");
    parametro1.setDescripcion("URL principal para conectar con los servicios externos");
    parametros.add(parametro1);

    Parametro parametro2 = new Parametro();
    parametro2.setCodigo("TIMEOUT");
    parametro2.setNombre("Timeout de Conexión");
    parametro2.setValor("5000");
    parametro2.setTipo("Parámetro");
    parametro2.setOrden(2);
    parametro2.setEstado("Activo");
    parametro2.setDescripcion("Tiempo máximo de espera para conexiones en milisegundos");
    parametros.add(parametro2);

    // parametros.add(new Parametro("URL_API", "URL del API de Servicios",
    // "https://api.example.com/v1",
    // "Configuración", 1, "Activo", "URL principal para conectar con los servicios externos"));
    // parametros.add(new Parametro("TIMEOUT", "Timeout de Conexión", "5000", "Parámetro", 2,
    // "Activo", "Tiempo
    // máximo de espera para conexiones en milisegundos"));
    // parametros.add(new Parametro("MAX_USERS", "Máximo de Usuarios", "100", "Constante", 3,
    // "Inactivo", "Número
    // máximo de usuarios concurrentes permitidos"));
    // parametros.add(new Parametro("APP_VERSION", "Versión de la Aplicación", "1.0.0", "Variable",
    // 4, "Activo",
    // "Versión actual del sistema"));
    grid.setItems(parametros);
  }
}
