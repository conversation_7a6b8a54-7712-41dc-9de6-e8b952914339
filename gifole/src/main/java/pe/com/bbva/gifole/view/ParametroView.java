package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;

@PageTitle("Mantenimiento de Parámetros")
@Route(value = "parametro", layout = MainLayout.class)
public class ParametroView extends VerticalLayout {

  // Filtros
  private final TextField codigoFilter = new TextField("Código");
  private final TextField nombreFilter = new TextField("Nombre");
  private final TextField valorFilter = new TextField("Valor");
  private final ComboBox<String> tipoFilter = new ComboBox<>("Tipo");
  private final ComboBox<String> estadoFilter = new ComboBox<>("Estado");

  public ParametroView() {
    addClassName("app-main");
    setSizeFull();
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Filtros de Mantenimiento de Parámetros");
    title.addClassName("bbva-page-title");

    // Panel de filtros
    HorizontalLayout filtersPanel = createFiltersPanel();

    mainLayout.add(title, filtersPanel);
    add(mainLayout);
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.END);
    filtersPanel.setSpacing(true);
    filtersPanel.setPadding(true);

    codigoFilter.addClassName("bbva-input-floating");
    codigoFilter.setPlaceholder("Filtrar por código...");
    codigoFilter.setWidth("150px");

    nombreFilter.addClassName("bbva-input-floating");
    nombreFilter.setPlaceholder("Filtrar por nombre...");
    nombreFilter.setWidth("200px");

    valorFilter.addClassName("bbva-input-floating");
    valorFilter.setPlaceholder("Filtrar por valor...");
    valorFilter.setWidth("150px");

    tipoFilter.addClassName("bbva-input-floating");
    tipoFilter.setItems("Configuración", "Parámetro", "Constante", "Variable");
    tipoFilter.setWidth("150px");

    estadoFilter.addClassName("bbva-input-floating");
    estadoFilter.setItems("Activo", "Inactivo");
    estadoFilter.setWidth("120px");

    filtersPanel.add(codigoFilter, nombreFilter, valorFilter, tipoFilter, estadoFilter);
    return filtersPanel;
  }
}
