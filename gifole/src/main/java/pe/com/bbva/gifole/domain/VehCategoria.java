package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_CATEGORIA")
public class VehCategoria implements Serializable {
  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_VEH_CATEGORIA")
  @TableGenerator(
      name = "SEQ_VEH_CATEGORIA",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.VehCategoria",
      allocationSize = 1)
  private Long id;

  // bidirectional many-to-one association to VEH_MARCA
  @ManyToOne
  @JoinColumn(name = "MARCA")
  private VehMarca marca;

  // bi-directional many-to-one association to VEH_MODELO
  @ManyToOne
  @JoinColumn(name = "MODELO")
  private VehModelo modelo;

  @Column(name = "CLASE", length = 4)
  private String clase;

  // bi-directional many-to-one association to VEH_TIPO
  @ManyToOne
  @JoinColumn(name = "TIPO")
  private VehTipo tipo;

  @Column(name = "CODIGO", length = 4)
  private String codigo;

  @Column(name = "NOMBRE", length = 30)
  private String nombre;

  @Column(name = "ESTADO", length = 1)
  private String estado;

  @Column(name = "CREACION", nullable = false, insertable = true, updatable = false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date creacion;

  @Column(name = "EDICION", insertable = false, updatable = true)
  @Temporal(TemporalType.TIMESTAMP)
  private Date edicion;
}
