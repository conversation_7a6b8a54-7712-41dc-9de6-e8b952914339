package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
public class TarjetaDisenioBean implements Serializable {

  private Long id;
  private String idTarjetaModelo;
  private String nroTarjeta;
  private String codigoCentral;
  private String oficinaCentro;
  private String canal;
  private Date fechaRegistro;
  private Date fechaModificacion;
  private String creador;
  private String editor;
  private String estadoActual;
  private String estadoTarjeta;
  private String nombreDisenio;

  private String fechaDeRegistro;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getIdTarjetaModelo() {
    return idTarjetaModelo;
  }

  public void setIdTarjetaModelo(String idTarjetaModelo) {
    this.idTarjetaModelo = idTarjetaModelo;
  }

  public String getNroTarjeta() {
    return nroTarjeta;
  }

  public void setNroTarjeta(String nroTarjeta) {
    this.nroTarjeta = nroTarjeta;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getOficinaCentro() {
    return oficinaCentro;
  }

  public void setOficinaCentro(String oficinaCentro) {
    this.oficinaCentro = oficinaCentro;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public Date getFechaRegistro() {
    return fechaRegistro;
  }

  public void setFechaRegistro(Date fechaRegistro) {
    this.fechaRegistro = fechaRegistro;
  }

  public Date getFechaModificacion() {
    return fechaModificacion;
  }

  public void setFechaModificacion(Date fechaModificacion) {
    this.fechaModificacion = fechaModificacion;
  }

  public String getCreador() {
    return creador;
  }

  public void setCreador(String creador) {
    this.creador = creador;
  }

  public String getEditor() {
    return editor;
  }

  public void setEditor(String editor) {
    this.editor = editor;
  }

  public String getEstadoActual() {
    return estadoActual;
  }

  public void setEstadoActual(String estadoActual) {
    this.estadoActual = estadoActual;
  }

  public String getEstadoTarjeta() {
    return estadoTarjeta;
  }

  public void setEstadoTarjeta(String estadoTarjeta) {
    this.estadoTarjeta = estadoTarjeta;
  }

  public String getFechaDeRegistro() {
    return fechaDeRegistro;
  }

  public void setFechaDeRegistro(String fechaDeRegistro) {
    this.fechaDeRegistro = fechaDeRegistro;
  }

  public String getNombreDisenio() {
    return nombreDisenio;
  }

  public void setNombreDisenio(String nombreDisenio) {
    this.nombreDisenio = nombreDisenio;
  }
}
