package pe.com.bbva.gifole.util;

import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.owasp.esapi.ESAPI;
import pe.com.bbva.gifole.domain.SisPerfil;

public class Utilitario implements Serializable {

  private static final long serialVersionUID = 1L;

  public static boolean validarLinkURL(String targetUrl) {
    boolean indicador = false;
    try {
      URL url = new URL(targetUrl);
      InputStream i = null;
      try {
        i = url.openStream();
      } catch (UnknownHostException ex) {

      } catch (IOException e) {

      } finally {
        if (i != null) {
          try {
            i.close();
          } catch (IOException e) {

          }
        }
      }
      if (i != null) {
        indicador = true;
      }
    } catch (MalformedURLException e) {

    }
    return indicador;
  }

  public static String formatearFecha(Date fecha, String pattern) {
    return new SimpleDateFormat(pattern).format(fecha);
  }

  public static String encode(String message) {
    message = message.replace('\n', '_').replace('\r', '_').replace('\t', '_');
    message = ESAPI.encoder().encodeForHTML(message);
    return message;
  }

  public static String generarRuta(String servicio) {
    return "http://localhost:8082/gifole-web-ui/" + servicio;
  }

  public static String obtenerPrimerNumero(String texto) {
    if (texto == null) return "";
    String resultado = "";
    String[] textoArray = texto.split(" ");
    for (String cadena : textoArray) {
      try {
        int numero = Integer.parseInt(cadena);
        resultado = numero + "";
        break;
      } catch (Exception e) {
      }
    }
    return resultado;
  }

  public static String obtenerParteEntera(String numeroStr) {
    if (numeroStr == null) return "";
    String resultado = "";
    try {
      double numeroDouble = Double.parseDouble(numeroStr);
      resultado = "" + ((int) numeroDouble);
    } catch (Exception e) {
    }
    return resultado;
  }

  public static String quitarTildes(String palabra) {
    String valor = palabra.replaceAll("á", "a");
    valor = valor.replaceAll("é", "e");
    valor = valor.replaceAll("í", "i");
    valor = valor.replaceAll("ó", "o");
    valor = valor.replaceAll("ú", "u");
    valor = valor.replaceAll("Á", "A");
    valor = valor.replaceAll("É", "E");
    valor = valor.replaceAll("Í", "I");
    valor = valor.replaceAll("Ó", "O");
    valor = valor.replaceAll("Ú", "U");
    return valor;
  }

  public static boolean esPerfilSinBotonesXLS(SisPerfil sisPerfil) {
    if (sisPerfil != null && sisPerfil.getDescripcion() != null) {
      return sisPerfil.getDescripcion().equalsIgnoreCase(Constante.PERFIL.BACK_OFFICE_TC);
    }
    return false;
  }

  public static boolean esPerfilAdmTC(SisPerfil sisPerfil) {
    if (sisPerfil != null && sisPerfil.getDescripcion() != null) {
      return sisPerfil.getDescripcion().equalsIgnoreCase(Constante.PERFIL.ADMINISTRADOR)
          || sisPerfil.getDescripcion().equalsIgnoreCase(Constante.PERFIL.ADMI_YMP2_REL_SYS)
          || sisPerfil.getDescripcion().equalsIgnoreCase(Constante.PERFIL.ADMIN_TARJETAS);
    }
    return false;
  }
}
