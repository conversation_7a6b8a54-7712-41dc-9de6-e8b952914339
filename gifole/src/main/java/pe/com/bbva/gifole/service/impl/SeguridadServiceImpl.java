package pe.com.bbva.gifole.service.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pe.com.bbva.gifole.common.bean.UsuarioBean;
import pe.com.bbva.gifole.domain.SisPerfil;
import pe.com.bbva.gifole.domain.Usuario;
import pe.com.bbva.gifole.exception.UsuarioNotFoundException;
import pe.com.bbva.gifole.repository.UsuarioRepository;
import pe.com.bbva.gifole.service.LDAP2Service;
import pe.com.bbva.gifole.service.SeguridadService;

@Service
public class SeguridadServiceImpl implements SeguridadService {

  @Autowired private UsuarioRepository usuarioRepository;

  @PersistenceContext private EntityManager entityManager;

  @Autowired private LDAP2Service ldap2Service;

  static final Logger logger = LogManager.getLogger(SeguridadServiceImpl.class);

  @Override
  public Usuario loginUsuarioEnAplicacion(String loginUsuario) {
    logger.info("SeguridadService-loginUsuarioEnAplicacion se consulta en BD Gifole");

    CriteriaBuilder cb = entityManager.getCriteriaBuilder();
    CriteriaQuery<Usuario> cq = cb.createQuery(Usuario.class);
    Root<Usuario> root = cq.from(Usuario.class);

    Predicate predicate = cb.conjunction();

    if (loginUsuario != null && StringUtils.isNotBlank(loginUsuario)) {
      predicate = cb.and(predicate, cb.equal(root.get("registro"), loginUsuario));
      // Asigna el predicado a la consulta
      cq.where(predicate);
      Usuario usuario = usuarioRepository.obtenerUsuario(cq);
      if (usuario != null) {
        return usuario;
      } else {
        throw new UsuarioNotFoundException("no se encontro al usuario: " + loginUsuario);
      }
    } else {
      throw new UsuarioNotFoundException("no se encontro al usuario: " + loginUsuario);
    }
    // return usuarioRepository.obtenerUsuario(cq);
  }

  private Usuario getUsuarioCustomizado(String registro) {
    Usuario usuarioCustom = new Usuario();
    usuarioCustom.setId(Long.valueOf(1));
    usuarioCustom.setRegistro(registro.toUpperCase());
    usuarioCustom.setNombre("DEFAULT");
    usuarioCustom.setPaterno("");
    usuarioCustom.setMaterno("");
    SisPerfil sp = new SisPerfil();
    sp.setId(Long.valueOf(1));
    sp.setEstado("A");
    usuarioCustom.setSisPerfil(sp);
    return usuarioCustom;
  }

  /** Metodo para validar un usuario en las tablas aplicativo de Gifole (BD) solamente. */
  @Override
  public Usuario loginEnGifole(String loginUsuario) {
    logger.info("\tSeguridadService-loginEnGifole: inicio ");
    Usuario usuario = loginUsuarioEnAplicacion(loginUsuario);
    if (usuario != null) {
      try {
        logger.info("[BD]-getRegistro: {} ", StringUtils.trimToEmpty(usuario.getRegistro()));
        logger.info("[BD]-getPaterno: {} ", StringUtils.trimToEmpty(usuario.getPaterno()));
      } catch (Exception e) {
        logger.error("\tloginEnGifole: Ha ocurrido una exception: {} ", e.getMessage());
        return null;
      }
    } else {
      logger.info("\tSeguridadService-loginEnGifole: No se pudo recuperar usuario en BD.");
    }
    logger.info("\tSeguridadService-loginEnGifole: fin");
    return usuario;
  }

  /**
   * Metodo para validar un usuario en LDAP de Empleado Mex, se utiliza un certificado para realizar
   * la conexion
   */
  @Override
  public Usuario loginLdapGlobalGiam(String loginUsuario, String cl) {
    logger.info("\tSeguridadService-loginLdapGlobalGiam: inicio ");
    Usuario usuario = null;
    try {
      UsuarioBean usuarioBean = ldap2Service.autenticacionLDAPGlobal(loginUsuario, cl);
      if (usuarioBean.isAuthError()) {
        logger.info("\tSeguridadService-loginLdapGlobalGiam devuelve isAuthError");
        return null;
      }

      Optional<List<String>> rolesLDAP = Optional.ofNullable(usuarioBean.getRolesLDAP());
      if (rolesLDAP.isPresent() && !rolesLDAP.get().isEmpty()) {
        String primerRol = rolesLDAP.get().get(0);
        usuario = getUsuarioCustomizado(loginUsuario);
        usuario.getSisPerfil().setDescripcion(primerRol);
        logger.info("\tSeguridadService-loginLdapGlobalGiam-getRolesLDAP: {} ", primerRol);

      } else {
        logger.info(
            "\tSeguridadService-loginLdapGlobalGiam: NO HAY NINGUN ROL EN GIAM PARA el usuario: {} ",
            loginUsuario);
      }
    } catch (Exception e) {
      logger.error(
          "\tSeguridadService-loginLdapGlobalGiam: Ha ocurrido una exception: {} ", e.getMessage());
      return null;
    }
    logger.info("\tSeguridadService-loginLdapGlobalGiam: fin");
    return usuario;
  }
}
