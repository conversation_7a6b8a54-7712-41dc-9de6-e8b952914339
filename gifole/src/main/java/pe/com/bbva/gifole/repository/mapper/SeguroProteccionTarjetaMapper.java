package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.SeguroProteccionTarjeta;

@Component
public class SeguroProteccionTarjetaMapper implements RowMapper<SeguroProteccionTarjeta> {

  @Override
  public SeguroProteccionTarjeta mapRow(ResultSet rs, int i) throws SQLException {
    SeguroProteccionTarjeta seguro = new SeguroProteccionTarjeta();
    seguro.setId(rs.getLong("ID"));
    seguro.setNumeroCertificado(rs.getString("NUMERO_CERTIFICADO"));
    seguro.setFechaEmision(rs.getTimestamp("FECHA_EMISION"));
    seguro.setFechaInicioSeguro(rs.getTimestamp("FECHA_INICIO_SEGURO"));
    seguro.setOficinaGestora(rs.getString("OFICINA_GESTORA"));
    seguro.setCodigoVendedor(rs.getString("COD_VENDEDOR"));
    seguro.setNumeroCtaTarjetaCargo(rs.getString("NUMERO_CTATARJETA_CARGO"));
    seguro.setNumeroTarjetaAsegurada(rs.getString("NUMERO_TARJETA_ASEGURADA"));
    seguro.setNombre(rs.getString("NOMBRE"));
    seguro.setTipoDocumento(rs.getString("TIPO_DOCUMENTO"));
    seguro.setDocumento(rs.getString("DOCUMENTO"));
    seguro.setFechaNacimiento(rs.getTimestamp("FECHA_NACIMIENTO"));
    seguro.setSexo(rs.getString("SEXO"));
    seguro.setCorreo(rs.getString("CORREO"));
    seguro.setEstadoCivil(rs.getString("ESTADO_CIVIL"));
    seguro.setTelefono(rs.getString("TELEFONO"));
    seguro.setDireccion(rs.getString("DIRECCION_ASEGURADO"));
    // seguro.setDireccionOpcional(rs.getString("DIRECCION_OPCIONAL"));
    seguro.setNombreTitular(rs.getString("NOMBRE_TIT"));
    seguro.setTipoDocumentoTitular(rs.getString("TIPO_DOCUMENTO_TIT"));
    seguro.setDocumentoTitular(rs.getString("DOCUMENTO_TIT"));
    seguro.setFechaNacimientoTitular(rs.getTimestamp("FECHA_NACIMIENTO_TIT"));
    seguro.setSexoTitular(rs.getString("SEXO_TIT"));
    seguro.setCorreoTitular(rs.getString("CORREO_TIT"));
    seguro.setEstadoCivilTitular(rs.getString("ESTADO_CIVIL_TIT"));
    seguro.setTelefonoTitular(rs.getString("TELEFONO_TIT"));
    seguro.setDireccionTitular(rs.getString("DIRECCION_TITULAR"));
    // seguro.setDireccionOpcionalTitular(rs.getString("DIRECCION_OPCIONAL_TIT"));
    seguro.setModalidad(rs.getString("MODALIDAD"));
    seguro.setPrimaSeguro(rs.getString("PRIMA_SEGURO"));
    seguro.setMoneda(rs.getString("MONEDA"));
    seguro.setFormaPago(rs.getString("FORMA_PAGO"));
    seguro.setLugarFechaEmision(rs.getString("LUGAR_FECHA_EMISION"));
    seguro.setFechaRegistro(rs.getTimestamp("FECHA_REGISTRO"));
    seguro.setCanal(StringUtils.trimToEmpty(rs.getString("CANAL")));
    seguro.setProcesado(rs.getString("PROCESADO"));
    seguro.setNombreEmailPT(rs.getString("NOMBRE_EMAIL"));
    return seguro;
  }
}
