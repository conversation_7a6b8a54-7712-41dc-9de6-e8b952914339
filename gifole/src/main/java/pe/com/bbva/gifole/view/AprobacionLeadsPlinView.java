package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.datepicker.DatePicker;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.List;
import java.util.Locale;

@PageTitle("Bandeja Leads Plin")
@Route(value = "aprobacion-leads-plin", layout = MainLayout.class)
public class AprobacionLeadsPlinView extends VerticalLayout {

  // Filtros
  private final DatePicker fechaDesde = new DatePicker();
  private final DatePicker fechaHasta = new DatePicker();
  private final TextField rucField = new TextField();
  private final TextField razonSocialField = new TextField();
  private final TextField correoField = new TextField();
  private final ComboBox<String> estadoCombo = new ComboBox<>();

  public AprobacionLeadsPlinView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Panel de filtros
    HorizontalLayout filtersPanel = createFiltersPanel();

    // Título
    H2 title = new H2("Bandeja Leads Plin");
    title.addClassName("bbva-grid-title");

    mainLayout.add(filtersPanel, title);
    add(mainLayout);
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setSpacing(false);
    filtersPanel.setPadding(false);
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.START);

    fechaDesde.setLabel("Fecha desde");
    fechaDesde.addClassName("bbva-input-floating");
    fechaDesde.setWidth("140px");
    fechaDesde.setClearButtonVisible(true);
    fechaDesde.setLocale(Locale.forLanguageTag("es-ES"));
    fechaDesde.setI18n(createSpanishDatePickerI18n());

    fechaHasta.setLabel("Fecha hasta");
    fechaHasta.addClassName("bbva-input-floating");
    fechaHasta.setWidth("140px");
    fechaHasta.setClearButtonVisible(true);
    fechaHasta.setLocale(Locale.forLanguageTag("es-ES"));
    fechaHasta.setI18n(createSpanishDatePickerI18n());

    rucField.setLabel("RUC");
    rucField.addClassName("bbva-input-floating");
    rucField.setWidth("120px");
    rucField.setClearButtonVisible(true);

    razonSocialField.setLabel("Razón Social");
    razonSocialField.addClassName("bbva-input-floating");
    razonSocialField.setWidth("180px");
    razonSocialField.setClearButtonVisible(true);

    correoField.setLabel("Correo electrónico");
    correoField.addClassName("bbva-input-floating");
    correoField.setWidth("180px");
    correoField.setClearButtonVisible(true);

    estadoCombo.setLabel("Estado");
    estadoCombo.addClassName("bbva-input-floating");
    estadoCombo.setWidth("120px");
    estadoCombo.setItems("Todos", "PENDIENTE", "APROBADO", "RECHAZADO");
    estadoCombo.setValue("Todos");

    filtersPanel.add(fechaDesde, fechaHasta, rucField, razonSocialField, correoField, estadoCombo);
    return filtersPanel;
  }

  private DatePicker.DatePickerI18n createSpanishDatePickerI18n() {
    DatePicker.DatePickerI18n i18n = new DatePicker.DatePickerI18n();
    i18n.setMonthNames(
        List.of(
            "Enero",
            "Febrero",
            "Marzo",
            "Abril",
            "Mayo",
            "Junio",
            "Julio",
            "Agosto",
            "Septiembre",
            "Octubre",
            "Noviembre",
            "Diciembre"));
    i18n.setWeekdays(
        List.of("Domingo", "Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado"));
    i18n.setWeekdaysShort(List.of("Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"));
    i18n.setFirstDayOfWeek(1);
    i18n.setToday("Hoy");
    i18n.setCancel("Cancelar");
    return i18n;
  }
}
