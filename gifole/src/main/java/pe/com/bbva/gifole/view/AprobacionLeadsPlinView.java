package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.datepicker.DatePicker;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import pe.com.bbva.gifole.common.bean.AfiliacionPlinBean;

@PageTitle("Bandeja Leads Plin")
@Route(value = "aprobacion-leads-plin", layout = MainLayout.class)
public class AprobacionLeadsPlinView extends VerticalLayout {

  // Constantes
  private static final String PENDIENTE = "PENDIENTE";
  private static final String APROBADO = "APROBADO";
  private static final String RECHAZADO = "RECHAZADO";

  private final Grid<AfiliacionPlinBean> grid = new Grid<>(AfiliacionPlinBean.class, false);
  private final List<AfiliacionPlinBean> leadsList = new ArrayList<>();
  private final List<AfiliacionPlinBean> allLeads = new ArrayList<>();

  // Variables de paginación
  private int currentPage = 0;
  private final int pageSize = 10;
  private int totalPages = 0;

  // Componentes de paginación
  private Button previousButton;
  private Button nextButton;
  private Span pageInfo;

  // Filtros con labels
  private final DatePicker fechaDesde = new DatePicker();
  private final DatePicker fechaHasta = new DatePicker();
  private final TextField rucField = new TextField();
  private final TextField razonSocialField = new TextField();
  private final TextField correoField = new TextField();
  private final ComboBox<String> estadoCombo = new ComboBox<>();

  public AprobacionLeadsPlinView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    // Crear layout principal
    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Panel de filtros
    HorizontalLayout filtersPanel = createFiltersPanel();

    // Card para el Grid
    VerticalLayout gridCard = new VerticalLayout();
    gridCard.setSizeFull();
    gridCard.addClassName("bbva-grid-card");

    H2 gridTitle = new H2("Bandeja Leads Plin");
    gridTitle.addClassName("bbva-grid-title");

    configureGrid();
    // Crear controles de paginación
    HorizontalLayout paginationControls = createPaginationControls();

    gridCard.add(gridTitle, grid, paginationControls);

    mainLayout.add(filtersPanel, gridCard);
    add(mainLayout);

    // Cargar datos de ejemplo
    loadSampleData();
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setSpacing(false);
    filtersPanel.setPadding(false);
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.START);

    // Configurar campos con labels flotantes y anchos en Java
    fechaDesde.setLabel("Fecha desde");
    fechaDesde.addClassName("bbva-input-floating");
    fechaDesde.setWidth("140px");
    fechaDesde.setClearButtonVisible(true);
    fechaDesde.setLocale(Locale.forLanguageTag("es-ES"));
    fechaDesde.setI18n(createSpanishDatePickerI18n());

    fechaHasta.setLabel("Fecha hasta");
    fechaHasta.addClassName("bbva-input-floating");
    fechaHasta.setWidth("140px");
    fechaHasta.setClearButtonVisible(true);
    fechaHasta.setLocale(Locale.forLanguageTag("es-ES"));
    fechaHasta.setI18n(createSpanishDatePickerI18n());

    rucField.setLabel("RUC");
    rucField.addClassName("bbva-input-floating");
    rucField.setWidth("120px");
    rucField.setClearButtonVisible(true);

    razonSocialField.setLabel("Razón Social");
    razonSocialField.addClassName("bbva-input-floating");
    razonSocialField.setWidth("180px");
    razonSocialField.setClearButtonVisible(true);

    correoField.setLabel("Correo electrónico");
    correoField.addClassName("bbva-input-floating");
    correoField.setWidth("180px");
    correoField.setClearButtonVisible(true);

    // Configurar combo de estado
    estadoCombo.setLabel("Estado");
    estadoCombo.addClassName("bbva-input-floating");
    estadoCombo.setWidth("120px");
    estadoCombo.setItems("Todos", PENDIENTE, APROBADO, RECHAZADO);
    estadoCombo.setValue("Todos");

    filtersPanel.add(fechaDesde, fechaHasta, rucField, razonSocialField, correoField, estadoCombo);
    return filtersPanel;
  }

  private void configureGrid() {
    grid.setSizeFull();
    grid.addClassName("bbva-grid");
    grid.setHeight("500px");

    // Configurar columnas según la imagen
    grid.addColumn(lead -> formatDate(lead.getFechaRegistro()))
        .setHeader("Fecha registro")
        .setSortable(true);

    grid.addColumn(AfiliacionPlinBean::getRuc).setHeader("Ruc").setSortable(true);

    grid.addColumn(AfiliacionPlinBean::getRazonSocial).setHeader("Razón Social").setSortable(true);

    grid.addColumn(AfiliacionPlinBean::getCorreo).setHeader("Correo").setSortable(true);

    grid.addColumn(AfiliacionPlinBean::getEstado).setHeader("Estado").setSortable(true);

    // Columna de acciones con botón de detalle
    grid.addComponentColumn(this::createDetailButton).setHeader("Detalle");

    // Configurar selección
    grid.setSelectionMode(Grid.SelectionMode.SINGLE);
  }

  private Button createDetailButton(AfiliacionPlinBean lead) {
    Button detailButton = new Button(new Icon(VaadinIcon.SEARCH));
    detailButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY, ButtonVariant.LUMO_SMALL);
    detailButton.addClassName("detail-button");
    detailButton.getElement().setAttribute("title", "Ver detalle");

    detailButton.addClickListener(e -> verDetalle(lead));

    return detailButton;
  }

  private void buscarLeads() {
    // Implementar lógica de búsqueda
    String mensaje = "Búsqueda realizada";
    if (fechaDesde.getValue() != null) {
      mensaje += " desde: " + fechaDesde.getValue();
    }
    if (fechaHasta.getValue() != null) {
      mensaje += " hasta: " + fechaHasta.getValue();
    }

    Notification.show(mensaje).addThemeVariants(NotificationVariant.LUMO_SUCCESS);
  }

  private void verDetalle(AfiliacionPlinBean lead) {
    String mensaje = "Ver detalle de: " + lead.getRazonSocial() + " (RUC: " + lead.getRuc() + ")";
    Notification.show(mensaje).addThemeVariants(NotificationVariant.LUMO_PRIMARY);
  }

  private String formatDate(Date date) {
    if (date == null) return "";
    SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
    return formatter.format(date);
  }

  private void loadSampleData() {
    // Agregar datos de ejemplo basados en la imagen
    leadsList.add(
        createLeadItem(
            createDate(2022, 5, 18, 6, 55, 17),
            "20721138334",
            "Jdjdjdj",
            "<EMAIL>",
            PENDIENTE));

    leadsList.add(
        createLeadItem(
            createDate(2022, 5, 18, 6, 1, 33),
            "20721138334",
            "Jejfjdjdkk",
            "<EMAIL>",
            PENDIENTE));

    leadsList.add(
        createLeadItem(
            createDate(2022, 5, 18, 6, 8, 20),
            "20721138334",
            "Kdkfjjdj",
            "<EMAIL>",
            PENDIENTE));

    leadsList.add(
        createLeadItem(
            createDate(2022, 5, 18, 6, 9, 3),
            "20721138334",
            "Kekejejej",
            "<EMAIL>",
            PENDIENTE));

    leadsList.add(
        createLeadItem(
            createDate(2022, 5, 19, 9, 11, 48),
            "20721138334",
            "Dllrldke",
            "<EMAIL>",
            PENDIENTE));

    // Agregar más datos para probar la paginación (total: 25 elementos)
    for (int i = 6; i <= 25; i++) {
      leadsList.add(
          createLeadItem(
              createDate(2022, 5, 18 - i, 6, i, 0),
              "2072113833" + i,
              "Empresa " + i + " S.A.C.",
              "empresa" + i + "@gmail.com",
              i % 3 == 0 ? APROBADO : (i % 3 == 1 ? PENDIENTE : RECHAZADO)));
    }

    allLeads.addAll(leadsList);
    updatePagination();
  }

  private HorizontalLayout createPaginationControls() {
    HorizontalLayout paginationLayout = new HorizontalLayout();
    paginationLayout.addClassName("bbva-pagination");
    paginationLayout.setWidthFull();
    paginationLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    paginationLayout.setAlignItems(FlexComponent.Alignment.CENTER);

    // Información de página
    pageInfo = new Span();
    pageInfo.addClassName("bbva-pagination-info");

    // Controles de navegación
    HorizontalLayout controls = new HorizontalLayout();
    controls.addClassName("bbva-pagination-controls");
    controls.setSpacing(true);

    previousButton = new Button("Anterior", VaadinIcon.ANGLE_LEFT.create());
    previousButton.addClassName("bbva-pagination-button");
    previousButton.addClickListener(e -> goToPreviousPage());

    nextButton = new Button("Siguiente", VaadinIcon.ANGLE_RIGHT.create());
    nextButton.addClassName("bbva-pagination-button");
    nextButton.addClickListener(e -> goToNextPage());

    controls.add(previousButton, nextButton);
    paginationLayout.add(pageInfo, controls);

    return paginationLayout;
  }

  private void updatePagination() {
    totalPages = (int) Math.ceil((double) allLeads.size() / pageSize);

    // Calcular índices
    int startIndex = currentPage * pageSize;
    int endIndex = Math.min(startIndex + pageSize, allLeads.size());

    // Obtener elementos de la página actual
    List<AfiliacionPlinBean> currentPageItems = allLeads.subList(startIndex, endIndex);
    grid.setItems(currentPageItems);

    // Actualizar información de página
    if (allLeads.isEmpty()) {
      pageInfo.setText("No hay elementos");
    } else {
      int displayStart = startIndex + 1;
      pageInfo.setText(
          String.format(
              "Mostrando %d-%d de %d elementos (Página %d de %d)",
              displayStart, endIndex, allLeads.size(), currentPage + 1, totalPages));
    }

    // Actualizar estado de botones
    previousButton.setEnabled(currentPage > 0);
    nextButton.setEnabled(currentPage < totalPages - 1);
  }

  private void goToPreviousPage() {
    if (currentPage > 0) {
      currentPage--;
      updatePagination();
    }
  }

  private void goToNextPage() {
    if (currentPage < totalPages - 1) {
      currentPage++;
      updatePagination();
    }
  }

  private AfiliacionPlinBean createLeadItem(
      Date fechaRegistro, String ruc, String razonSocial, String correo, String estado) {
    AfiliacionPlinBean lead = new AfiliacionPlinBean();
    lead.setFechaRegistro(fechaRegistro);
    lead.setRuc(ruc);
    lead.setRazonSocial(razonSocial);
    lead.setCorreo(correo);
    lead.setEstado(estado);
    return lead;
  }

  @SuppressWarnings("deprecation")
  private Date createDate(int year, int month, int day, int hour, int minute, int second) {
    return new Date(year - 1900, month - 1, day, hour, minute, second);
  }

  private DatePicker.DatePickerI18n createSpanishDatePickerI18n() {
    DatePicker.DatePickerI18n i18n = new DatePicker.DatePickerI18n();

    // Nombres de meses
    i18n.setMonthNames(
        List.of(
            "Enero",
            "Febrero",
            "Marzo",
            "Abril",
            "Mayo",
            "Junio",
            "Julio",
            "Agosto",
            "Septiembre",
            "Octubre",
            "Noviembre",
            "Diciembre"));

    // Días de la semana
    i18n.setWeekdays(
        List.of("Domingo", "Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado"));

    // Días de la semana cortos
    i18n.setWeekdaysShort(List.of("Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"));

    // Primer día de la semana (LUNES = 1)
    i18n.setFirstDayOfWeek(1);

    // Textos adicionales
    i18n.setToday("Hoy");
    i18n.setCancel("Cancelar");

    return i18n;
  }
}
