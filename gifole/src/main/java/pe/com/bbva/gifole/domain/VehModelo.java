package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_MODELO")
public class VehModelo implements Serializable {
  @Id
  @Column(name = "CODIGO", length = 8)
  private String codigo;

  @Column(name = "NOMBRE", length = 50)
  private String nombre;

  // bi-directional many-to-one association to VEH_MARCA
  @ManyToOne
  @JoinColumn(name = "MARCA")
  private VehMarca marca;

  @Column(name = "ESTADO", length = 1)
  private String estado;

  @Column(name = "CREACION", nullable = false, insertable = true, updatable = false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date creacion;

  @Column(name = "EDICION", insertable = false, updatable = true)
  @Temporal(TemporalType.TIMESTAMP)
  private Date edicion;
}
