package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "SIS_ERROR")
public class SisError implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_SIS_ERROR")
  @TableGenerator(
      name = "SEQ_SIS_ERROR",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.SisError",
      allocationSize = 1)
  private Long id;

  @Column(name = "CLASE")
  private String clase;

  @Column(name = "METODO")
  private String metodo;

  @Column(name = "TRAMA")
  private String trama;

  @Column(name = "ERROR")
  private String error;

  @Column(name = "CREADOR")
  private String creador;

  @Column(name = "FECHA_REGISTRO")
  private Date fechaCreacion;
}
