package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

import pe.com.bbva.gifole.domain.TCTocRemarketing;
import pe.com.bbva.gifole.util.Constante;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Lead Tarjeta OneClick Remarketing")
@Route(value = "reporte/leads/tarjeta-oneclick-remarketing", layout = MainLayout.class)
public class ConsultaLeadTarjetaOneClickRemarketingView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<TCTocRemarketing> allData = new ArrayList<>();
    private DataTable<TCTocRemarketing> dataTable;

    public ConsultaLeadTarjetaOneClickRemarketingView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Lead Tarjeta OneClick Remarketing");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<TCTocRemarketing>builder()
                .id("tabla-lead-tarjeta-oneclick-remarketing") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaLeadTarjetaOneClickRemarketingUI)
                .column("codigoCentral", "Código Central", 
                    bean -> StringUtils.trimToEmpty(bean.getCodigoCentral()), "100px")
                .column("tipoDocumento", "Tipo Documento", 
                    bean -> StringUtils.trimToEmpty(bean.getTipoDocumento()), "120px")
                .column("numeroDocumento", "Número Documento", 
                    bean -> StringUtils.trimToEmpty(bean.getNumeroDocumento()), "120px")
                .column("nombreCompleto", "Nombre Completo", 
                    bean -> StringUtils.trimToEmpty(bean.getNombreCompleto()), "200px")
                .column("telefono", "Teléfono", 
                    bean -> StringUtils.trimToEmpty(bean.getTelefono()), "100px")
                .column("correo", "Correo", 
                    bean -> StringUtils.trimToEmpty(bean.getCorreo()), "180px")
                .column("fechaRegistro", "Fecha Registro", 
                    bean -> bean.getFechaRegistro() != null ? FORMATO_FECHA.format(bean.getFechaRegistro()) : StringUtils.EMPTY, "150px")
                .column("nombreTarjeta", "Nombre Tarjeta", 
                    bean -> StringUtils.trimToEmpty(bean.getNombreTarjeta()), "150px")
                .column("tipoTarjeta", "Tipo Tarjeta", 
                    bean -> StringUtils.trimToEmpty(bean.getTipoTarjeta()), "120px")
                .column("limiteTarjeta", "Límite Tarjeta", 
                    bean -> StringUtils.trimToEmpty(bean.getMontoLimite()), "120px")
                .column("tea", "TEA", 
                    bean -> StringUtils.trimToEmpty(bean.getTea()), "100px")
                .column("tcea", "TCEA", 
                    bean -> StringUtils.trimToEmpty(bean.getTcea()), "100px")
                .column("tipoInformacion", "Tipo Información", 
                    bean -> StringUtils.trimToEmpty(bean.getTipoInformacion()), "150px")
                .column("fechaPago", "Fecha Pago", 
                    bean -> StringUtils.trimToEmpty(bean.getFechaPago()), "120px")
                .column("canal", "Canal", 
                    bean -> {
                        if (bean.getCanal() == null) {
                            return Constante.CANAL.NOMBRE_BANCA_POR_INTERNET;
                        } else if (bean.getCanal().equals(Constante.CANAL.BANCA_POR_INTERNET_UX)) {
                            return Constante.CANAL.NOMBRE_BANCA_POR_INTERNET;
                        }
                        return StringUtils.trimToEmpty(bean.getCanal());
                    }, "120px")
                .column("pasoAbandono", "Paso Abandono", 
                    bean -> StringUtils.trimToEmpty(bean.getPasoAbandono()), "120px")
                .column("marcaPH", "Marca PH", 
                    bean -> StringUtils.trimToEmpty(bean.getMarcaPh()), "100px")
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}