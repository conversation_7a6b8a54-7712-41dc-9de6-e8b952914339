package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "SEGURO_VEHICULAR")
public class SeguroVehicular implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SeqGenV")
  @SequenceGenerator(name = "SeqGenV", sequenceName = "SQ_SEGURO_VEHICULAR", allocationSize = 1)
  @Column(name = "ID")
  private Long id;

  @Column(name = "NOMBRE", length = 50)
  private String nombre;

  @Column(name = "APELLIDO", length = 50)
  private String apellido;

  @Column(name = "TIPO_DOCUMENTO", length = 1)
  private String tipoDocumento;

  @Column(name = "DOCUMENTO", length = 20)
  private String documento;

  @Column(name = "DEPARTAMENTO", length = 2)
  private String departamento;

  @Column(name = "CORREO", length = 80)
  private String correo;

  @Column(name = "TELEFONO", length = 20)
  private String telefono;

  @Column(name = "HORARIO", length = 20)
  private String horario;

  @Column(name = "ESTADO_PLACA", length = 20)
  private String estadoPlaca;

  @Column(name = "PLACA", length = 20)
  private String placa;

  @Column(name = "AUTORIZACION", length = 1)
  private String autorizacion;

  @Column(name = "FECHA_REGISTRO")
  private Date fechaRegistro;

  @Column(name = "PROCESADO", length = 1)
  private String procesado;

  @Column(name = "ANHO_FABRICACION", length = 4)
  private String anhoFabricacion;
}
