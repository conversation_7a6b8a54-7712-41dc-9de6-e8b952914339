package pe.com.bbva.gifole.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import javax.naming.AuthenticationException;
import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.SearchControls;
import javax.naming.directory.SearchResult;
import javax.naming.ldap.InitialLdapContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import pe.com.bbva.gifole.common.bean.UsuarioBean;
import pe.com.bbva.gifole.service.LDAP2Service;
import pe.com.bbva.gifole.util.Constants;
import pe.com.bbva.gifole.util.LDAPContext;
import pe.com.bbva.gifole.util.PropertiesExterno;

@Service
@SuppressWarnings("serial")
public class LDAP2ServiceImpl implements LDAP2Service {

  static final Logger logger = LogManager.getLogger(LDAP2ServiceImpl.class);
  static final String TAG = LDAP2ServiceImpl.class.getSimpleName();

  private transient PropertiesExterno propertiesExterno;

  @Override
  public UsuarioBean autenticacionLDAPGlobal(String loginUsuario, String cl) {
    logger.info("======== LDAP2DaoImpl-autenticacionLDAPGlobal(): inicio ========");
    String ldapUrl = Constants.RUTA_LDAP_GLOBAL;
    String ldapAppUser = propertiesExterno.getProperty("ldap.app.user");
    String ldapAppCl = propertiesExterno.getProperty("ldap.app.password");
    String ldapRamaRoles = Constants.RUTA_LDAP_GLOBAL_ROLES;
    String ldapRamaUsuarios = Constants.RUTA_LDAP_GLOBAL_USUARIOS;
    logger.info("LDAP2DaoImpl-autenticacionLDAPGlobal: LDAP_URL: {} ", ldapUrl);
    logger.info("LDAP2DaoImpl-autenticacionLDAPGlobal: LDAP_APP_USER: {} ", ldapAppUser);
    logger.info("LDAP2DaoImpl-autenticacionLDAPGlobal: LDAP_APP_CL: {} ", ldapAppCl);
    logger.info("LDAP2DaoImpl-autenticacionLDAPGlobal: LDAP_RAMA_ROLES: {} ", ldapRamaRoles);
    logger.info("LDAP2DaoImpl-autenticacionLDAPGlobal: LDAP_RAMA_USUARIOS: {} ", ldapRamaUsuarios);

    UsuarioBean usuarioBean = new UsuarioBean();

    InitialLdapContext ctx = null;
    String[] attributeToRetrieve = {"member", "cn"};
    List<String> roles = new ArrayList<>();
    NamingEnumeration<SearchResult> resultsRoles = null;
    NamingEnumeration<SearchResult> resultsUser = null;
    SearchControls searchControls = new SearchControls();
    Properties envLDAP = getHashtable(ldapUrl, ldapAppUser, ldapAppCl);
    String userDN = "";
    try {
      ctx = LDAPContext.getInstance(envLDAP);
      logger.info("Ldap conected! ");
      logger.info("LDAP2DaoImpl-autenticacionLDAPGlobal: LDAP CONECTED!");

      searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE);
      searchControls.setReturningAttributes(attributeToRetrieve);
      // Filter en la rama de usuarios
      String filterUser = "(&(cn=" + loginUsuario + "))";
      resultsUser = ctx.search(ldapRamaUsuarios, filterUser, null);

      if (resultsUser.hasMore()) {
        logger.info("LDAP2DaoImpl-autenticacionLDAPGlobal: resultsUser.hasMore()");
        // Get DN the user
        SearchResult result = resultsUser.next();
        userDN = result.getNameInNamespace();
        logger.info("userDN: {}", userDN);
        logger.info("LDAP2DaoImpl-autenticacionLDAPGlobal: userDN: {} ", userDN);
      }

      // Filter en la rama de roles
      String filterForRoles =
          "(&(objectClass=BBVAGroup)(member=cn="
              + loginUsuario
              + ",cn=Users,ou=native,o=igrupobbva))";
      logger.info("LDAP2DaoImpl-autenticacionLDAPGlobal: filterForRoles: {}", filterForRoles);

      // go search
      resultsRoles = ctx.search(ldapRamaRoles, filterForRoles, searchControls);
      logger.info("Se busca al usuario {}, en Ldap", loginUsuario);
      logger.info(
          "LDAP2DaoImpl-autenticacionLDAPGlobal: Se busca al usuario {}, en Ldap ", loginUsuario);

      if (resultsRoles.hasMore()) {
        logger.info("Se encontro el usuario en el LDAP de empleados");
        logger.info(
            "LDAP2DaoImpl-autenticacionLDAPGlobal: Se encontro el usuario en el LDAP de empleados ");
        while (resultsRoles.hasMore()) {
          SearchResult result = resultsRoles.next();
          Attributes attributes = result.getAttributes();
          Attribute rolLDAP = attributes.get("cn");
          Attribute memberLDAP = attributes.get("member");
          logger.info("atributos: {}", rolLDAP); // ADMIN_TARJETAS
          logger.info("atributo: {}", memberLDAP); // cn=P031063,cn=Users,ou=native,o=igrupobbva
          logger.info("LDAP2DaoImpl-autenticacionLDAPGlobal:rolLDAP: {}", rolLDAP.get());
          logger.info("LDAP2DaoImpl-autenticacionLDAPGlobal:memberLDAP: {}", memberLDAP.get());

          roles.add(rolLDAP.get().toString());
        }
        // Set the roles
        usuarioBean.setRolesLDAP(roles);
        // Bind con el DN y cl
        ctx.addToEnvironment(Context.SECURITY_PRINCIPAL, userDN);
        ctx.addToEnvironment(Context.SECURITY_CREDENTIALS, cl);
        ctx.reconnect(null);
        logger.info("LDAP2DaoImpl-autenticacionLDAPGlobal: OK");
      } else {
        usuarioBean.setAuthError(true);
        logger.info("No se encontro el usuario en el LDAP de empleados");
        logger.info(
            "LDAP2DaoImpl-autenticacionLDAPGlobal - No se encontro el usuario en el LDAP de empleados.");
        return usuarioBean;
      }

    } catch (AuthenticationException e) {
      usuarioBean.setAuthError(true);
      logger.error("Credenciales de usuario invalidas: {}", e.getMessage());
      logger.error(TAG, e);
      logger.error(
          "LDAP2DaoImpl-autenticacionLDAPGlobal - AuthenticationException {} ", e.getMessage());
      return usuarioBean;
    } catch (NamingException e) {
      usuarioBean.setAuthError(true);
      logger.error(TAG, e);
      logger.error(TAG, e);
      logger.error("LDAP2DaoImpl-autenticacionLDAPGlobal - NamingException {} ", e.getMessage());
      return usuarioBean;
    } catch (Exception e) {
      usuarioBean.setAuthError(true);
      logger.error("Exception General: {} ", e.getMessage());
      logger.error(TAG, e);
      logger.error("LDAP2DaoImpl-autenticacionLDAPGlobal - Exception {} ", e.getMessage());
      return usuarioBean;
    } finally {
      try {
        if (ctx != null) {
          LDAPContext.closeConnection();
        }
      } catch (NamingException e) {
        logger.error("finally : {}", e.getMessage());
        logger.error(TAG, e);
        logger.error(
            "LDAP2DaoImpl-autenticacionLDAPGlobal - Exception finally {} ", e.getMessage());
        usuarioBean.setAuthError(true);
      }
    }
    usuarioBean.setAuthError(false);
    logger.info("========== LDAP2DaoImpl-autenticacionLDAPGlobal: fin ==========");
    return usuarioBean;
  }

  private static Properties getHashtable(String ldapUrl, String ldapAppuser, String ldapAppCl) {
    Properties env = System.getProperties();
    Properties envLDAP = (Properties) env.clone();
    envLDAP.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
    envLDAP.put(Context.PROVIDER_URL, ldapUrl);
    envLDAP.put(Context.SECURITY_PRINCIPAL, ldapAppuser);
    envLDAP.put(Context.SECURITY_CREDENTIALS, ldapAppCl);
    envLDAP.put(Context.SECURITY_AUTHENTICATION, "simple");
    return envLDAP;
  }
}
