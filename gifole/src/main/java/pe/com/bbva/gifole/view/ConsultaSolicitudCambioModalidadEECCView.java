package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.CambioModalidadEECCDetalleBean; // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.common.bean.CambioModalidadEECCBean;       // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.util.GifoleUtil; // Asumo que existe, ajusta según tu proyecto
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Solicitud Cambio Modalidad EECC")
@Route(value = "reporte/solicitudes/cambio-modalidad-eecc", layout = MainLayout.class)
public class ConsultaSolicitudCambioModalidadEECCView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA_HORA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<CambioModalidadEECCDetalleBean> allData = new ArrayList<>();
    private DataTable<CambioModalidadEECCDetalleBean> dataTable;

    public ConsultaSolicitudCambioModalidadEECCView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Solicitud Cambio Modalidad EECC");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<CambioModalidadEECCDetalleBean>builder()
                .id("tabla-solicitud-cambio-modalidad-eecc") // ID único para la tabla
                // Mapeo de columnas basado en la información proporcionada
                .column("codigoCentral", "Código Central", // Equivalente parcial a "numeroSolicitud"
                    bean -> StringUtils.trimToEmpty(
                        bean.getCambioModalidadEECCBean() != null ?
                        bean.getCambioModalidadEECCBean().getCodigoCentral() : ""), "120px")
                .column("nombresApellidos", "Cliente", // Equivalente a "cliente"
                    bean -> StringUtils.trimToEmpty(
                        bean.getCambioModalidadEECCBean() != null ?
                        bean.getCambioModalidadEECCBean().getNombreCompletoCliente() : ""), "250px")
                .column("tipoDocumento", "Tipo Documento", // Parte de identificación del cliente
                    bean -> StringUtils.trimToEmpty(
                        bean.getCambioModalidadEECCBean() != null ?
                        bean.getCambioModalidadEECCBean().getTipoDocumento() : ""), "120px")
                .column("nroDocumento", "Número Documento", // Parte de identificación del cliente
                    bean -> StringUtils.trimToEmpty(
                        bean.getCambioModalidadEECCBean() != null ?
                        bean.getCambioModalidadEECCBean().getNroDocumento() : ""), "120px")
                .column("fechaHoraRegistro", "Fecha Solicitud", // Equivalente a "fechaSolicitud"
                    bean -> bean.getCreacion() != null ? FORMATO_FECHA_HORA.format(bean.getCreacion()) : "", "170px")
                .column("subProducto", "Sub Producto", // Información del producto
                    bean -> StringUtils.trimToEmpty(
                        bean.getCambioModalidadEECCBean() != null ?
                        bean.getCambioModalidadEECCBean().getSubProducto() : ""), "150px")
                 .column("nroContrato", "Número Contrato", // Información del contrato (formateada)
                    bean -> StringUtils.trimToEmpty(
                        bean.getCambioModalidadEECCBean() != null ?
                        GifoleUtil.obtenerNumeroCuentaFormateado(
                            bean.getCambioModalidadEECCBean().getNroContrato()) : ""), "170px")
                .column("nroTarjetaTitular", "Tarjeta", // Equivalente a "tarjeta" (formateada)
                    bean -> StringUtils.trimToEmpty(
                        bean.getCambioModalidadEECCBean() != null ?
                        GifoleUtil.obtenerNumeroTarjetaFormateado(
                            bean.getCambioModalidadEECCBean().getNroTarjetaTitular()) : ""), "170px")
                 .column("tipoModalidadEnvio", "Modalidad Actual", // Asumiendo que este campo existe en CambioModalidadEECCBean
                    bean -> StringUtils.trimToEmpty(
                        bean.getCambioModalidadEECCBean() != null ?
                        bean.getCambioModalidadEECCBean().getTipoModalidadEnvio() : ""), "150px")
                 .column("nuevaModalidad", "Nueva Modalidad", // Asumiendo que este campo existe o se deriva
                    bean -> StringUtils.trimToEmpty(
                        // Si hay un campo específico para la nueva modalidad, úsalo aquí
                        // Por ahora, asumimos que se refiere al estado o a un campo calculado
                        // Ejemplo placeholder, ajusta según tu modelo:
                        bean.getCambioModalidadEECCBean() != null ?
                        "Nueva Modalidad" : ""), "150px") // Placeholder, ajusta
                .column("estado", "Estado", // Equivalente a "estado"
                    bean -> StringUtils.trimToEmpty(
                        bean.getCambioModalidadEECCBean() != null ?
                        bean.getCambioModalidadEECCBean().getEstado() : ""), "120px")
                .column("canal", "Canal", // Información del canal
                    bean -> StringUtils.trimToEmpty(
                        bean.getCambioModalidadEECCBean() != null ?
                        bean.getCambioModalidadEECCBean().getCanal() : ""), "100px")
                .column("correoContactoCliente", "Correo Contacto", // Información de contacto
                    bean -> StringUtils.trimToEmpty(
                        bean.getCambioModalidadEECCBean() != null ?
                        bean.getCambioModalidadEECCBean().getCorreoClienteContacto() : ""), "200px")
                // Puedes agregar más columnas aquí si las necesitas, siguiendo el mismo patrón
                // Nota: El DTO menciona "usuarioAprobacion", pero no se proporciona el mapeo.
                // Podría estar en el DetalleBean o requerir otra lógica.
                // Datos (inicialmente vacío)
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

    // Método para cargar datos, puedes conectarlo a un servicio
    private void loadData(List<CambioModalidadEECCDetalleBean> data) {
        // Limpiar datos existentes
        allData.clear();
        allData.addAll(data); // Asumiendo que 'data' viene del servicio

        // Actualizar la tabla con los nuevos datos
        dataTable.setItems(allData);
    }

    // Método auxiliar de ejemplo para formatear números de tarjeta si GifoleUtil no está disponible
    // private String obtenerNumeroTarjetaFormateado(String nroTarjeta) {
    //     // Implementa la lógica real aquí o usa GifoleUtil
    //     return StringUtils.defaultString(nroTarjeta);
    // }
}