package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.TCAdicionalDetEstado;
import pe.com.bbva.gifole.domain.TCAdicionalDetalle;
import pe.com.bbva.gifole.domain.TCEstado;
import pe.com.bbva.gifole.domain.TCMotivo;

@Component
public class AdicionalEstadoMapper implements RowMapper<TCAdicionalDetEstado> {

  @Override
  public TCAdicionalDetEstado mapRow(ResultSet rs, int i) throws SQLException {
    TCAdicionalDetalle tcAdicionalDetalle = new TCAdicionalDetalle();
    TCEstado tcEstado = new TCEstado();
    TCAdicionalDetEstado adicional = new TCAdicionalDetEstado();
    adicional.setTcAdicionalDetalle(new TCAdicionalDetalle());
    adicional.getTcAdicionalDetalle().setEstadoActual(new TCEstado());
    adicional.setTcMotivo(new TCMotivo());
    adicional.getTcMotivo().setNombre(rs.getString("MOTIVO"));
    adicional.setObservacion(rs.getString("OBSERVACION"));
    adicional.setCreacion(rs.getTimestamp("CREACION"));
    adicional.setCreador(rs.getString("CREADOR"));
    tcAdicionalDetalle.setId(rs.getLong("TC_ADICIONAL_DETALLE"));
    tcAdicionalDetalle.setNombres(rs.getString("adicional"));
    tcEstado.setNombre(rs.getString("ESTADO"));
    tcAdicionalDetalle.setEstadoActual(tcEstado);
    adicional.setTcAdicionalDetalle(tcAdicionalDetalle);
    return adicional;
  }
}
