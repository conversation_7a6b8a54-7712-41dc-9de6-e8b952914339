package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class CampaniaMultiplicaBean implements Serializable {

  private static final long serialVersionUID = 3196169962233040100L;
  private Long id;
  private String nombre;
  private String descripcion;
  private String divisa;
  private String valor1;
  private String valor2;
  private String rango1;
  private String rango2;
  private Date fechaInicio;
  private Date fechaInicio1;
  private Date fechaInicio2;
  private Date fechaFin;
  private Date fechaFin1;
  private Date fechaFin2;
  private String estado;
  private String canal;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public String getDescripcion() {
    return descripcion;
  }

  public void setDescripcion(String descripcion) {
    this.descripcion = descripcion;
  }

  public String getDivisa() {
    return divisa;
  }

  public void setDivisa(String divisa) {
    this.divisa = divisa;
  }

  public String getValor1() {
    return valor1;
  }

  public void setValor1(String valor1) {
    this.valor1 = valor1;
  }

  public String getValor2() {
    return valor2;
  }

  public void setValor2(String valor2) {
    this.valor2 = valor2;
  }

  public String getRango1() {
    return rango1;
  }

  public void setRango1(String rango1) {
    this.rango1 = rango1;
  }

  public String getRango2() {
    return rango2;
  }

  public void setRango2(String rango2) {
    this.rango2 = rango2;
  }

  public Date getFechaInicio() {
    return fechaInicio;
  }

  public void setFechaInicio(Date fechaInicio) {
    this.fechaInicio = fechaInicio;
  }

  public Date getFechaInicio1() {
    return fechaInicio1;
  }

  public void setFechaInicio1(Date fechaInicio1) {
    this.fechaInicio1 = fechaInicio1;
  }

  public Date getFechaInicio2() {
    return fechaInicio2;
  }

  public void setFechaInicio2(Date fechaInicio2) {
    this.fechaInicio2 = fechaInicio2;
  }

  public Date getFechaFin() {
    return fechaFin;
  }

  public void setFechaFin(Date fechaFin) {
    this.fechaFin = fechaFin;
  }

  public Date getFechaFin1() {
    return fechaFin1;
  }

  public void setFechaFin1(Date fechaFin1) {
    this.fechaFin1 = fechaFin1;
  }

  public Date getFechaFin2() {
    return fechaFin2;
  }

  public void setFechaFin2(Date fechaFin2) {
    this.fechaFin2 = fechaFin2;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }
}
