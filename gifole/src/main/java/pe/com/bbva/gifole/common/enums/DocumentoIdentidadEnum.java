package pe.com.bbva.gifole.common.enums;

public enum DocumentoIdentidadEnum {
  L("L", "DNI", "DNI"),
  R("R", "RUC", "RUC"),
  E("E", "Carnet de Extranjeria", "FOREIGNERS"),
  P("P", "<PERSON><PERSON><PERSON><PERSON>", "PASSPORT"),
  M("M", "Libreta Militar", "MILITARY"),
  T("T", "Carnet Policial", "POLICE"),
  D("T", "Carnet Diplomatico", "DIPLOMATIC"),
  J("J", "Certificado Nacimiento", "BIRTH_CERTIFICATE");

  private String codigo;
  private String nombre;
  private String nombreAlterno;

  private DocumentoIdentidadEnum(String codigo, String nombre, String nombreAlterno) {
    this.codigo = codigo;
    this.nombre = nombre;
    this.nombreAlterno = nombreAlterno;
  }

  public String getCodigo() {
    return codigo;
  }

  public String getNombre() {
    return nombre;
  }

  public String getNombreAlterno() {
    return nombreAlterno;
  }

  public static DocumentoIdentidadEnum obtenerDocPorCodigo(String codigo) {
    DocumentoIdentidadEnum[] values = DocumentoIdentidadEnum.values();
    for (int i = 0; i < values.length; i++) {
      if (values[i].getCodigo().equals(codigo)) {
        return values[i];
      }
    }
    return null;
  }

  public static DocumentoIdentidadEnum obtenerDocPorNombre(String nombre) {
    DocumentoIdentidadEnum[] values = DocumentoIdentidadEnum.values();
    for (int i = 0; i < values.length; i++) {
      if (values[i].getNombreAlterno().equals(nombre)) {
        return values[i];
      }
    }
    return null;
  }
}
