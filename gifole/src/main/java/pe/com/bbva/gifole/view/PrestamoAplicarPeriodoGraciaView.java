package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.datepicker.DatePicker;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;

@PageTitle("Aplicar Período de Gracia")
@Route(value = "prestamo-aplicar-periodo-gracia", layout = MainLayout.class)
public class PrestamoAplicarPeriodoGraciaView extends VerticalLayout {

  // Filtros
  private final DatePicker fechaDesdeField = new DatePicker();
  private final DatePicker fechaHastaField = new DatePicker();
  private final TextField codigoCentralField = new TextField();
  private final TextField numeroContratoField = new TextField();
  private final TextField estadoField = new TextField();
  private final Button buscarButton = new Button("Buscar", new Icon(VaadinIcon.SEARCH));

  public PrestamoAplicarPeriodoGraciaView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Panel de filtros
    VerticalLayout filtersPanel = createFiltersPanel();

    // Título
    H2 title = new H2("Bandeja de Solicitudes de período de gracia");
    title.addClassName("bbva-grid-title");

    mainLayout.add(filtersPanel, title);
    add(mainLayout);
  }

  private VerticalLayout createFiltersPanel() {
    VerticalLayout filtersPanel = new VerticalLayout();
    filtersPanel.addClassName("filters-panel");
    filtersPanel.setSpacing(true);
    filtersPanel.setPadding(true);

    H2 filtersTitle = new H2("Filtros de Búsqueda");
    filtersTitle.addClassName("bbva-page-title");

    // Primera fila
    HorizontalLayout firstRow = new HorizontalLayout();
    firstRow.setWidthFull();
    firstRow.setSpacing(true);

    fechaDesdeField.setLabel("Desde");
    fechaDesdeField.addClassName("bbva-input-floating");
    fechaDesdeField.setWidth("150px");

    fechaHastaField.setLabel("Hasta");
    fechaHastaField.addClassName("bbva-input-floating");
    fechaHastaField.setWidth("150px");

    codigoCentralField.setLabel("Código central");
    codigoCentralField.addClassName("bbva-input-floating");
    codigoCentralField.setPlaceholder("Ingrese código central");
    codigoCentralField.setWidth("180px");

    numeroContratoField.setLabel("Número de contrato");
    numeroContratoField.addClassName("bbva-input-floating");
    numeroContratoField.setPlaceholder("Ingrese contrato");
    numeroContratoField.setWidth("220px");

    firstRow.add(fechaDesdeField, fechaHastaField, codigoCentralField, numeroContratoField);

    // Segunda fila
    HorizontalLayout secondRow = new HorizontalLayout();
    secondRow.setWidthFull();
    secondRow.setSpacing(true);

    estadoField.setLabel("Estado");
    estadoField.addClassName("bbva-input-floating");
    estadoField.setPlaceholder("Ej: REGISTRADO");
    estadoField.setWidth("180px");

    buscarButton.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    buscarButton.addClassName("buscar-button");
    buscarButton.addClickListener(e -> buscarRegistros());

    secondRow.add(estadoField, buscarButton);
    secondRow.setAlignItems(Alignment.END);

    filtersPanel.add(filtersTitle, firstRow, secondRow);
    return filtersPanel;
  }

  private void buscarRegistros() {
    Notification.show("Búsqueda realizada").addThemeVariants(NotificationVariant.LUMO_SUCCESS);
  }
}
