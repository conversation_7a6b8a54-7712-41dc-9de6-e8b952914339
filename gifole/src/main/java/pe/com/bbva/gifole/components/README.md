# DataTable Component

Componente reutilizable para mostrar datos en formato de tabla con paginación automática.

## Características

- ✅ **Paginación automática**: Divide los datos en páginas configurables
- ✅ **Columnas dinámicas**: Define las columnas usando `GridColumn`
- ✅ **Genérico**: Funciona con cualquier tipo de objeto Java
- ✅ **Responsive**: Se adapta a diferentes tamaños de pantalla
- ✅ **Estilos BBVA**: Incluye los estilos corporativos
- ✅ **Reflection**: Obtiene automáticamente los valores de las propiedades
- ✅ **Navegación**: Controles de navegación entre páginas
- ✅ **Información de estado**: Muestra información de registros y páginas

## Uso Básico

### 1. Definir las columnas

```java
List<GridColumn> columns = Arrays.asList(
    new GridColumn("id", "ID", "80px"),
    new GridColumn("nombre", "Nombre", "200px"),
    new GridColumn("email", "Email", "250px"),
    new GridColumn("estado", "Estado", "100px")
);
```

### 2. Preparar los datos

```java
List<MiClase> datos = obtenerDatos();
```

### 3. Crear el DataTable

```java
DataTable<MiClase> dataTable = new DataTable<>(columns, datos, 10);
add(dataTable);
```

## Ejemplo Completo

```java
public class MiVista extends VerticalLayout {
    
    public MiVista() {
        // Definir columnas
        List<GridColumn> columns = Arrays.asList(
            new GridColumn("codigoCampania", "Código", "120px"),
            new GridColumn("nombreCampania", "Nombre", "200px"),
            new GridColumn("estado", "Estado", "100px")
        );
        
        // Obtener datos
        List<CampaniaMultiplicaBean> campanias = campaniaService.findAll();
        
        // Crear tabla con 5 registros por página
        DataTable<CampaniaMultiplicaBean> tabla = new DataTable<>(columns, campanias, 5);
        
        add(tabla);
    }
}
```

## Parámetros del Constructor

| Parámetro | Tipo | Descripción |
|-----------|------|-------------|
| `columns` | `List<GridColumn>` | Lista de columnas a mostrar |
| `items` | `List<T>` | Lista de datos a mostrar |
| `pageSize` | `int` | Número de registros por página |

## GridColumn

El record `GridColumn` define cada columna:

```java
public record GridColumn(
    String key,      // Nombre de la propiedad del objeto (ej: "nombre")
    String header,   // Texto del encabezado (ej: "Nombre Completo")
    String width     // Ancho de la columna (ej: "200px") - opcional
)
```

## Métodos Públicos

### Navegación
- `goToPage(int page)`: Navega a una página específica
- `getCurrentPage()`: Obtiene la página actual
- `getTotalPages()`: Obtiene el total de páginas

### Datos
- `setItems(List<T> newItems)`: Actualiza los datos de la tabla
- `getAllItems()`: Obtiene todos los items
- `getCurrentPageItems()`: Obtiene los items de la página actual
- `refresh()`: Refresca la tabla

### Configuración
- `getGrid()`: Obtiene el Grid interno para configuraciones avanzadas
- `getPageSize()`: Obtiene el tamaño de página

## Configuración Avanzada

### Acceso al Grid interno

```java
DataTable<MiClase> tabla = new DataTable<>(columns, datos, 10);

// Configurar selección
tabla.getGrid().setSelectionMode(Grid.SelectionMode.SINGLE);
tabla.getGrid().addSelectionListener(event -> {
    // Manejar selección
});

// Agregar listeners
tabla.getGrid().addItemClickListener(event -> {
    // Manejar click en item
});
```

### Actualizar datos dinámicamente

```java
// Actualizar con nuevos datos
List<MiClase> nuevosDatos = obtenerNuevosDatos();
tabla.setItems(nuevosDatos);

// Refrescar datos actuales
tabla.refresh();
```

### Navegación programática

```java
// Ir a la primera página
tabla.goToPage(0);

// Ir a la última página
tabla.goToPage(tabla.getTotalPages() - 1);

// Ir a una página específica
tabla.goToPage(2);
```

## Estilos CSS

El componente incluye estilos CSS predefinidos:

- `.bbva-datatable`: Contenedor principal
- `.bbva-grid`: Grid de datos
- `.bbva-pagination`: Controles de paginación
- `.pagination-button`: Botones de navegación
- `.pagination-info`: Información de página

### Personalizar estilos

```css
/* Personalizar el contenedor */
.mi-tabla.bbva-datatable {
    border: 2px solid #004481;
}

/* Personalizar las filas */
.mi-tabla .bbva-grid [part="row"]:hover {
    background-color: #e3f2fd;
}
```

```java
// Aplicar clase CSS personalizada
dataTable.addClassName("mi-tabla");
```

## Requisitos

- **Java 11+**
- **Vaadin 24+**
- **Spring Boot** (opcional, para inyección de dependencias)

## Convenciones de Nombres

El componente usa **reflection** para obtener los valores de las propiedades. Asegúrate de que tus clases tengan:

1. **Getters estándar**: `getNombre()`, `getId()`, etc.
2. **Nombres consistentes**: El `key` en `GridColumn` debe coincidir con el nombre de la propiedad

### Ejemplos de mapeo

| GridColumn key | Método getter esperado |
|----------------|------------------------|
| `"nombre"` | `getNombre()` |
| `"fechaCreacion"` | `getFechaCreacion()` |
| `"id"` | `getId()` |
| `"estado"` | `getEstado()` |

## Troubleshooting

### "N/A" en las celdas
- Verifica que el `key` en `GridColumn` coincida con el nombre de la propiedad
- Asegúrate de que existe el método getter correspondiente
- Revisa que el método getter sea público

### Paginación no funciona
- Verifica que `pageSize` sea mayor a 0
- Asegúrate de que la lista de datos no esté vacía

### Estilos no se aplican
- Importa el archivo CSS: `@CssImport("./themes/bbva/components/datatable.css")`
- Verifica que el archivo CSS esté en la ruta correcta

## Ejemplos Adicionales

Ver `DataTableExample.java` para ejemplos completos de uso con diferentes tipos de datos.