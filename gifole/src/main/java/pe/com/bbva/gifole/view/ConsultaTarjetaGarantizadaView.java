package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.TarjetaGarantizadaBean;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Tarjeta Garantizada")
@Route(value = "reporte/tarjetas/consulta-tarjeta-garantizada", layout = MainLayout.class)
public class ConsultaTarjetaGarantizadaView extends VerticalLayout {

  public ConsultaTarjetaGarantizadaView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Tarjeta Garantizada");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    DataTable<TarjetaGarantizadaBean> dataTable =
        DataTable.<TarjetaGarantizadaBean>builder()
            .id("tabla-tarjeta-garantizada")
            .column(
                "codigoCentral",
                "Código Central",
                bean -> StringUtils.trimToEmpty(bean.getCodigoCentral()),
                "100px")
            .column(
                "tipoDocumento",
                "Tipo Documento",
                bean -> StringUtils.trimToEmpty(bean.getTipoDocumento()),
                "120px")
            .column(
                "nroDocumento",
                "Número de Documento",
                bean -> StringUtils.trimToEmpty(bean.getNroDocumento()),
                "130px")
            .column(
                "nombreCompleto",
                "Nombre Completo",
                bean -> StringUtils.trimToEmpty(bean.getNombreCompleto()),
                "200px")
            .column("fechaRegistro", "Fecha Registro", bean -> bean.getFechaRegistro(), "150px")
            .column(
                "nroTarjeta",
                "Número de Tarjeta",
                bean -> StringUtils.trimToEmpty(bean.getNroTarjeta()),
                "140px")
            .column(
                "registroGestor",
                "Registro Gestor",
                bean -> StringUtils.trimToEmpty(bean.getRegistroGestor()),
                "150px")
            .column(
                "telefono",
                "Teléfono",
                bean -> StringUtils.trimToEmpty(bean.getTelefono()),
                "120px")
            .column("correo", "Correo", bean -> StringUtils.trimToEmpty(bean.getCorreo()), "200px")
            .column("estado", "Estado", bean -> StringUtils.trimToEmpty(bean.getEstado()), "120px")
            .column(
                "codigoOficina",
                "Código Oficina",
                bean -> StringUtils.trimToEmpty(bean.getCodigoOficina()),
                "120px")
            .column(
                "descripcionOficina",
                "Descripción Oficina",
                bean -> StringUtils.trimToEmpty(bean.getDescripcionOficina()),
                "180px")
            .column(
                "oficinaRecojo",
                "Oficina de Recojо",
                bean -> StringUtils.trimToEmpty(bean.getOficinaRecojo()),
                "180px")
            .column(
                "lineaCredito",
                "Línea de Crédito",
                bean -> StringUtils.trimToEmpty(bean.getLineaCredito()),
                "120px")
            .column(
                "tipoTarjeta",
                "Tipo de Tarjeta",
                bean -> StringUtils.trimToEmpty(bean.getTipoTarjeta()),
                "130px")
            .column("fechaAlta", "Fecha de Alta", bean -> bean.getFechaAlta(), "120px")
            .column(
                "cuentaRespaldo",
                "Cuenta Respaldo",
                bean -> StringUtils.trimToEmpty(bean.getCuentaRespaldo()),
                "120px")
            .column("canal", "Canal", bean -> StringUtils.trimToEmpty(bean.getCanal()), "100px")
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
