package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;

@PageTitle("Mantenimiento de Costos (Beneficios)")
@Route(value = "tc-beneficio-mantenimiento", layout = MainLayout.class)
public class TcBeneficioMantenimientoView extends VerticalLayout {

  // Filtros
  private final ComboBox<String> filtroEstado = new ComboBox<>("Estado");
  private final TextField filtroId = new TextField("ID");
  private final TextField filtroDescripcion = new TextField("Descripción");

  public TcBeneficioMantenimientoView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Filtros de Mantenimiento de Costos (Beneficios)");
    title.addClassName("bbva-grid-title");

    // Panel de filtros
    HorizontalLayout filtersPanel = createFiltersPanel();

    mainLayout.add(title, filtersPanel);
    add(mainLayout);
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.END);
    filtersPanel.setSpacing(false);

    filtroEstado.addClassName("bbva-input-floating");
    filtroEstado.setWidth("90px");
    filtroEstado.setItems("Todos", "ACTIVO", "INACTIVO");
    filtroEstado.setValue("Todos");

    filtroId.addClassName("bbva-input-floating");
    filtroId.setWidth("80px");

    filtroDescripcion.addClassName("bbva-input-floating");
    filtroDescripcion.setWidth("300px");

    filtersPanel.add(filtroEstado, filtroId, filtroDescripcion);
    return filtersPanel;
  }
}
