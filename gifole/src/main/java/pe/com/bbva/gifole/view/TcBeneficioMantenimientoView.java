package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import java.util.List;
import pe.com.bbva.gifole.domain.BeneficioTcCu;
import pe.com.bbva.gifole.domain.TcTarifario;

@PageTitle("Mantenimiento de Costos (Beneficios)")
@Route(value = "tc-beneficio-mantenimiento", layout = MainLayout.class)
public class TcBeneficioMantenimientoView extends VerticalLayout {

  // Constantes
  private static final String ACTIVO = "ACTIVO";
  private static final String INACTIVO = "INACTIVO";

  private final Grid<BeneficioTcCu> grid = new Grid<>(BeneficioTcCu.class, false);
  private final List<BeneficioTcCu> beneficiosList = new ArrayList<>();
  private final List<BeneficioTcCu> allBeneficios = new ArrayList<>();

  // Variables de paginación
  private int currentPage = 0;
  private final int pageSize = 10;
  private int totalPages = 0;

  // Componentes de paginación
  private Button previousButton;
  private Button nextButton;
  private Span pageInfo;

  // Filtros superiores
  private final ComboBox<String> filtroEstado = new ComboBox<>();
  private final TextField filtroId = new TextField();
  private final TextField filtroDescripcion = new TextField();

  // Campos del formulario
  private final TextField txtId = new TextField();
  private final TextField txtBin = new TextField();
  private final TextField txtDescripcion = new TextField();
  private final ComboBox<String> cmbEstado = new ComboBox<>();
  private final ComboBox<TcTarifario> cmbTarjeta = new ComboBox<>();

  // Botones
  private final Button btnCrear = new Button("Crear");
  private final Button btnNuevo = new Button("Nuevo", VaadinIcon.PLUS.create());
  private final Button btnCerrarDrawer = new Button(VaadinIcon.CLOSE.create());

  // Drawer/Panel flotante
  private VerticalLayout drawerPanel;
  private boolean drawerVisible = false;

  public TcBeneficioMantenimientoView() {
    setSizeFull();
    addClassName("tc-beneficio-mantenimiento-view");
    setPadding(true);
    setSpacing(true);
    getStyle().set("position", "relative"); // Para posicionamiento del drawer

    // Crear layout principal
    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Panel de filtros superiores
    HorizontalLayout filtersPanel = createFiltersPanel();

    // Header con título y botón nuevo
    HorizontalLayout headerLayout = createHeaderLayout();

    // Card para el Grid (ahora ocupa todo el ancho)
    VerticalLayout gridCard = createGridCard();
    gridCard.setSizeFull();

    mainLayout.add(filtersPanel, headerLayout, gridCard);
    mainLayout.setFlexGrow(1, gridCard);

    // Crear drawer flotante
    drawerPanel = createDrawerPanel();

    add(mainLayout, drawerPanel);

    // Configurar componentes
    configureComponents();

    // Cargar datos de ejemplo
    loadSampleData();
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.END);
    filtersPanel.setSpacing(false); // Controlado por CSS gap

    // Configurar filtros con anchos específicos y clase CSS
    filtroEstado.setLabel("Estado");
    filtroEstado.addClassName("bbva-input-floating");
    filtroEstado.setWidth("90px");
    filtroEstado.setItems("Todos", ACTIVO, INACTIVO);
    filtroEstado.setValue("Todos");

    filtroId.setLabel("ID");
    filtroId.addClassName("bbva-input-floating");
    filtroId.setWidth("80px");

    filtroDescripcion.setLabel("Descripción del beneficio");
    filtroDescripcion.addClassName("bbva-input-floating");
    filtroDescripcion.setWidth("300px");

    // Agregar todos los filtros directamente al panel principal
    filtersPanel.add(filtroEstado, filtroId, filtroDescripcion);

    return filtersPanel;
  }

  private HorizontalLayout createHeaderLayout() {
    HorizontalLayout headerLayout = new HorizontalLayout();
    headerLayout.setWidthFull();
    headerLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    headerLayout.setAlignItems(FlexComponent.Alignment.CENTER);
    headerLayout.setPadding(false);
    headerLayout.setSpacing(true);

    H2 pageTitle = new H2("Lista de Beneficios");
    pageTitle.addClassName("bbva-grid-title");
    pageTitle.getStyle().set("margin", "0");

    // Botón para abrir el drawer
    btnNuevo.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnNuevo.addClassName("bbva-filters-button");
    btnNuevo.addClickListener(e -> openDrawerForNew());

    headerLayout.add(pageTitle, btnNuevo);
    return headerLayout;
  }

  private VerticalLayout createDrawerPanel() {
    VerticalLayout drawer = new VerticalLayout();
    drawer.addClassName("drawer-panel");
    drawer.setWidth("450px");
    drawer.setHeight("100%");
    drawer.setPadding(true);
    drawer.setSpacing(true);

    // Estilos CSS para el drawer flotante
    drawer
        .getStyle()
        .set("position", "fixed")
        .set("top", "0")
        .set("right", "-450px") // Inicialmente oculto
        .set("background", "var(--bbva-white)")
        .set("box-shadow", "-4px 0 20px rgba(0, 0, 0, 0.15)")
        .set("z-index", "1000")
        .set("transition", "right 0.3s ease-in-out")
        .set("overflow-y", "auto");

    // Header del drawer
    HorizontalLayout drawerHeader = new HorizontalLayout();
    drawerHeader.setWidthFull();
    drawerHeader.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    drawerHeader.setAlignItems(FlexComponent.Alignment.CENTER);

    H2 drawerTitle = new H2("Formulario de Beneficio");
    drawerTitle.addClassName("bbva-page-title");
    drawerTitle.getStyle().set("margin", "0");

    btnCerrarDrawer.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
    btnCerrarDrawer.addClickListener(e -> toggleDrawer());

    drawerHeader.add(drawerTitle, btnCerrarDrawer);

    // Contenido del formulario
    VerticalLayout formContent = createFormContent();

    drawer.add(drawerHeader, formContent);
    drawer.setFlexGrow(1, formContent);

    return drawer;
  }

  private VerticalLayout createFormContent() {
    VerticalLayout formContent = new VerticalLayout();
    formContent.setPadding(false);
    formContent.setSpacing(true);

    // Configurar campos del formulario usando clases globales
    txtId.setLabel("ID:");
    txtId.addClassName("bbva-form-field");
    txtId.setMaxLength(10);

    txtBin.setLabel("BIN:");
    txtBin.addClassName("bbva-form-field");
    txtBin.setMaxLength(10);

    txtDescripcion.setLabel("Descripción del beneficio:");
    txtDescripcion.addClassName("bbva-form-field");
    txtDescripcion.setMaxLength(200);

    cmbEstado.setLabel("Estado:");
    cmbEstado.addClassName("bbva-form-field");
    cmbEstado.setItems(ACTIVO, INACTIVO);
    cmbEstado.setValue(ACTIVO);

    // ComboBox para tarjetas de crédito usando TcTarifario
    cmbTarjeta.setLabel("Tarjetas de crédito:");
    cmbTarjeta.addClassName("bbva-form-field");
    cmbTarjeta.setItemLabelGenerator(
        tarjeta -> tarjeta.getBin()
            + " - "
            + (tarjeta.getDescripcion_zona_publica() != null
                ? tarjeta.getDescripcion_zona_publica()
                : "Sin descripción"));

    // Configurar botón usando clase global
    btnCrear.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnCrear.addClassName("bbva-button");
    btnCrear.addClickListener(e -> crearBeneficio());

    // Agregar los campos al contenido del formulario
    formContent.add(txtId, txtBin, txtDescripcion, cmbEstado, cmbTarjeta, btnCrear);

    return formContent;
  }

  private void openDrawerForNew() {
    // Limpiar selección del grid y formulario para nuevo registro
    grid.deselectAll();
    limpiarFormulario();

    if (!drawerVisible) {
      openDrawer();
    } else {
      closeDrawer();
    }
  }

  private void openDrawer() {
    drawerVisible = true;
    drawerPanel.getStyle().set("right", "0");
    btnNuevo.setText("Cerrar");
    btnNuevo.setIcon(VaadinIcon.CLOSE.create());
  }

  private void closeDrawer() {
    drawerVisible = false;
    drawerPanel.getStyle().set("right", "-450px");
    btnNuevo.setText("Nuevo");
    btnNuevo.setIcon(VaadinIcon.PLUS.create());
  }

  private void toggleDrawer() {
    if (drawerVisible) {
      closeDrawer();
    } else {
      openDrawer();
    }
  }

  private VerticalLayout createGridCard() {
    VerticalLayout gridCard = new VerticalLayout();
    gridCard.setSizeFull();
    gridCard.addClassName("bbva-grid-card");
    gridCard.setSpacing(true);
    gridCard.setPadding(true);

    // ComboBox para tarjetas de crédito en lugar del título
    ComboBox<TcTarifario> cmbTarjetasGrid = new ComboBox<>();
    cmbTarjetasGrid.setLabel("Tarjetas de crédito:");
    cmbTarjetasGrid.addClassName("bbva-form-field");
    cmbTarjetasGrid.setItemLabelGenerator(
        tarjeta -> tarjeta.getBin()
            + " - "
            + (tarjeta.getDescripcion_zona_publica() != null
                ? tarjeta.getDescripcion_zona_publica()
                : "Sin descripción"));

    // Cargar datos para el combo del grid
    loadTarjetasData(cmbTarjetasGrid);

    configureGrid();

    // Crear controles de paginación
    HorizontalLayout paginationControls = createPaginationControls();

    gridCard.add(cmbTarjetasGrid, grid, paginationControls);
    gridCard.setFlexGrow(1, grid);

    return gridCard;
  }

  private void configureGrid() {
    grid.setSizeFull();
    grid.addClassName("bbva-grid");

    // Configurar columnas según la imagen y estructura de BeneficioTcCu
    grid.addColumn(beneficio -> beneficio.getEstado())
        .setHeader("Estado")
        .setSortable(true)
        .setWidth("80px");

    grid.addColumn(BeneficioTcCu::getId).setHeader("ID").setSortable(true).setWidth("80px");

    grid.addColumn(BeneficioTcCu::getDescripcion)
        .setHeader("Descripción del beneficio")
        .setSortable(true)
        .setFlexGrow(1);

    // Configurar selección
    grid.setSelectionMode(Grid.SelectionMode.SINGLE);
    grid.addSelectionListener(
        event -> {
          if (event.getFirstSelectedItem().isPresent()) {
            BeneficioTcCu selectedBeneficio = event.getFirstSelectedItem().get();
            loadBeneficioToForm(selectedBeneficio);
            if (!drawerVisible) {
              openDrawer(); // Abrir drawer automáticamente al seleccionar
            }
          }
        });
  }

  private void configureComponents() {
    // Cargar datos para el combo de tarjetas
    loadTarjetasData(cmbTarjeta);

    // Configurar listeners de filtros
    filtroEstado.addValueChangeListener(e -> applyFilters());
    filtroId.addValueChangeListener(e -> applyFilters());
    filtroDescripcion.addValueChangeListener(e -> applyFilters());
  }

  private void loadTarjetasData(ComboBox<TcTarifario> combo) {
    // Datos de ejemplo para tarjetas - en producción vendría de un servicio
    List<TcTarifario> tarjetas = new ArrayList<>();

    TcTarifario tarjeta1 = new TcTarifario();
    tarjeta1.setBin("404293");
    tarjeta1.setDescripcion_zona_publica("Visa - tarjeta visa cero bbva");

    TcTarifario tarjeta2 = new TcTarifario();
    tarjeta2.setBin("404294");
    tarjeta2.setDescripcion_zona_publica("Visa - tarjeta visa gold bbva");

    tarjetas.add(tarjeta1);
    tarjetas.add(tarjeta2);

    combo.setItems(tarjetas);
  }

  private HorizontalLayout createPaginationControls() {
    HorizontalLayout paginationLayout = new HorizontalLayout();
    paginationLayout.addClassName("bbva-pagination");
    paginationLayout.setWidthFull();
    paginationLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    paginationLayout.setAlignItems(FlexComponent.Alignment.CENTER);

    // Información de página
    pageInfo = new Span();
    pageInfo.addClassName("bbva-pagination-info");

    // Controles de navegación
    HorizontalLayout controls = new HorizontalLayout();
    controls.addClassName("bbva-pagination-controls");
    controls.setSpacing(true);

    previousButton = new Button("Anterior", VaadinIcon.ANGLE_LEFT.create());
    previousButton.addClassName("bbva-pagination-button");
    previousButton.addClickListener(e -> goToPreviousPage());

    nextButton = new Button("Siguiente", VaadinIcon.ANGLE_RIGHT.create());
    nextButton.addClassName("bbva-pagination-button");
    nextButton.addClickListener(e -> goToNextPage());

    controls.add(previousButton, nextButton);
    paginationLayout.add(pageInfo, controls);

    return paginationLayout;
  }

  private void updatePagination() {
    totalPages = (int) Math.ceil((double) beneficiosList.size() / pageSize);

    // Calcular índices
    int startIndex = currentPage * pageSize;
    int endIndex = Math.min(startIndex + pageSize, beneficiosList.size());

    // Obtener elementos de la página actual
    List<BeneficioTcCu> currentPageItems = beneficiosList.subList(startIndex, endIndex);
    grid.setItems(currentPageItems);

    // Actualizar información de página
    if (beneficiosList.isEmpty()) {
      pageInfo.setText("No hay elementos");
    } else {
      int displayStart = startIndex + 1;
      pageInfo.setText(
          String.format(
              "Mostrando %d-%d de %d elementos (Página %d de %d)",
              displayStart, endIndex, beneficiosList.size(), currentPage + 1, totalPages));
    }

    // Actualizar estado de botones
    previousButton.setEnabled(currentPage > 0);
    nextButton.setEnabled(currentPage < totalPages - 1);
  }

  private void goToPreviousPage() {
    if (currentPage > 0) {
      currentPage--;
      updatePagination();
    }
  }

  private void goToNextPage() {
    if (currentPage < totalPages - 1) {
      currentPage++;
      updatePagination();
    }
  }

  private void applyFilters() {
    beneficiosList.clear();

    String estadoFilter = filtroEstado.getValue();
    String idFilter = filtroId.getValue();
    String descripcionFilter = filtroDescripcion.getValue();

    for (BeneficioTcCu beneficio : allBeneficios) {
      boolean matches = true;

      // Filtro por estado
      if (estadoFilter != null && !estadoFilter.equals("Todos")) {
        if (!estadoFilter.equals(beneficio.getEstado())) {
          matches = false;
        }
      }

      // Filtro por ID
      if (idFilter != null && !idFilter.trim().isEmpty()) {
        if (beneficio.getId() == null
            || !beneficio.getId().toLowerCase().contains(idFilter.toLowerCase())) {
          matches = false;
        }
      }

      // Filtro por descripción
      if (descripcionFilter != null && !descripcionFilter.trim().isEmpty()) {
        if (beneficio.getDescripcion() == null
            || !beneficio
                .getDescripcion()
                .toLowerCase()
                .contains(descripcionFilter.toLowerCase())) {
          matches = false;
        }
      }

      if (matches) {
        beneficiosList.add(beneficio);
      }
    }

    currentPage = 0;
    updatePagination();
  }

  private void crearBeneficio() {
    if (validarCampos()) {
      BeneficioTcCu nuevoBeneficio = new BeneficioTcCu();

      // Mapear campos del formulario al objeto
      nuevoBeneficio.setId(txtId.getValue());
      nuevoBeneficio.setBin(txtBin.getValue());
      nuevoBeneficio.setDescripcion(txtDescripcion.getValue());
      nuevoBeneficio.setEstado(cmbEstado.getValue());

      // Agregar a la lista
      allBeneficios.add(nuevoBeneficio);
      applyFilters(); // Aplicar filtros para actualizar la vista

      // Cerrar drawer después de crear/actualizar
      if (drawerVisible) {
        closeDrawer();
      }

      limpiarFormulario();

      Notification.show("Beneficio creado exitosamente")
          .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
    }
  }

  private boolean validarCampos() {
    if (txtId.getValue() == null || txtId.getValue().trim().isEmpty()) {
      Notification.show("El ID es requerido").addThemeVariants(NotificationVariant.LUMO_ERROR);
      return false;
    }

    if (txtDescripcion.getValue() == null || txtDescripcion.getValue().trim().isEmpty()) {
      Notification.show("La descripción es requerida")
          .addThemeVariants(NotificationVariant.LUMO_ERROR);
      return false;
    }

    return true;
  }

  private void loadBeneficioToForm(BeneficioTcCu beneficio) {
    txtId.setValue(beneficio.getId() != null ? beneficio.getId() : "");
    txtBin.setValue(beneficio.getBin() != null ? beneficio.getBin() : "");
    txtDescripcion.setValue(beneficio.getDescripcion() != null ? beneficio.getDescripcion() : "");
    cmbEstado.setValue(beneficio.getEstado() != null ? beneficio.getEstado() : ACTIVO);

    btnCrear.setText("Actualizar");
  }

  private void limpiarFormulario() {
    txtId.clear();
    txtBin.clear();
    txtDescripcion.clear();
    cmbEstado.setValue(ACTIVO);
    cmbTarjeta.clear();

    btnCrear.setText("Crear");
    grid.deselectAll();
  }

  private void loadSampleData() {
    // Datos de ejemplo basados en la imagen
    allBeneficios.add(
        createBeneficioItem(
            ACTIVO, "EXON", "404293", "Con esta tarjeta te exoneramos del pago de membresía."));
    allBeneficios.add(
        createBeneficioItem(
            ACTIVO,
            "ZINT",
            "404293",
            "Compra en cuotas sin intereses en más de 5 mil establecimientos y disfruta nuestras promociones."));
    allBeneficios.add(
        createBeneficioItem(
            ACTIVO,
            "BNEF",
            "404293",
            "Accede a los cientos de descuentos en establecimientos que tu tarjeta Cero BBVA tiene para ti."));
    allBeneficios.add(
        createBeneficioItem(
            ACTIVO,
            "EFEC",
            "404293",
            "Retira efectivo hasta por el 100% de tu línea de crédito desde el primer día."));
    allBeneficios.add(
        createBeneficioItem(
            ACTIVO,
            "COMI",
            "404293",
            "No te cobramos comisión por retiros de efectivo en cajeros BBVA."));
    allBeneficios.add(
        createBeneficioItem(
            ACTIVO,
            "DCTO",
            "404293",
            "¡Ya estás a un paso de disfrutar de descuentos en farmacias, restaurantes y más!"));
    allBeneficios.add(
        createBeneficioItem(
            ACTIVO, "SCOM", "404293", "No pagarás membresía o comisiones por retiro de efectivo."));
    allBeneficios.add(
        createBeneficioItem(
            ACTIVO,
            "SINT",
            "404293",
            "Compra en cuotas sin intereses en más de 5 mil establecimientos."));

    applyFilters(); // Aplicar filtros iniciales
  }

  private BeneficioTcCu createBeneficioItem(
      String estado, String id, String bin, String descripcion) {
    BeneficioTcCu beneficio = new BeneficioTcCu();
    beneficio.setId(id);
    beneficio.setBin(bin);
    beneficio.setDescripcion(descripcion);
    beneficio.setEstado(estado);

    return beneficio;
  }
}
