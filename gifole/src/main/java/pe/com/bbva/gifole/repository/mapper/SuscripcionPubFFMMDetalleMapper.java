package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.SuscripcionPubFFMM;
import pe.com.bbva.gifole.domain.SuscripcionPubFFMMDetalle;

@Component
public class SuscripcionPubFFMMDetalleMapper implements RowMapper<SuscripcionPubFFMMDetalle> {

  @Override
  public SuscripcionPubFFMMDetalle mapRow(ResultSet rs, int i) throws SQLException {
    SuscripcionPubFFMMDetalle afiliacionDetalle = new SuscripcionPubFFMMDetalle();
    afiliacionDetalle.setMontoCuota(rs.getBigDecimal("MONTO_CUOTA"));
    afiliacionDetalle.setPeriodo(rs.getString("PERIODO"));
    afiliacionDetalle.setSuscripcionPubFFMM(new SuscripcionPubFFMM());
    afiliacionDetalle.getSuscripcionPubFFMM().setId(rs.getLong("ID"));
    afiliacionDetalle.getSuscripcionPubFFMM().setNombre(rs.getString("NOMBRE"));
    afiliacionDetalle.getSuscripcionPubFFMM().setApellido(rs.getString("APELLIDO"));
    afiliacionDetalle.getSuscripcionPubFFMM().setFechaRegistro(rs.getTimestamp("FECHA_REGISTRO"));
    afiliacionDetalle.getSuscripcionPubFFMM().setTipoDocumento(rs.getString("TIPO_DOCUMENTO"));
    afiliacionDetalle.getSuscripcionPubFFMM().setDocumento(rs.getString("DOCUMENTO"));
    afiliacionDetalle.getSuscripcionPubFFMM().setDepartamento(rs.getString("DEPARTAMENTO"));
    afiliacionDetalle.getSuscripcionPubFFMM().setCorreo(rs.getString("CORREO"));
    afiliacionDetalle.getSuscripcionPubFFMM().setTelefono(rs.getString("TELEFONO"));
    afiliacionDetalle.getSuscripcionPubFFMM().setHorario(rs.getString("HORARIO"));
    afiliacionDetalle.getSuscripcionPubFFMM().setSuscripcion(rs.getString("SUSCRIPCION"));
    afiliacionDetalle.getSuscripcionPubFFMM().setAfiliacionEECC(rs.getString("AFILIACION_EECC"));
    afiliacionDetalle.getSuscripcionPubFFMM().setAutorizacion(rs.getString("AUTORIZACION"));
    afiliacionDetalle.getSuscripcionPubFFMM().setTipoCalculo(rs.getString("TIPO_CALCULO"));
    afiliacionDetalle.getSuscripcionPubFFMM().setPlazo(rs.getInt("PLAZO"));
    afiliacionDetalle.getSuscripcionPubFFMM().setObjetivo(rs.getString("OBJETIVO"));
    afiliacionDetalle.getSuscripcionPubFFMM().setMonto(rs.getBigDecimal("MONTO"));
    afiliacionDetalle.getSuscripcionPubFFMM().setTipoCliente(rs.getString("TIPO_CLIENTE"));
    return afiliacionDetalle;
  }
}
