package pe.com.bbva.gifole.repository.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.util.Date;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import pe.com.bbva.gifole.domain.AfiliacionEECCFFMMDetalle;
import pe.com.bbva.gifole.repository.AfiliacionEECCFFMMDetalleRepository;
import pe.com.bbva.gifole.repository.mapper.AfiliacionEECCFFMMDetalleMapper;

@Repository
public class AfiliacionEECCFFMMDetalleRepositoryImpl
    implements AfiliacionEECCFFMMDetalleRepository {

  private static final Logger logger =
      LogManager.getLogger(AfiliacionEECCFFMMDetalleRepositoryImpl.class);

  @PersistenceContext private EntityManager entityManager;

  @Autowired private JdbcTemplate jdbcTemplate;

  @Autowired private AfiliacionEECCFFMMDetalleMapper afiliacionEECCFFMMDetalleMapper;

  @Override
  public void crear(AfiliacionEECCFFMMDetalle afiliacionEECCFFMMDetalle) {
    entityManager.persist(afiliacionEECCFFMMDetalle);
  }

  @Override
  public List<AfiliacionEECCFFMMDetalle> buscarAfiliacionEECCFFMMNoProcesado() {
    StringBuilder sql = new StringBuilder();
    sql.append("select a.ID, a.NOMBRE, a.CORREO1, ");
    sql.append("a.FECHA_REGISTRO, a.CODIGO_CENTRAL, ad.NOMBRE_FONDO, ad.NUMERO_CONTRATO ");
    sql.append("from GIFOLE.AFILIACION_EECC_FFMM_DETALLE ad ");
    sql.append("inner join GIFOLE.AFILIACION_EECC_FFMM a on ad.AFILIACION_EECC_FFMM=a.ID ");
    sql.append("where a.PROCESADO='0' order by a.ID, a.FECHA_REGISTRO ");

    return jdbcTemplate.query(sql.toString(), afiliacionEECCFFMMDetalleMapper);
  }

  @Override
  public List<AfiliacionEECCFFMMDetalle> buscarAfiliacionEECCFFMMNoPorFecha(
      Date desde, Date hasta) {
    StringBuilder sql = new StringBuilder();
    sql.append("select a.ID, a.NOMBRE, a.CORREO1, ");
    sql.append("a.FECHA_REGISTRO, a.CODIGO_CENTRAL, ad.NOMBRE_FONDO, ad.NUMERO_CONTRATO ");
    sql.append("from GIFOLE.AFILIACION_EECC_FFMM_DETALLE ad ");
    sql.append("inner join GIFOLE.AFILIACION_EECC_FFMM a on ad.AFILIACION_EECC_FFMM=a.ID ");
    sql.append(
        "where TRUNC(FECHA_REGISTRO) between TRUNC(?) and TRUNC(?) order by a.ID, a.FECHA_REGISTRO ");

    return jdbcTemplate.query(sql.toString(), afiliacionEECCFFMMDetalleMapper, desde, hasta);
  }
}
