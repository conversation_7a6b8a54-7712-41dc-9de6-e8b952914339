package pe.com.bbva.gifole.repository;

import jakarta.persistence.criteria.CriteriaQuery;
import java.util.List;
import pe.com.bbva.gifole.domain.Usuario;

public interface UsuarioRepository {
  Usuario obtenerUsuario(CriteriaQuery criteriaQuery);

  void actualizar(Usuario usuario);

  List<Usuario> buscarUsuario(CriteriaQuery criteriaQuery);

  void eliminarUsuario(Long id);

  String crearUsuario(Usuario usuario);

  void actualizarUsuario(Usuario usuario);
}
