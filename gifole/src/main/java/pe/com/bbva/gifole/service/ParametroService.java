package pe.com.bbva.gifole.service;

import java.util.List;
import pe.com.bbva.gifole.domain.Parametro;

public interface ParametroService {

  public void guardarParametro(Parametro parametro, String indicadorFlagAnterior);

  public void eliminarParametro(Long id, String codigo, String tipo);

  public List<Parametro> buscarParametro(Parametro parametro);

  Parametro obtenerParametroPorCodigo(String codigo, String tipo);

  public void procesarRelacionArchivoAdjunto(Parametro parametro, String indicadorFlagAnterior);
  /*
   * public List<Parametro> buscarParametroPorTipo(String tipo); public Parametro obtenerParametroPorCodigo(String
   * codigo, String tipo); public Parametro obtenerParametroPorCodigoData(String codigo, List<Parametro>
   * dataParametro); public Parametro obtenerParametroPorDescripcionData(String descripcion, List<Parametro>
   * dataParametro); public String obtenerCodigoEstadoValoresFijos(String cad); public void
   * procesarRelacionArchivoAdjunto(Parametro parametro, String indicadorFlagAnterior); public void
   * eliminarParametroArchivoAdjunto(String codigo, String valor); public List<Parametro>
   * buscarParametroPorTipoSubDetalle(String tipo, String flagAdjuntarArchivo);
   */

}
