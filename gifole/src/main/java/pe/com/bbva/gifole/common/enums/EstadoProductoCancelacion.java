package pe.com.bbva.gifole.common.enums;

public enum EstadoProductoCancelacion {
  REGISTRADO("001", "REGIS<PERSON>AD<PERSON>"),
  PROCESADO("002", "PROCESADO"),
  REC<PERSON><PERSON>AD<PERSON>("003", "REC<PERSON><PERSON><PERSON><PERSON>"),
  CANCELADO_AUTOMATICO("004", "CANCELADO-AUTOMATICO");

  private final String codigo;
  private final String nombre;

  private EstadoProductoCancelacion(String codigo, String nombre) {
    this.codigo = codigo;
    this.nombre = nombre;
  }

  public String getCodigo() {
    return codigo;
  }

  public String getNombre() {
    return nombre;
  }
}
