package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class CotizacionSeguroVehicularBean implements Serializable {

  private static final long serialVersionUID = -5464399560758243450L;

  private Long id;
  private Date fechaRegistro;
  private String telefono;
  private String horarioContacto;
  private String nroCotizacion;
  private String planElegido;
  private String divisaPlanElegido;
  private String montoPlanElegido;
  private String canal;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Date getFechaRegistro() {
    return fechaRegistro;
  }

  public void setFechaRegistro(Date fechaRegistro) {
    this.fechaRegistro = fechaRegistro;
  }

  public String getTelefono() {
    return telefono;
  }

  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  public String getHorarioContacto() {
    return horarioContacto;
  }

  public void setHorarioContacto(String horarioContacto) {
    this.horarioContacto = horarioContacto;
  }

  public String getNroCotizacion() {
    return nroCotizacion;
  }

  public void setNroCotizacion(String nroCotizacion) {
    this.nroCotizacion = nroCotizacion;
  }

  public String getPlanElegido() {
    return planElegido;
  }

  public void setPlanElegido(String planElegido) {
    this.planElegido = planElegido;
  }

  public String getDivisaPlanElegido() {
    return divisaPlanElegido;
  }

  public void setDivisaPlanElegido(String divisaPlanElegido) {
    this.divisaPlanElegido = divisaPlanElegido;
  }

  public String getMontoPlanElegido() {
    return montoPlanElegido;
  }

  public void setMontoPlanElegido(String montoPlanElegido) {
    this.montoPlanElegido = montoPlanElegido;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }
}
