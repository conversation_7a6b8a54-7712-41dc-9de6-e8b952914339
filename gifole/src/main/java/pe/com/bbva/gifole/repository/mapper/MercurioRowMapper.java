package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.Mercurio;

@SuppressWarnings("rawtypes")
@Component
public class MercurioRowMapper implements RowMapper {

  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    Mercurio mercurio = new Mercurio();
    mercurio.setId(rs.getLong("ID"));
    mercurio.setCodigoCentral(rs.getString("CODIGO_CENTRAL"));
    mercurio.setTipoDocumento(rs.getString("TIPO_DOCUMENTO"));
    mercurio.setNumeroDocumento(rs.getString("NUMERO_DOCUMENTO"));
    mercurio.setNombreCompleto(rs.getString("NOMBRE_COMPLETO"));
    mercurio.setTelefono(rs.getString("TELEFONO"));
    mercurio.setCorreo(rs.getString("CORREO"));
    mercurio.setFechaRegistro(rs.getString("FECHA_REGISTRO"));
    mercurio.setCanal(rs.getString("CANAL"));
    mercurio.setPasoAbandono(rs.getString("PASO_ABANDONO"));
    mercurio.setProducto(rs.getString("PRODUCTO"));

    return mercurio;
  }
}
