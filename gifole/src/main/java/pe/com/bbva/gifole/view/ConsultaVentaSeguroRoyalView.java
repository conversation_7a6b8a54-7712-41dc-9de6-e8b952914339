package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.VentaSeguroRoyalBean;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Venta Seguro Royal")
@Route(value = "reporte/seguros/consulta-venta-seguro-royal", layout = MainLayout.class)
public class ConsultaVentaSeguroRoyalView extends VerticalLayout {

  public ConsultaVentaSeguroRoyalView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Venta Seguro Royal");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    DataTable<VentaSeguroRoyalBean> dataTable =
        DataTable.<VentaSeguroRoyalBean>builder()
            .id("tabla-venta-seguro-royal")
            .column(
                "fechaRegistroCotizacion",
                "Fecha Registro Cotización",
                bean -> bean.getFechaRegistroCotizacion(),
                "160px")
            .column(
                "fechaRegistroVenta",
                "Fecha Registro Venta",
                bean -> bean.getFechaRegistroVenta(),
                "160px")
            .column("nombre", "Nombre", bean -> StringUtils.trimToEmpty(bean.getNombre()), "150px")
            .column(
                "apellido",
                "Apellido",
                bean -> StringUtils.trimToEmpty(bean.getApellido()),
                "150px")
            .column(
                "documento",
                "Documento",
                bean -> StringUtils.trimToEmpty(bean.getDocumento()),
                "120px")
            .column("correo", "Correo", bean -> StringUtils.trimToEmpty(bean.getCorreo()), "200px")
            .column(
                "telefono",
                "Teléfono",
                bean -> StringUtils.trimToEmpty(bean.getTelefono()),
                "120px")
            .column(
                "productoId",
                "ID Producto",
                bean -> StringUtils.trimToEmpty(bean.getProductoId()),
                "100px")
            .column(
                "producto",
                "Producto",
                bean -> StringUtils.trimToEmpty(bean.getProductoName()),
                "180px")
            .column(
                "planCuotasMoneda",
                "Plan Cuotas Moneda",
                bean -> StringUtils.trimToEmpty(bean.getPlanCuotasMoneda()),
                "140px")
            .column(
                "planCuotasPeriodo",
                "Plan Cuotas Período",
                bean -> StringUtils.trimToEmpty(bean.getPlanCuotasPeriodo()),
                "140px")
            .column(
                "planCuotasMonto",
                "Plan Cuotas Monto",
                bean -> StringUtils.trimToEmpty(bean.getPlanCuotasMonto()),
                "130px")
            .column(
                "planNumeroCuotasTotal",
                "Número Total de Cuotas",
                bean -> StringUtils.trimToEmpty(bean.getPlanNumeroCuotasTotal()),
                "140px")
            .column(
                "cotizacionId",
                "ID Cotización",
                bean -> StringUtils.trimToEmpty(bean.getCotizacionId()),
                "130px")
            .column(
                "primaMonto",
                "Prima Monto",
                bean -> StringUtils.trimToEmpty(bean.getPrimaMonto()),
                "120px")
            .column(
                "primaMontoMoneda",
                "Prima Moneda",
                bean -> StringUtils.trimToEmpty(bean.getPrimaMontoMoneda()),
                "120px")
            .column(
                "sumaAseguradaMoneda",
                "Suma Asegurada Moneda",
                bean -> StringUtils.trimToEmpty(bean.getSumaAseguradaMoneda()),
                "140px")
            .column(
                "sumaAsegurada",
                "Suma Asegurada",
                bean -> StringUtils.trimToEmpty(bean.getSumaAsegurada()),
                "130px")
            .column(
                "contrato",
                "Contrato",
                bean -> StringUtils.trimToEmpty(bean.getContratoId()),
                "120px")
            .column(
                "poliza", "Póliza", bean -> StringUtils.trimToEmpty(bean.getPolizaId()), "120px")
            .column("fechaVigIni", "Fecha Vigencia Inicio", bean -> bean.getFechaVigIni(), "140px")
            .column("fechaVigFin", "Fecha Vigencia Fin", bean -> bean.getFechaVigFin(), "140px")
            .column(
                "metodoPago",
                "Método de Pago",
                bean -> StringUtils.trimToEmpty(bean.getMetodoPago()),
                "140px")
            .column(
                "numeroTarjetaCuenta",
                "Número de Tarjeta/Cuenta",
                bean -> StringUtils.trimToEmpty(bean.getNroCuentaOTarjeta()),
                "150px")
            .column("canal", "Canal", bean -> StringUtils.trimToEmpty(bean.getCanal()), "120px")
            .column(
                "subcanal",
                "Subcanal",
                bean -> StringUtils.trimToEmpty(bean.getSubChannel()),
                "120px")
            .column(
                "informacionBanco",
                "Información Banco",
                bean -> StringUtils.trimToEmpty(bean.getInformacionBanco()),
                "200px")
            .column(
                "codigoOficina",
                "Código Oficina",
                bean -> StringUtils.trimToEmpty(bean.getCodigoOficina()),
                "120px")
            .column(
                "cupon", "Cupón", bean -> StringUtils.trimToEmpty(bean.getCouponCode()), "120px")
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
