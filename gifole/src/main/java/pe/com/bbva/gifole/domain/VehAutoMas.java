package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_AUTOMAS")
public class VehAutoMas implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_VEH_AUTOMAS")
  @TableGenerator(
      name = "SEQ_VEH_AUTOMAS",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.VehAutoMas",
      allocationSize = 1)
  private Long id;

  @Column(name = "MARCA", length = 4)
  private String marca;

  @Column(name = "MODELO", length = 8)
  private String modelo;

  @Column(name = "ANHO_FABRICACION", length = 4)
  private String anhoFabricacion;

  @Column(name = "DIVISA", length = 3)
  private String divisa;

  @Column(name = "VALOR_COMERCIAL", precision = 13, scale = 2)
  private BigDecimal valorComercial;

  @Transient private BigDecimal valorComercialMin;

  @Transient private BigDecimal valorComercialMax;
}
