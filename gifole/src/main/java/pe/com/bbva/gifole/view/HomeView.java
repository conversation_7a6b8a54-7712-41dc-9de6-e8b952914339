package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.html.Paragraph;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import pe.com.bbva.gifole.view.theme.BBVATheme;

/** Vista principal de la aplicacion */
@CssImport("./themes/bbva/views/home-view.css")
@Route(value = "", layout = MainLayout.class)
@PageTitle("Inicio | BBVA Gifole")
public class HomeView extends BaseView {

  public HomeView() {
    // Aplicar clase CSS especifica para esta vista
    addClassName("app-main");
  }

  @Override
  protected void createContent() {
    // Header principal
    add(createBBVAHeader("Bienvenido a BBVA Gifole"));

    // Contenido de bienvenida
    VerticalLayout content = new VerticalLayout();
    content.addClassName(BBVATheme.BBVA_CARD);
    content.addClassName("home-card");
    content.addClassName("home-welcome-section");

    H2 subtitle = new H2("Sistema de Gestion");
    subtitle.addClassName(BBVATheme.BBVA_TEXT_SECONDARY);
    subtitle.addClassName("home-subtitle");

    Paragraph description =
        new Paragraph(
            "Esta es la pagina principal del sistema BBVA Gifole. "
                + "Utiliza la navegacion superior para acceder a las diferentes funcionalidades.");
    description.addClassName(BBVATheme.BBVA_TEXT_NAVY);
    description.addClassName("home-description");

    content.add(subtitle, description);
    add(content);
  }
}
