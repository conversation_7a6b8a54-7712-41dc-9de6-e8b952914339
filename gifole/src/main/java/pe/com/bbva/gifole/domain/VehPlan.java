package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_PLAN")
public class VehPlan implements Serializable {
  @Id
  @Column(name = "CODIGO", length = 4)
  private String codigo;

  @Column(name = "DESCRIPCION", length = 40)
  private String descripcion;

  @Column(name = "ESTILO", length = 30)
  private String estilo;

  @Column(name = "FACTOR_BANCARIO", length = 1)
  private String factorBancario;

  @Column(name = "MONTO_MINIMO", length = 15, precision = 2)
  private BigDecimal montoMinimo;

  @Column(name = "ESTADO", length = 1)
  private String estado;

  @Column(name = "ORDEN")
  private int orden;
}
