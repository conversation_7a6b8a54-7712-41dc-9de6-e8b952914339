package pe.com.bbva.gifole.common.enums;

public enum TipoProductoCancelacion {
  CUENTA("1", "CUENTA"),
  PRESTAMO("2", "PRESTAMO"),
  TARJETA("3", "TARJETA"),
  SEGURO("4", "SEG<PERSON><PERSON>"),
  DEPOSITO("5", "DEPOSITO"),
  PRESTAMO_NATURAL("6", "NATURAL"),
  PRESTAMO_JURIDICO("7", "JURIDIC<PERSON>"),
  LINEA_CREDITO("8", "LINEA CREDITO");

  private final String codigo;
  private final String nombre;

  private TipoProductoCancelacion(String codigo, String nombre) {
    this.codigo = codigo;
    this.nombre = nombre;
  }

  public String getCodigo() {
    return codigo;
  }

  public String getNombre() {
    return nombre;
  }
}
