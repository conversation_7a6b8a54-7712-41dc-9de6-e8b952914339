package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.CambioModalidadEECCDetalleBean; // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Cambio Modalidad EECC")
@Route(value = "reporte/cambio-modalidad-eecc/consulta", layout = MainLayout.class)
public class ConsultaCambioModalidadEECCView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<CambioModalidadEECCDetalleBean> allData = new ArrayList<>();
    private DataTable<CambioModalidadEECCDetalleBean> dataTable;

    public ConsultaCambioModalidadEECCView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Cambio Modalidad EECC");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<CambioModalidadEECCDetalleBean>builder()
                .id("tabla-cambio-modalidad-eecc") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaCambioModadalidadEECCUI)
                .column("codigoCentral", "Código Central",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getCodigoCentral()), "100px")
                .column("nombresApellidos", "Nombre Cliente",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getNombreCompletoCliente()), "280px")
                .column("tipoDocumento", "Tipo Documento",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getTipoDocumento()), "60px")
                .column("numeroDocumento", "Número de Documento",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getNroDocumento()), "80px")
                .column("fechaHoraRegistro", "Fecha Registro",
                    bean -> bean.getCreacion() != null ? FORMATO_FECHA.format(bean.getCreacion()) : "", "170px")
                .column("subProducto", "Sub Producto",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getSubProducto()), "150px")
                .column("nroContrato", "Número de contrato",
                    bean -> StringUtils.trimToEmpty(obtenerNumeroCuentaFormateado(bean.getCambioModalidadEECCBean().getNroContrato())), "170px")
                .column("nroTarjetaTitular", "Número tarjeta titular",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getNroTarjetaTitular()), "170px")
                .column("correoContacto", "Correo de Contacto",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getCorreoClienteContacto()), "200px")
                .column("tipoModalidadEnvio", "Nueva Modalidad de envío",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getTipoModalidadEnvio()), "150px")
                .column("direccionEntrega", "Dirección de entrega",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getDireccionEntrega()), "250px")
                .column("correoEntrega", "Correo de entrega",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getCorreoClienteEntrega()), "200px")
                .column("estado", "Estado Solicitud",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getEstado()), "110px")
                .column("motivoAprobacion", "Detalle de procesado",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getMotivoProcesado()), "300px")
                .column("otroMotivoRechazo", "Motivo Rechazado",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getOtroMotivoRechazo()), "150px")
                .column("fechaHoraModificacion", "Fecha Modificación",
                    bean -> bean.getEdicion() != null ? FORMATO_FECHA.format(bean.getEdicion()) : "", "170px")
                .column("registroModificacion", "Registro Externo",
                    bean -> StringUtils.trimToEmpty(bean.getEditor()), "100px")
                .column("canal", "Canal",
                    bean -> StringUtils.trimToEmpty(bean.getCambioModalidadEECCBean().getCanal()), "70px")
                // Datos (inicialmente vacío)
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

    // Método auxiliar para formatear el número de cuenta (ajusta según tu implementación real)
    private String obtenerNumeroCuentaFormateado(String nroContrato) {
        // Aquí iría la lógica real de GifoleUtil.obtenerNumeroCuentaFormateado
        // Por ahora, devolvemos el valor original o vacío si es null
        return StringUtils.defaultString(nroContrato);
    }

}
