package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.datepicker.DatePicker;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import pe.com.bbva.gifole.common.bean.AperturaCuentaBean;

@PageTitle("Apertura de Cuenta")
@Route(value = "apertura-cuenta", layout = MainLayout.class)
public class AperturaCuentaView extends VerticalLayout {

  // Constantes
  private static final String REGISTRADO = "REGISTRADO";
  private static final String DEPOSITO_PENDIENTE = "DEPÓSITO PENDIENTE";
  private static final String SOLES = "SOLES";
  private static final String BANCA_POR_INTERNET = "BANCA POR INTERNET";

  // Componentes de filtros
  private final TextField codigoCentralField = new TextField();
  private final DatePicker fechaDesdeField = new DatePicker();
  private final DatePicker fechaHastaField = new DatePicker();
  private final TextField numeroCuentaField = new TextField();
  private final TextField divisaField = new TextField();
  private final TextField estadoPremioField = new TextField();
  private final TextField canalField = new TextField();
  private final Button buscarButton = new Button("Buscar", new Icon(VaadinIcon.SEARCH));

  private final Grid<AperturaCuentaBean> grid = new Grid<>(AperturaCuentaBean.class, false);
  private final List<AperturaCuentaBean> aperturasList = new ArrayList<>();

  public AperturaCuentaView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    // Layout principal
    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Panel de filtros
    VerticalLayout filtersPanel = createFiltersPanel();

    // Card para el Grid
    VerticalLayout gridCard = createGridCard();

    mainLayout.add(filtersPanel, gridCard);
    mainLayout.setFlexGrow(0, filtersPanel);
    mainLayout.setFlexGrow(1, gridCard);

    add(mainLayout);

    // Cargar datos de ejemplo
    loadSampleData();
  }

  private VerticalLayout createFiltersPanel() {
    VerticalLayout filtersPanel = new VerticalLayout();
    filtersPanel.addClassName("filters-panel");
    filtersPanel.setSpacing(true);
    filtersPanel.setPadding(true);

    H2 filtersTitle = new H2("Filtros de Búsqueda");
    filtersTitle.addClassName("bbva-page-title");

    // Primera fila de filtros
    HorizontalLayout firstRow = new HorizontalLayout();
    firstRow.setWidthFull();
    firstRow.setSpacing(true);

    codigoCentralField.setLabel("Código central");
    codigoCentralField.addClassName("bbva-input-floating");
    codigoCentralField.setPlaceholder("Ingrese código central");
    codigoCentralField.setWidth("200px");

    fechaDesdeField.setLabel("Desde");
    fechaDesdeField.addClassName("bbva-input-floating");
    fechaDesdeField.setWidth("150px");

    fechaHastaField.setLabel("Hasta");
    fechaHastaField.addClassName("bbva-input-floating");
    fechaHastaField.setWidth("150px");

    numeroCuentaField.setLabel("Número de cuenta");
    numeroCuentaField.addClassName("bbva-input-floating");
    numeroCuentaField.setPlaceholder("Ingrese número de cuenta");
    numeroCuentaField.setWidth("200px");

    firstRow.add(codigoCentralField, fechaDesdeField, fechaHastaField, numeroCuentaField);

    // Segunda fila de filtros
    HorizontalLayout secondRow = new HorizontalLayout();
    secondRow.setWidthFull();
    secondRow.setSpacing(true);

    divisaField.setLabel("Divisa");
    divisaField.addClassName("bbva-input-floating");
    divisaField.setPlaceholder("Ej: SOLES");
    divisaField.setWidth("120px");

    estadoPremioField.setLabel("Estado premio");
    estadoPremioField.addClassName("bbva-input-floating");
    estadoPremioField.setPlaceholder("Estado del premio");
    estadoPremioField.setWidth("150px");

    canalField.setLabel("Canal");
    canalField.addClassName("bbva-input-floating");
    canalField.setPlaceholder("Canal de registro");
    canalField.setWidth("180px");

    buscarButton.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    buscarButton.addClassName("buscar-button");
    buscarButton.addClickListener(e -> buscarRegistros());

    secondRow.add(divisaField, estadoPremioField, canalField, buscarButton);
    secondRow.setAlignItems(Alignment.END);

    filtersPanel.add(filtersTitle, firstRow, secondRow);
    return filtersPanel;
  }

  private VerticalLayout createGridCard() {
    VerticalLayout gridCard = new VerticalLayout();
    gridCard.setSizeFull();
    gridCard.addClassName("bbva-grid-card");

    H2 gridTitle = new H2("Bandeja Apertura de Cuenta");
    gridTitle.addClassName("bbva-grid-title");

    configureGrid();
    gridCard.add(gridTitle, grid);

    return gridCard;
  }

  private void configureGrid() {
    grid.setSizeFull();
    grid.addClassName("apertura-cuenta-grid");

    // Configurar columnas según la imagen
    grid.addColumn(apertura -> apertura.getCodigoCentral())
        .setHeader("Código central")
        .setSortable(true);

    grid.addColumn(apertura -> formatDate(apertura.getFechaRegistro()))
        .setHeader("Fecha registro")
        .setSortable(true);

    grid.addColumn(AperturaCuentaBean::getNroCuenta)
        .setHeader("Número de cuenta")
        .setSortable(true);

    grid.addColumn(AperturaCuentaBean::getTipoMoneda).setHeader("Divisa").setSortable(true);

    grid.addColumn(AperturaCuentaBean::getEstadoPremio)
        .setHeader("Estado premio")
        .setSortable(true);

    grid.addColumn(AperturaCuentaBean::getCanal).setHeader("Canal").setSortable(true);

    // Columna de acciones con botón de detalle
    grid.addComponentColumn(this::createDetailButton).setHeader("Detalle");

    // Configurar selección
    grid.setSelectionMode(Grid.SelectionMode.SINGLE);
  }

  private Button createDetailButton(AperturaCuentaBean apertura) {
    Button detailButton = new Button(new Icon(VaadinIcon.EYE));
    detailButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY, ButtonVariant.LUMO_SMALL);
    detailButton.addClassName("detail-button");
    detailButton.getElement().setAttribute("title", "Ver detalle");

    detailButton.addClickListener(e -> mostrarDetalle(apertura));

    return detailButton;
  }

  private void mostrarDetalle(AperturaCuentaBean apertura) {
    // Simular mostrar detalle
    Notification.show("Mostrando detalle para cuenta: " + apertura.getNroCuenta())
        .addThemeVariants(NotificationVariant.LUMO_PRIMARY);
  }

  private void buscarRegistros() {
    // Simular búsqueda con filtros
    String mensaje = "Búsqueda realizada";
    if (!codigoCentralField.isEmpty()) {
      mensaje += " - Código: " + codigoCentralField.getValue();
    }

    Notification.show(mensaje).addThemeVariants(NotificationVariant.LUMO_SUCCESS);
  }

  private String formatDate(Date date) {
    if (date == null) return "";
    SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
    return sdf.format(date);
  }

  private void loadSampleData() {
    // Datos de ejemplo basados en la imagen proporcionada

    aperturasList.add(
        createAperturaItem(
            "77813310",
            createDate(2021, 6, 15, 12, 52, 42),
            "0011-0814-0200089300",
            SOLES,
            REGISTRADO,
            BANCA_POR_INTERNET));

    aperturasList.add(
        createAperturaItem(
            "77813310",
            createDate(2021, 6, 15, 12, 52, 42),
            "0011-0814-0200089300",
            SOLES,
            DEPOSITO_PENDIENTE,
            BANCA_POR_INTERNET));

    aperturasList.add(
        createAperturaItem(
            "77813310",
            createDate(2021, 6, 15, 12, 52, 42),
            "0011-0814-0200089300",
            SOLES,
            REGISTRADO,
            BANCA_POR_INTERNET));

    aperturasList.add(
        createAperturaItem(
            "77813310",
            createDate(2021, 6, 15, 12, 52, 42),
            "0011-0814-0200089300",
            SOLES,
            DEPOSITO_PENDIENTE,
            BANCA_POR_INTERNET));

    // Agregar más registros para simular la tabla completa
    for (int i = 0; i < 8; i++) {
      aperturasList.add(
          createAperturaItem(
              "77813310",
              createDate(2021, 6, 15, 12, 52, 43 + i),
              "0011-0814-0200089300",
              SOLES,
              REGISTRADO,
              BANCA_POR_INTERNET));
    }

    // Configurar grid con todos los datos
    grid.setItems(aperturasList);
  }

  private AperturaCuentaBean createAperturaItem(
      String codigoCentral,
      Date fechaRegistro,
      String nroCuenta,
      String tipoMoneda,
      String estadoPremio,
      String canal) {
    AperturaCuentaBean item = new AperturaCuentaBean();
    item.setCodigoCentral(codigoCentral);
    item.setFechaRegistro(fechaRegistro);
    item.setNroCuenta(nroCuenta);
    item.setTipoMoneda(tipoMoneda);
    item.setEstadoPremio(estadoPremio);
    item.setCanal(canal);
    return item;
  }

  @SuppressWarnings("deprecation")
  private Date createDate(int year, int month, int day, int hour, int minute, int second) {
    return new Date(year - 1900, month - 1, day, hour, minute, second);
  }
}
