package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.datepicker.DatePicker;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;

@PageTitle("Apertura de Cuenta")
@Route(value = "apertura-cuenta", layout = MainLayout.class)
public class AperturaCuentaView extends VerticalLayout {

  // Filtros
  private final TextField codigoCentralField = new TextField();
  private final DatePicker fechaDesdeField = new DatePicker();
  private final DatePicker fechaHastaField = new DatePicker();
  private final TextField numeroCuentaField = new TextField();
  private final TextField divisaField = new TextField();
  private final TextField estadoPremioField = new TextField();
  private final TextField canalField = new TextField();
  private final Button buscarButton = new Button("Buscar", new Icon(VaadinIcon.SEARCH));

  // Vista simplificada sin tabla

  public AperturaCuentaView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Bandeja Apertura de Cuenta");
    title.addClassName("bbva-grid-title");

    // Panel de filtros
    VerticalLayout filtersPanel = createFiltersPanel();

    // Contenido simplificado (sin tabla)
    VerticalLayout contentCard = createContentCard();

    mainLayout.add(title, filtersPanel, contentCard);
    add(mainLayout);
  }

  /* ------------------------------------------------------------------ */
  /* 1) PANEL DE FILTROS */
  /* ------------------------------------------------------------------ */
  private VerticalLayout createFiltersPanel() {
    VerticalLayout panel = new VerticalLayout();
    panel.addClassName("bbva-filters-card");
    panel.setWidthFull();
    panel.setSpacing(true);
    panel.setPadding(true);

    // Fila 1
    HorizontalLayout row1 = new HorizontalLayout();
    row1.setWidthFull();
    row1.setSpacing(true);
    row1.setAlignItems(Alignment.END);

    codigoCentralField.setLabel("Código central");
    codigoCentralField.addClassName("bbva-input-floating");
    codigoCentralField.setPlaceholder("Ingrese código central");
    codigoCentralField.setWidth("200px");

    fechaDesdeField.setLabel("Desde");
    fechaDesdeField.addClassName("bbva-input-floating");
    fechaDesdeField.setWidth("150px");

    fechaHastaField.setLabel("Hasta");
    fechaHastaField.addClassName("bbva-input-floating");
    fechaHastaField.setWidth("150px");

    numeroCuentaField.setLabel("Número de cuenta");
    numeroCuentaField.addClassName("bbva-input-floating");
    numeroCuentaField.setPlaceholder("Ingrese número de cuenta");
    numeroCuentaField.setWidth("200px");

    row1.add(codigoCentralField, fechaDesdeField, fechaHastaField, numeroCuentaField);

    // Fila 2
    HorizontalLayout row2 = new HorizontalLayout();
    row2.setWidthFull();
    row2.setSpacing(true);
    row2.setAlignItems(Alignment.END);

    divisaField.setLabel("Divisa");
    divisaField.addClassName("bbva-input-floating");
    divisaField.setPlaceholder("Ej: SOLES");
    divisaField.setWidth("120px");

    estadoPremioField.setLabel("Estado premio");
    estadoPremioField.addClassName("bbva-input-floating");
    estadoPremioField.setPlaceholder("Estado del premio");
    estadoPremioField.setWidth("150px");

    canalField.setLabel("Canal");
    canalField.addClassName("bbva-input-floating");
    canalField.setPlaceholder("Canal de registro");
    canalField.setWidth("180px");

    buscarButton.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    buscarButton.addClassName("buscar-button");
    buscarButton.addClickListener(e -> buscarRegistros());

    row2.add(divisaField, estadoPremioField, canalField, buscarButton);

    panel.add(row1, row2);
    return panel;
  }

  /* ------------------------------------------------------------------ */
  /* 2) CONTENIDO SIMPLIFICADO */
  /* ------------------------------------------------------------------ */
  private VerticalLayout createContentCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Contenido simplificado sin tabla
    H2 contentTitle = new H2("Vista Simplificada");
    contentTitle.addClassName("bbva-grid-title");

    com.vaadin.flow.component.html.Span info =
        new com.vaadin.flow.component.html.Span(
            "Vista de apertura de cuenta simplificada sin tablas. "
                + "Los filtros están disponibles para búsqueda pero sin mostrar resultados en tabla.");

    card.add(contentTitle, info);
    return card;
  }

  private void buscarRegistros() {
    Notification.show("Búsqueda realizada").addThemeVariants(NotificationVariant.LUMO_SUCCESS);
  }
}
