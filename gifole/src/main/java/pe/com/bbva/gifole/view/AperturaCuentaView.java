package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.datepicker.DatePicker;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import java.util.List;
import pe.com.bbva.gifole.common.bean.AperturaCuentaDetalleBean;
import pe.com.bbva.gifole.components.DataTable;
import pe.com.bbva.gifole.dto.GridColumn;

@PageTitle("Apertura de Cuenta")
@Route(value = "apertura-cuenta", layout = MainLayout.class)
public class AperturaCuentaView extends VerticalLayout {

  // Filtros
  private final TextField codigoCentralField = new TextField();
  private final DatePicker fechaDesdeField = new DatePicker();
  private final DatePicker fechaHastaField = new DatePicker();
  private final TextField numeroCuentaField = new TextField();
  private final TextField divisaField = new TextField();
  private final TextField estadoPremioField = new TextField();
  private final TextField canalField = new TextField();
  private final Button buscarButton = new Button("Buscar", new Icon(VaadinIcon.SEARCH));

  // DataTable
  private DataTable<AperturaCuentaDetalleBean> dataTable;

  public AperturaCuentaView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Bandeja Apertura de Cuenta");
    title.addClassName("bbva-grid-title");

    // Panel de filtros
    VerticalLayout filtersPanel = createFiltersPanel();

    // DataTable vacío (sin Grid extra ni formulario)
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, filtersPanel, tableCard);
    add(mainLayout);
  }

  /* ------------------------------------------------------------------ */
  /* 1)  PANEL DE FILTROS                                               */
  /* ------------------------------------------------------------------ */
  private VerticalLayout createFiltersPanel() {
    VerticalLayout panel = new VerticalLayout();
    panel.addClassName("bbva-filters-card");
    panel.setWidthFull();
    panel.setSpacing(true);
    panel.setPadding(true);

    // Fila 1
    HorizontalLayout row1 = new HorizontalLayout();
    row1.setWidthFull();
    row1.setSpacing(true);
    row1.setAlignItems(Alignment.END);

    codigoCentralField.setLabel("Código central");
    codigoCentralField.addClassName("bbva-input-floating");
    codigoCentralField.setPlaceholder("Ingrese código central");
    codigoCentralField.setWidth("200px");

    fechaDesdeField.setLabel("Desde");
    fechaDesdeField.addClassName("bbva-input-floating");
    fechaDesdeField.setWidth("150px");

    fechaHastaField.setLabel("Hasta");
    fechaHastaField.addClassName("bbva-input-floating");
    fechaHastaField.setWidth("150px");

    numeroCuentaField.setLabel("Número de cuenta");
    numeroCuentaField.addClassName("bbva-input-floating");
    numeroCuentaField.setPlaceholder("Ingrese número de cuenta");
    numeroCuentaField.setWidth("200px");

    row1.add(codigoCentralField, fechaDesdeField, fechaHastaField, numeroCuentaField);

    // Fila 2
    HorizontalLayout row2 = new HorizontalLayout();
    row2.setWidthFull();
    row2.setSpacing(true);
    row2.setAlignItems(Alignment.END);

    divisaField.setLabel("Divisa");
    divisaField.addClassName("bbva-input-floating");
    divisaField.setPlaceholder("Ej: SOLES");
    divisaField.setWidth("120px");

    estadoPremioField.setLabel("Estado premio");
    estadoPremioField.addClassName("bbva-input-floating");
    estadoPremioField.setPlaceholder("Estado del premio");
    estadoPremioField.setWidth("150px");

    canalField.setLabel("Canal");
    canalField.addClassName("bbva-input-floating");
    canalField.setPlaceholder("Canal de registro");
    canalField.setWidth("180px");

    buscarButton.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    buscarButton.addClassName("buscar-button");
    buscarButton.addClickListener(e -> buscarRegistros());

    row2.add(divisaField, estadoPremioField, canalField, buscarButton);

    panel.add(row1, row2);
    return panel;
  }

  /* ------------------------------------------------------------------ */
  /* 2)  DATATABLE                                                      */
  /* ------------------------------------------------------------------ */
  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Columnas para AperturaCuentaDetalleBean
    List<GridColumn> columns = new ArrayList<>();
    columns.add(new GridColumn("id", "ID", "80px"));
    columns.add(new GridColumn("ctaEstado", "Estado Cuenta", "120px"));
    columns.add(new GridColumn("creador", "Creador", "150px"));
    columns.add(new GridColumn("editor", "Editor", "150px"));
    columns.add(new GridColumn("action-edit-delete", "Acciones", "150px"));

    // Lista vacía para que la tabla se muestre limpia
    List<AperturaCuentaDetalleBean> items = new ArrayList<>();

    // Instanciar el DataTable
    dataTable = new DataTable<>("apertura-detalle-table", columns, items, 10);

    // (opcional) listeners
    dataTable.onItemEdit(item -> Notification.show("Editar detalle " + item.getId()));
    dataTable.onItemDelete(item -> Notification.show("Eliminar detalle " + item.getId()));

    card.add(dataTable);
    return card;
  }

  private void buscarRegistros() {
    Notification.show("Búsqueda realizada").addThemeVariants(NotificationVariant.LUMO_SUCCESS);
  }
}
