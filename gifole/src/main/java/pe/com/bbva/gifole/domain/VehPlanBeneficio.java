package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_PLAN_BENEFICIO")
public class VehPlanBeneficio implements Serializable {
  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_VEH_PLANBENEFICIO")
  @TableGenerator(
      name = "SEQ_VEH_PLANBENEFICIO",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.VehPlanBeneficio",
      allocationSize = 1)
  private Long id;

  // bidirectional many-to-one association to Parametro
  @ManyToOne
  @JoinColumn(name = "PLAN")
  private VehPlan plan;

  // bidirectional many-to-one association to Seguro
  @ManyToOne
  @JoinColumn(name = "BENEFICIO")
  private VehBeneficio beneficio;

  @Column(name = "DETALLE", length = 100)
  private String detalle;
}
