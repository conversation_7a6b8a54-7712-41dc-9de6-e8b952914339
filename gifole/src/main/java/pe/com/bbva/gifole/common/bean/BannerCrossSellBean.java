package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import lombok.Data;
import pe.com.bbva.gifole.domain.Parametro;

@Data
public class BannerCrossSellBean implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;
  private Parametro flujo;
  private Parametro producto_1;
  private String imagen_desk_1;
  private String imagen_resp_1;
  private Parametro producto_2;
  private String imagen_desk_2;
  private String imagen_resp_2;
  private Parametro producto_3;
  private String imagen_desk_3;
  private String imagen_resp_3;
  private Parametro producto_4;
  private String imagen_desk_4;
  private String redireccion_1;
  private String redireccion_2;
  private String redireccion_3;
  private String redireccion_4;

  private String imagen_resp_4;
  private Parametro estado;
}
