package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class CancelacionProductoBean implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;
  private String codigoCentral;
  private String nombreCompletoCliente;
  private String nombreCliente;
  private String apellidosCliente;
  private String tipoDocumento;
  private String nroDocumento;
  private Date fechaHoraRegistro;
  private Date fechaRegistro1;
  private Date fechaRegistro2;
  private String subProducto;
  private String nroContrato;
  private String nroTarjetaTitular;
  private String celularCliente;
  private String correoCliente;
  private String motivoRechazo;
  private String codMotivoRechazo;
  private String otroMotivoRechazo;
  private String codEstado;
  private String estado;
  private Date fechaHoraModificacion;
  private String codigoBloqueoTarjetas;
  private String canal;
  private String registroExterno;
  private String nroCuentCargo;
  private String montoPrestamo;
  private String nombreEmpresa;
  private String codigoEmpresa;
  private String codigoUsuario;
  private String moneda;
  private String codigoEstadoSolicitud;
  private String tipoGarantia;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public String getNroDocumento() {
    return nroDocumento;
  }

  public void setNroDocumento(String nroDocumento) {
    this.nroDocumento = nroDocumento;
  }

  public Date getFechaHoraRegistro() {
    return fechaHoraRegistro;
  }

  public void setFechaHoraRegistro(Date fechaHoraRegistro) {
    this.fechaHoraRegistro = fechaHoraRegistro;
  }

  public Date getFechaRegistro1() {
    return fechaRegistro1;
  }

  public void setFechaRegistro1(Date fechaRegistro1) {
    this.fechaRegistro1 = fechaRegistro1;
  }

  public Date getFechaRegistro2() {
    return fechaRegistro2;
  }

  public void setFechaRegistro2(Date fechaRegistro2) {
    this.fechaRegistro2 = fechaRegistro2;
  }

  public String getSubProducto() {
    return subProducto;
  }

  public void setSubProducto(String subProducto) {
    this.subProducto = subProducto;
  }

  public String getNroContrato() {
    return nroContrato;
  }

  public void setNroContrato(String nroContrato) {
    this.nroContrato = nroContrato;
  }

  public String getNroTarjetaTitular() {
    return nroTarjetaTitular;
  }

  public void setNroTarjetaTitular(String nroTarjetaTitular) {
    this.nroTarjetaTitular = nroTarjetaTitular;
  }

  public String getCorreoCliente() {
    return correoCliente;
  }

  public void setCorreoCliente(String correoCliente) {
    this.correoCliente = correoCliente;
  }

  public String getMotivoRechazo() {
    return motivoRechazo;
  }

  public void setMotivoRechazo(String motivoRechazo) {
    this.motivoRechazo = motivoRechazo;
  }

  public String getOtroMotivoRechazo() {
    return otroMotivoRechazo;
  }

  public void setOtroMotivoRechazo(String otroMotivoRechazo) {
    this.otroMotivoRechazo = otroMotivoRechazo;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public Date getFechaHoraModificacion() {
    return fechaHoraModificacion;
  }

  public void setFechaHoraModificacion(Date fechaHoraModificacion) {
    this.fechaHoraModificacion = fechaHoraModificacion;
  }

  public String getCodigoBloqueoTarjetas() {
    return codigoBloqueoTarjetas;
  }

  public void setCodigoBloqueoTarjetas(String codigoBloqueoTarjetas) {
    this.codigoBloqueoTarjetas = codigoBloqueoTarjetas;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public String getRegistroExterno() {
    return registroExterno;
  }

  public void setRegistroExterno(String registroExterno) {
    this.registroExterno = registroExterno;
  }

  public String getNroCuentCargo() {
    return nroCuentCargo;
  }

  public void setNroCuentCargo(String nroCuentCargo) {
    this.nroCuentCargo = nroCuentCargo;
  }

  public String getCodEstado() {
    return codEstado;
  }

  public void setCodEstado(String codEstado) {
    this.codEstado = codEstado;
  }

  public String getCodMotivoRechazo() {
    return codMotivoRechazo;
  }

  public void setCodMotivoRechazo(String codMotivoRechazo) {
    this.codMotivoRechazo = codMotivoRechazo;
  }

  public String getNombreCompletoCliente() {
    return nombreCompletoCliente;
  }

  public void setNombreCompletoCliente(String nombreCompletoCliente) {
    this.nombreCompletoCliente = nombreCompletoCliente;
  }

  public String getNombreCliente() {
    return nombreCliente;
  }

  public void setNombreCliente(String nombreCliente) {
    this.nombreCliente = nombreCliente;
  }

  public String getApellidosCliente() {
    return apellidosCliente;
  }

  public void setApellidosCliente(String apellidosCliente) {
    this.apellidosCliente = apellidosCliente;
  }

  public String getMontoPrestamo() {
    return montoPrestamo;
  }

  public void setMontoPrestamo(String montoPrestamo) {
    this.montoPrestamo = montoPrestamo;
  }

  public String getCelularCliente() {
    return celularCliente;
  }

  public void setCelularCliente(String celularCliente) {
    this.celularCliente = celularCliente;
  }

  public String getNombreEmpresa() {
    return nombreEmpresa;
  }

  public void setNombreEmpresa(String nombreEmpresa) {
    this.nombreEmpresa = nombreEmpresa;
  }

  public String getCodigoEmpresa() {
    return codigoEmpresa;
  }

  public void setCodigoEmpresa(String codigoEmpresa) {
    this.codigoEmpresa = codigoEmpresa;
  }

  public String getCodigoUsuario() {
    return codigoUsuario;
  }

  public void setCodigoUsuario(String codigoUsuario) {
    this.codigoUsuario = codigoUsuario;
  }

  public String getMoneda() {
    return moneda;
  }

  public void setMoneda(String moneda) {
    this.moneda = moneda;
  }

  public String getCodigoEstadoSolicitud() {
    return codigoEstadoSolicitud;
  }

  public void setCodigoEstadoSolicitud(String codigoEstadoSolicitud) {
    this.codigoEstadoSolicitud = codigoEstadoSolicitud;
  }

  public String getTipoGarantia() {
    return tipoGarantia;
  }

  public void setTipoGarantia(String tipoGarantia) {
    this.tipoGarantia = tipoGarantia;
  }
}
