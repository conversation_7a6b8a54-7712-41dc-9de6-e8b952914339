package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class TarjetaUpgradeBean implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;
  private String codigoCentral;
  private String tipoDocumento;
  private String numeroDocumento;
  private String nombreCompleto;
  private String telefono;
  private String correo;
  private String nombreTarjetaOrigen;
  private String tipoTarjetaOrigen;
  private String nombreNuevaTarjeta;
  private String tipoNuevaTarjeta;
  private String moneda;
  private String lineaCredito;
  private String canal;
  private String estado;
  private String creador;
  private String fechaCreacion;
  private String editor;
  private String fechaModificacion;
  private Date fechaRegistroDesde;
  private Date fechaRegistroHasta;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public String getNumeroDocumento() {
    return numeroDocumento;
  }

  public void setNumeroDocumento(String numeroDocumento) {
    this.numeroDocumento = numeroDocumento;
  }

  public String getNombreCompleto() {
    return nombreCompleto;
  }

  public void setNombreCompleto(String nombreCompleto) {
    this.nombreCompleto = nombreCompleto;
  }

  public String getTelefono() {
    return telefono;
  }

  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public String getNombreTarjetaOrigen() {
    return nombreTarjetaOrigen;
  }

  public void setNombreTarjetaOrigen(String nombreTarjetaOrigen) {
    this.nombreTarjetaOrigen = nombreTarjetaOrigen;
  }

  public String getTipoTarjetaOrigen() {
    return tipoTarjetaOrigen;
  }

  public void setTipoTarjetaOrigen(String tipoTarjetaOrigen) {
    this.tipoTarjetaOrigen = tipoTarjetaOrigen;
  }

  public String getNombreNuevaTarjeta() {
    return nombreNuevaTarjeta;
  }

  public void setNombreNuevaTarjeta(String nombreNuevaTarjeta) {
    this.nombreNuevaTarjeta = nombreNuevaTarjeta;
  }

  public String getTipoNuevaTarjeta() {
    return tipoNuevaTarjeta;
  }

  public void setTipoNuevaTarjeta(String tipoNuevaTarjeta) {
    this.tipoNuevaTarjeta = tipoNuevaTarjeta;
  }

  public String getMoneda() {
    return moneda;
  }

  public void setMoneda(String moneda) {
    this.moneda = moneda;
  }

  public String getLineaCredito() {
    return lineaCredito;
  }

  public void setLineaCredito(String lineaCredito) {
    this.lineaCredito = lineaCredito;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public String getCreador() {
    return creador;
  }

  public void setCreador(String creador) {
    this.creador = creador;
  }

  public String getFechaCreacion() {
    return fechaCreacion;
  }

  public void setFechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
  }

  public String getEditor() {
    return editor;
  }

  public void setEditor(String editor) {
    this.editor = editor;
  }

  public String getFechaModificacion() {
    return fechaModificacion;
  }

  public void setFechaModificacion(String fechaModificacion) {
    this.fechaModificacion = fechaModificacion;
  }

  public Date getFechaRegistroDesde() {
    return fechaRegistroDesde;
  }

  public void setFechaRegistroDesde(Date fechaRegistroDesde) {
    this.fechaRegistroDesde = fechaRegistroDesde;
  }

  public Date getFechaRegistroHasta() {
    return fechaRegistroHasta;
  }

  public void setFechaRegistroHasta(Date fechaRegistroHasta) {
    this.fechaRegistroHasta = fechaRegistroHasta;
  }
}
