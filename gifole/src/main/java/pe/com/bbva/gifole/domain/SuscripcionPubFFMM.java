package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@Entity
@Table(name = "SUSCRIPCION_PUB_FFMM")
public class SuscripcionPubFFMM implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_SUSCRIPCION_PUB_FFMM")
  @TableGenerator(
      name = "SEQ_SUSCRIPCION_PUB_FFMM",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.SuscripcionPubFFMM",
      allocationSize = 1)
  private Long id;

  @Column(name = "NOMBRE", length = 50)
  private String nombre;

  @Column(name = "APELLIDO", length = 50)
  private String apellido;

  @Column(name = "TIPO_DOCUMENTO", length = 11)
  private String tipoDocumento;

  @Column(name = "DOCUMENTO", length = 20)
  private String documento;

  @Column(name = "DEPARTAMENTO", length = 20)
  private String departamento;

  @Column(name = "CORREO", length = 80)
  private String correo;

  @Column(name = "TELEFONO", length = 20)
  private String telefono;

  @Column(name = "TELEFONO2", length = 20)
  private String telefono2;

  @Column(name = "HORARIO", length = 20)
  private String horario;

  @Column(name = "TIPO_CLIENTE", length = 1)
  private String tipoCliente;

  @Column(name = "SUSCRIPCION", length = 1)
  private String suscripcion;

  @Column(name = "AFILIACION_EECC", length = 1)
  private String afiliacionEECC;

  @Column(name = "OBJETIVO", length = 30)
  private String objetivo;

  @Column(name = "TIPO_CALCULO", length = 1)
  private String tipoCalculo;

  @Column(name = "DIVISA_APORTE", length = 10)
  private String divisaAporte;

  @Column(name = "MONTO", length = 15, precision = 2)
  private BigDecimal monto;

  @Column(name = "PLAZO", length = 10)
  private Integer plazo;

  @Column(name = "PROCESADO", length = 1)
  private String procesado;

  @Column(name = "FECHA_REGISTRO")
  private Date fechaRegistro;

  @Column(name = "AUTORIZACION", length = 1)
  private String autorizacion;

  @Transient private List<SuscripcionPubFFMMDetalle> suscripcionPubFFMMDetalles;
}
