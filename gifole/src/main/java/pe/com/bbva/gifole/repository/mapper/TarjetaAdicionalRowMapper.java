package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.TCAdicional;
import pe.com.bbva.gifole.domain.TCAdicionalDetalle;

@SuppressWarnings("rawtypes")
@Component
public class TarjetaAdicionalRowMapper implements RowMapper {

  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    TCAdicionalDetalle param = new TCAdicionalDetalle();
    TCAdicional paramTit = new TCAdicional();
    param.setId(rs.getLong("ID"));
    paramTit.setCodigoCentral(rs.getString("CODIGO_CENTRAL"));
    param.setNroContrato(rs.getString("NRO_CONTRATO"));
    param.setCodCliente(rs.getString("COD_CLIENTE"));
    param.setNroTarjeta(rs.getString("NRO_TARJETA"));
    param.setFechaModificacionFormat(rs.getString("FECHA_MODIFICACION"));

    param.setTcAdicional(paramTit);

    return param;
  }
}
