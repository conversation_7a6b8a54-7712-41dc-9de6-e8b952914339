package pe.com.bbva.gifole.domain;

import java.io.Serializable;

@SuppressWarnings("serial")
public class Canal implements Serializable {

  private String id;

  private String nombre;

  private String descripcion;

  private String estado;

  private String fecha_registro;

  private String creador;

  private String fecha_modificacion;

  private String editor;

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public String getDescripcion() {
    return descripcion;
  }

  public void setDescripcion(String descripcion) {
    this.descripcion = descripcion;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public String getFecha_registro() {
    return fecha_registro;
  }

  public void setFecha_registro(String fecha_registro) {
    this.fecha_registro = fecha_registro;
  }

  public String getCreador() {
    return creador;
  }

  public void setCreador(String creador) {
    this.creador = creador;
  }

  public String getFecha_modificacion() {
    return fecha_modificacion;
  }

  public void setFecha_modificacion(String fecha_modificacion) {
    this.fecha_modificacion = fecha_modificacion;
  }

  public String getEditor() {
    return editor;
  }

  public void setEditor(String editor) {
    this.editor = editor;
  }
}
