package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class FondoMutuoBean implements Serializable {

  /** */
  private static final long serialVersionUID = 8791971568992119458L;

  private Long id;
  private String codigoCentral;
  private String nombres;
  private String indOpcionRequisitos;
  private String tipoEnvioEECC;
  private String descEnvioEECC;
  private String indLpdp;
  private String estado;
  private String canal;
  private String creador;
  private Date fechaCreacion;
  private String fechaDeCreacion;
  private String editor;
  private Date fechaModificacion;
  private FondoMutuoDetalleBean fondoMutuoDetalle;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getNombres() {
    return nombres;
  }

  public void setNombres(String nombres) {
    this.nombres = nombres;
  }

  public String getIndOpcionRequisitos() {
    return indOpcionRequisitos;
  }

  public void setIndOpcionRequisitos(String indOpcionRequisitos) {
    this.indOpcionRequisitos = indOpcionRequisitos;
  }

  public String getTipoEnvioEECC() {
    return tipoEnvioEECC;
  }

  public void setTipoEnvioEECC(String tipoEnvioEECC) {
    this.tipoEnvioEECC = tipoEnvioEECC;
  }

  public String getDescEnvioEECC() {
    return descEnvioEECC;
  }

  public void setDescEnvioEECC(String descEnvioEECC) {
    this.descEnvioEECC = descEnvioEECC;
  }

  public String getIndLpdp() {
    return indLpdp;
  }

  public void setIndLpdp(String indLpdp) {
    this.indLpdp = indLpdp;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public String getCreador() {
    return creador;
  }

  public void setCreador(String creador) {
    this.creador = creador;
  }

  public Date getFechaCreacion() {
    return fechaCreacion;
  }

  public void setFechaCreacion(Date fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
  }

  public String getEditor() {
    return editor;
  }

  public void setEditor(String editor) {
    this.editor = editor;
  }

  public Date getFechaModificacion() {
    return fechaModificacion;
  }

  public void setFechaModificacion(Date fechaModificacion) {
    this.fechaModificacion = fechaModificacion;
  }

  public FondoMutuoDetalleBean getFondoMutuoDetalle() {
    return fondoMutuoDetalle;
  }

  public void setFondoMutuoDetalle(FondoMutuoDetalleBean fondoMutuoDetalle) {
    this.fondoMutuoDetalle = fondoMutuoDetalle;
  }

  public String getFechaDeCreacion() {
    return fechaDeCreacion;
  }

  public void setFechaDeCreacion(String fechaDeCreacion) {
    this.fechaDeCreacion = fechaDeCreacion;
  }
}
