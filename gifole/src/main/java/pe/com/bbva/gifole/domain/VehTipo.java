package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_TIPO")
public class VehTipo implements Serializable {

  @Id
  @Column(name = "CODIGO", length = 4, unique = true)
  private String codigo;

  @Column(name = "NOMBRE", length = 50)
  private String nombre;

  @Column(name = "ESTADO", length = 1)
  private String estado;

  @Column(name = "CREACION", nullable = false, insertable = true, updatable = false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date creacion;

  @Column(name = "EDICION", insertable = false, updatable = true)
  @Temporal(TemporalType.TIMESTAMP)
  private Date edicion;
}
