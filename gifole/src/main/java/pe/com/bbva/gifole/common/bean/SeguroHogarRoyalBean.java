package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class SeguroHogarRoyalBean implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;
  private String fechaRegistro;
  private String nombre;
  private String apellido;
  private String tipoDocumento;
  private String documento;
  private String correo;
  private String telefono;
  private String tipoPropiedad;
  private String propiedad;
  private String direccion;
  private String zona;
  private String distrito;
  private String departamento;
  private String provincia;
  private String moneda;
  private String monto;
  private String tipoUso;
  private String uso;
  private String tipoMaterial;
  private String material;
  private String antiguedad;
  private String numeroPisos;
  private Date fechaRegistroDesde;
  private Date fechaRegistroHasta;
  private String objetoCotizacion;
  private String informacionBanco;
  private String codigoOficina;
  private String codigoUsuario;
  private String horarioContacto;
  private String canal;
  private String indicadorSiEsCliente;
  private String possedor;
  private String PoseedorTipo;
  private String planCuotasMoneda;
  private String planCuotasMonto;
  private String primaMonto;
  private String primaMontoMoneda;

  public String getPossedor() {
    return possedor;
  }

  public void setPossedor(String possedor) {
    this.possedor = possedor;
  }

  public String getPoseedorTipo() {
    return PoseedorTipo;
  }

  public void setPoseedorTipo(String poseedorTipo) {
    PoseedorTipo = poseedorTipo;
  }

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getFechaRegistro() {
    return fechaRegistro;
  }

  public void setFechaRegistro(String fechaRegistro) {
    this.fechaRegistro = fechaRegistro;
  }

  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public String getApellido() {
    return apellido;
  }

  public void setApellido(String apellido) {
    this.apellido = apellido;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public String getDocumento() {
    return documento;
  }

  public void setDocumento(String documento) {
    this.documento = documento;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public String getTelefono() {
    return telefono;
  }

  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  public String getTipoPropiedad() {
    return tipoPropiedad;
  }

  public void setTipoPropiedad(String tipoPropiedad) {
    this.tipoPropiedad = tipoPropiedad;
  }

  public String getPropiedad() {
    return propiedad;
  }

  public void setPropiedad(String propiedad) {
    this.propiedad = propiedad;
  }

  public String getDireccion() {
    return direccion;
  }

  public void setDireccion(String direccion) {
    this.direccion = direccion;
  }

  public String getZona() {
    return zona;
  }

  public void setZona(String zona) {
    this.zona = zona;
  }

  public String getDistrito() {
    return distrito;
  }

  public void setDistrito(String distrito) {
    this.distrito = distrito;
  }

  public String getDepartamento() {
    return departamento;
  }

  public void setDepartamento(String departamento) {
    this.departamento = departamento;
  }

  public String getProvincia() {
    return provincia;
  }

  public void setProvincia(String provincia) {
    this.provincia = provincia;
  }

  public String getMoneda() {
    return moneda;
  }

  public void setMoneda(String moneda) {
    this.moneda = moneda;
  }

  public String getMonto() {
    return monto;
  }

  public void setMonto(String monto) {
    this.monto = monto;
  }

  public String getTipoUso() {
    return tipoUso;
  }

  public void setTipoUso(String tipoUso) {
    this.tipoUso = tipoUso;
  }

  public String getUso() {
    return uso;
  }

  public void setUso(String uso) {
    this.uso = uso;
  }

  public String getTipoMaterial() {
    return tipoMaterial;
  }

  public void setTipoMaterial(String tipoMaterial) {
    this.tipoMaterial = tipoMaterial;
  }

  public String getMaterial() {
    return material;
  }

  public void setMaterial(String material) {
    this.material = material;
  }

  public String getAntiguedad() {
    return antiguedad;
  }

  public void setAntiguedad(String antiguedad) {
    this.antiguedad = antiguedad;
  }

  public String getNumeroPisos() {
    return numeroPisos;
  }

  public void setNumeroPisos(String numeroPisos) {
    this.numeroPisos = numeroPisos;
  }

  public Date getFechaRegistroDesde() {
    return fechaRegistroDesde;
  }

  public void setFechaRegistroDesde(Date fechaRegistroDesde) {
    this.fechaRegistroDesde = fechaRegistroDesde;
  }

  public Date getFechaRegistroHasta() {
    return fechaRegistroHasta;
  }

  public void setFechaRegistroHasta(Date fechaRegistroHasta) {
    this.fechaRegistroHasta = fechaRegistroHasta;
  }

  public String getObjetoCotizacion() {
    return objetoCotizacion;
  }

  public void setObjetoCotizacion(String objetoCotizacion) {
    this.objetoCotizacion = objetoCotizacion;
  }

  public String getInformacionBanco() {
    return informacionBanco;
  }

  public void setInformacionBanco(String informacionBanco) {
    this.informacionBanco = informacionBanco;
  }

  public String getCodigoOficina() {
    return codigoOficina;
  }

  public void setCodigoOficina(String codigoOficina) {
    this.codigoOficina = codigoOficina;
  }

  public String getCodigoUsuario() {
    return codigoUsuario;
  }

  public void setCodigoUsuario(String codigoUsuario) {
    this.codigoUsuario = codigoUsuario;
  }

  public String getHorarioContacto() {
    return horarioContacto;
  }

  public void setHorarioContacto(String horarioContacto) {
    this.horarioContacto = horarioContacto;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public String getIndicadorSiEsCliente() {
    return indicadorSiEsCliente;
  }

  public void setIndicadorSiEsCliente(String indicadorSiEsCliente) {
    this.indicadorSiEsCliente = indicadorSiEsCliente;
  }

  public String getPlanCuotasMoneda() {
    return planCuotasMoneda;
  }

  public void setPlanCuotasMoneda(String planCuotasMoneda) {
    this.planCuotasMoneda = planCuotasMoneda;
  }

  public String getPlanCuotasMonto() {
    return planCuotasMonto;
  }

  public void setPlanCuotasMonto(String planCuotasMonto) {
    this.planCuotasMonto = planCuotasMonto;
  }

  public String getPrimaMonto() {
    return primaMonto;
  }

  public void setPrimaMonto(String primaMonto) {
    this.primaMonto = primaMonto;
  }

  public String getPrimaMontoMoneda() {
    return primaMontoMoneda;
  }

  public void setPrimaMontoMoneda(String primaMontoMoneda) {
    this.primaMontoMoneda = primaMontoMoneda;
  }
}
