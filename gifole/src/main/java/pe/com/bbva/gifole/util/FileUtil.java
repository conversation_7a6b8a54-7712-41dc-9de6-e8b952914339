package pe.com.bbva.gifole.util;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.io.FileUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import pe.com.bbva.gifole.common.bean.FileBean;
import pe.com.bbva.gifole.common.enums.PatronFechaEnum;
import pe.com.bbva.gifole.domain.Parametro;
import pe.com.bbva.gifole.domain.Parametro.FILE_UTIL;

public class FileUtil {

  private static Logger logger = LogManager.getLogger(FileUtil.class);

  public static List<FileBean> listarArchivoRepositorio(String rutaRepositorio) {
    List<FileBean> listaArchivoRepositorio = new ArrayList<>();

    try {
      String[] extensions = new String[] {Parametro.FILE_UTIL.FILE_PDF};

      List<File> files =
          (List<File>) FileUtils.listFiles(new File(rutaRepositorio), extensions, false);

      for (File fil : files) {
        FileBean fileBean = new FileBean();
        SimpleDateFormat sdf = new SimpleDateFormat(PatronFechaEnum.FECHA_HORA_MINUTO.getPatron());
        fileBean.setNombre(fil.getName());
        fileBean.setExtension(FILE_UTIL.FILE_PDF);
        fileBean.setFechaModificacion(sdf.format(fil.lastModified()));
        fileBean.setRutaArchivo(rutaRepositorio);
        listaArchivoRepositorio.add(fileBean);
      }

    } catch (Exception e) {
      logger.error("Error al listarArchivoRepositorio", Utilitario.encode(e.getMessage()));
    }
    return listaArchivoRepositorio;
  }
}
