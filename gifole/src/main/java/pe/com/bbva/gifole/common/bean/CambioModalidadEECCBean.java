package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class CambioModalidadEECCBean implements Serializable {

  private static final long serialVersionUID = 4395374511960448215L;

  private Long id;
  private String codigoCentral;
  private String nombreCompletoCliente;
  private String nombreCliente;
  private String apellidosCliente;
  private String tipoDocumento;
  private String nroDocumento;
  private Date fechaHoraRegistro;
  private Date fechaRegistro1;
  private Date fechaRegistro2;
  private String subProducto;
  private String nroContrato;
  private String nroTarjetaTitular;
  private String correoClienteContacto;
  private String tipoModalidadEnvio;
  private String direccionEntrega;
  private String correoClienteEntrega;
  private String estado;
  private String motivoProcesado;
  private String codMotivoProcesado;
  private String otroMotivoRechazo;
  private String codEstado;
  private Date fechaHoraModificacion;
  private String canal;
  private String registroExterno;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public String getNroDocumento() {
    return nroDocumento;
  }

  public void setNroDocumento(String nroDocumento) {
    this.nroDocumento = nroDocumento;
  }

  public Date getFechaHoraRegistro() {
    return fechaHoraRegistro;
  }

  public void setFechaHoraRegistro(Date fechaHoraRegistro) {
    this.fechaHoraRegistro = fechaHoraRegistro;
  }

  public Date getFechaRegistro1() {
    return fechaRegistro1;
  }

  public void setFechaRegistro1(Date fechaRegistro1) {
    this.fechaRegistro1 = fechaRegistro1;
  }

  public Date getFechaRegistro2() {
    return fechaRegistro2;
  }

  public void setFechaRegistro2(Date fechaRegistro2) {
    this.fechaRegistro2 = fechaRegistro2;
  }

  public String getSubProducto() {
    return subProducto;
  }

  public void setSubProducto(String subProducto) {
    this.subProducto = subProducto;
  }

  public String getNroContrato() {
    return nroContrato;
  }

  public void setNroContrato(String nroContrato) {
    this.nroContrato = nroContrato;
  }

  public String getNroTarjetaTitular() {
    return nroTarjetaTitular;
  }

  public void setNroTarjetaTitular(String nroTarjetaTitular) {
    this.nroTarjetaTitular = nroTarjetaTitular;
  }

  public String getOtroMotivoRechazo() {
    return otroMotivoRechazo;
  }

  public void setOtroMotivoRechazo(String otroMotivoRechazo) {
    this.otroMotivoRechazo = otroMotivoRechazo;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public Date getFechaHoraModificacion() {
    return fechaHoraModificacion;
  }

  public void setFechaHoraModificacion(Date fechaHoraModificacion) {
    this.fechaHoraModificacion = fechaHoraModificacion;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public String getRegistroExterno() {
    return registroExterno;
  }

  public void setRegistroExterno(String registroExterno) {
    this.registroExterno = registroExterno;
  }

  public static long getSerialversionuid() {
    return serialVersionUID;
  }

  public String getCodEstado() {
    return codEstado;
  }

  public void setCodEstado(String codEstado) {
    this.codEstado = codEstado;
  }

  public String getNombreCompletoCliente() {
    return nombreCompletoCliente;
  }

  public void setNombreCompletoCliente(String nombreCompletoCliente) {
    this.nombreCompletoCliente = nombreCompletoCliente;
  }

  public String getNombreCliente() {
    return nombreCliente;
  }

  public void setNombreCliente(String nombreCliente) {
    this.nombreCliente = nombreCliente;
  }

  public String getApellidosCliente() {
    return apellidosCliente;
  }

  public void setApellidosCliente(String apellidosCliente) {
    this.apellidosCliente = apellidosCliente;
  }

  public String getCorreoClienteContacto() {
    return correoClienteContacto;
  }

  public void setCorreoClienteContacto(String correoClienteContacto) {
    this.correoClienteContacto = correoClienteContacto;
  }

  public String getTipoModalidadEnvio() {
    return tipoModalidadEnvio;
  }

  public void setTipoModalidadEnvio(String tipoModalidadEnvio) {
    this.tipoModalidadEnvio = tipoModalidadEnvio;
  }

  public String getDireccionEntrega() {
    return direccionEntrega;
  }

  public void setDireccionEntrega(String direccionEntrega) {
    this.direccionEntrega = direccionEntrega;
  }

  public String getCorreoClienteEntrega() {
    return correoClienteEntrega;
  }

  public void setCorreoClienteEntrega(String correoClienteEntrega) {
    this.correoClienteEntrega = correoClienteEntrega;
  }

  public String getMotivoProcesado() {
    return motivoProcesado;
  }

  public void setMotivoProcesado(String motivoProcesado) {
    this.motivoProcesado = motivoProcesado;
  }

  public String getCodMotivoProcesado() {
    return codMotivoProcesado;
  }

  public void setCodMotivoProcesado(String codMotivoProcesado) {
    this.codMotivoProcesado = codMotivoProcesado;
  }
}
