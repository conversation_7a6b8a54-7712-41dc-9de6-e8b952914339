package pe.com.bbva.gifole.domain;

public class TCTocCarretera {

  private Long id;
  private String idArchivo;
  private String codigoCentral;
  private String tipoDocumento;
  private String numeroDocumento;
  private String nombres;
  private String apellidoPaterno;
  private String apellidoMaterno;
  private String celular;
  private String correo;
  private String rptaLpdp;
  private String moi2TitleId;
  private String beneficio;
  private String moi2Limitadj;
  private String moi2Workplaceval;
  private String moi2Addresseval;
  private String estado;
  private String motivoFin;
  private String motivoRechazo;
  private String fechaLead;
  private String tipoFlujo;
  private String tipoLead;
  private String horaContacto;
  private String motivoContacto;
  private String pasoMotor;
  private String flagOferta;
  private String respuestaMotor;
  private String codClasificacion;
  private String clasificacion;
  private String reglaRechazo;
  private String descripcion;
  private String resultadoEvaluacion;
  private String modeloCelular;
  private String sistemaOperativo;
  private String ip;
  private String navegador;
  private String origen;
  private String codTinker;
  private String fechaRegistro;
  private String canal;

  public Long getId() {
    return id;
  }

  public String getIdArchivo() {
    return idArchivo;
  }

  public void setIdArchivo(String idArchivo) {
    this.idArchivo = idArchivo;
  }

  public String getMoi2Limitadj() {
    return moi2Limitadj;
  }

  public void setMoi2Limitadj(String moi2Limitadj) {
    this.moi2Limitadj = moi2Limitadj;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public String getNumeroDocumento() {
    return numeroDocumento;
  }

  public void setNumeroDocumento(String numeroDocumento) {
    this.numeroDocumento = numeroDocumento;
  }

  public String getNombres() {
    return nombres;
  }

  public void setNombres(String nombres) {
    this.nombres = nombres;
  }

  public String getApellidoPaterno() {
    return apellidoPaterno;
  }

  public void setApellidoPaterno(String apellidoPaterno) {
    this.apellidoPaterno = apellidoPaterno;
  }

  public String getApellidoMaterno() {
    return apellidoMaterno;
  }

  public void setApellidoMaterno(String apellidoMaterno) {
    this.apellidoMaterno = apellidoMaterno;
  }

  public String getCelular() {
    return celular;
  }

  public void setCelular(String celular) {
    this.celular = celular;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public String getBeneficio() {
    return beneficio;
  }

  public void setBeneficio(String beneficio) {
    this.beneficio = beneficio;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public String getMotivoFin() {
    return motivoFin;
  }

  public void setMotivoFin(String motivoFin) {
    this.motivoFin = motivoFin;
  }

  public String getFechaLead() {
    return fechaLead;
  }

  public void setFechaLead(String fechaLead) {
    this.fechaLead = fechaLead;
  }

  public String getHoraContacto() {
    return horaContacto;
  }

  public void setHoraContacto(String horaContacto) {
    this.horaContacto = horaContacto;
  }

  public String getMotivoContacto() {
    return motivoContacto;
  }

  public void setMotivoContacto(String motivoContacto) {
    this.motivoContacto = motivoContacto;
  }

  public String getPasoMotor() {
    return pasoMotor;
  }

  public void setPasoMotor(String pasoMotor) {
    this.pasoMotor = pasoMotor;
  }

  public String getFlagOferta() {
    return flagOferta;
  }

  public void setFlagOferta(String flagOferta) {
    this.flagOferta = flagOferta;
  }

  public String getRespuestaMotor() {
    return respuestaMotor;
  }

  public void setRespuestaMotor(String respuestaMotor) {
    this.respuestaMotor = respuestaMotor;
  }

  public String getReglaRechazo() {
    return reglaRechazo;
  }

  public void setReglaRechazo(String reglaRechazo) {
    this.reglaRechazo = reglaRechazo;
  }

  public String getDescripcion() {
    return descripcion;
  }

  public void setDescripcion(String descripcion) {
    this.descripcion = descripcion;
  }

  public String getResultadoEvaluacion() {
    return resultadoEvaluacion;
  }

  public void setResultadoEvaluacion(String resultadoEvaluacion) {
    this.resultadoEvaluacion = resultadoEvaluacion;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public String getRptaLpdp() {
    return rptaLpdp;
  }

  public void setRptaLpdp(String rptaLpdp) {
    this.rptaLpdp = rptaLpdp;
  }

  public String getMoi2TitleId() {
    return moi2TitleId;
  }

  public void setMoi2TitleId(String moi2TitleId) {
    this.moi2TitleId = moi2TitleId;
  }

  public String getMoi2Workplaceval() {
    return moi2Workplaceval;
  }

  public void setMoi2Workplaceval(String moi2Workplaceval) {
    this.moi2Workplaceval = moi2Workplaceval;
  }

  public String getMoi2Addresseval() {
    return moi2Addresseval;
  }

  public void setMoi2Addresseval(String moi2Addresseval) {
    this.moi2Addresseval = moi2Addresseval;
  }

  public String getMotivoRechazo() {
    return motivoRechazo;
  }

  public void setMotivoRechazo(String motivoRechazo) {
    this.motivoRechazo = motivoRechazo;
  }

  public String getTipoFlujo() {
    return tipoFlujo;
  }

  public void setTipoFlujo(String tipoFlujo) {
    this.tipoFlujo = tipoFlujo;
  }

  public String getTipoLead() {
    return tipoLead;
  }

  public void setTipoLead(String tipoLead) {
    this.tipoLead = tipoLead;
  }

  public String getCodClasificacion() {
    return codClasificacion;
  }

  public void setCodClasificacion(String codClasificacion) {
    this.codClasificacion = codClasificacion;
  }

  public String getClasificacion() {
    return clasificacion;
  }

  public void setClasificacion(String clasificacion) {
    this.clasificacion = clasificacion;
  }

  public String getModeloCelular() {
    return modeloCelular;
  }

  public void setModeloCelular(String modeloCelular) {
    this.modeloCelular = modeloCelular;
  }

  public String getSistemaOperativo() {
    return sistemaOperativo;
  }

  public void setSistemaOperativo(String sistemaOperativo) {
    this.sistemaOperativo = sistemaOperativo;
  }

  public String getIp() {
    return ip;
  }

  public void setIp(String ip) {
    this.ip = ip;
  }

  public String getNavegador() {
    return navegador;
  }

  public void setNavegador(String navegador) {
    this.navegador = navegador;
  }

  public String getOrigen() {
    return origen;
  }

  public void setOrigen(String origen) {
    this.origen = origen;
  }

  public String getCodTinker() {
    return codTinker;
  }

  public void setCodTinker(String codTinker) {
    this.codTinker = codTinker;
  }

  public String getFechaRegistro() {
    return fechaRegistro;
  }

  public void setFechaRegistro(String fechaRegistro) {
    this.fechaRegistro = fechaRegistro;
  }
}
