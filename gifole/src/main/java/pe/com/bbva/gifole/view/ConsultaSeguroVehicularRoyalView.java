package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.SeguroVehicularRoyalBean;
import pe.com.bbva.gifole.util.Constante;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Seguro Vehicular Royal")
@Route(value = "reporte/seguros/consulta-seguro-vehicular-royal", layout = MainLayout.class)
public class ConsultaSeguroVehicularRoyalView extends VerticalLayout {

  public ConsultaSeguroVehicularRoyalView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Seguro Vehicular Royal");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private String obtenerNumeroCotizacion(SeguroVehicularRoyalBean seguroVehicularRoyalBean) {
    String numeroCotizacion = seguroVehicularRoyalBean.getObjetoCotizacion();
    if (Objects.nonNull(seguroVehicularRoyalBean.getStep())
        && StringUtils.isNotBlank(seguroVehicularRoyalBean.getStep())
        && (seguroVehicularRoyalBean
                .getStep()
                .equals(Constante.SEGURO_VEHICULAR_ROYAL.PRIMER_STEP_VISIBLE)
            || seguroVehicularRoyalBean
                .getStep()
                .equals(Constante.SEGURO_VEHICULAR_ROYAL.SEGUNDO_STEP_VISIBLE))) {
      numeroCotizacion = StringUtils.EMPTY;
    }
    return numeroCotizacion;
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    DataTable<SeguroVehicularRoyalBean> dataTable =
        DataTable.<SeguroVehicularRoyalBean>builder()
            .id("tabla-seguro-vehicular")
            .column("fechaRegistro", "Fecha Registro", bean -> bean.getFechaRegistro(), "150px")
            .column("step", "Step", bean -> StringUtils.trimToEmpty(bean.getStep()), "100px")
            .column("nombre", "Nombre", bean -> StringUtils.trimToEmpty(bean.getNombre()), "150px")
            .column(
                "apellido",
                "Apellido",
                bean -> StringUtils.trimToEmpty(bean.getApellido()),
                "150px")
            .column(
                "tipoDocumento",
                "Tipo Documento",
                bean -> StringUtils.trimToEmpty(bean.getTipoDocumento()),
                "120px")
            .column(
                "documento",
                "Número de Documento",
                bean -> StringUtils.trimToEmpty(bean.getDocumento()),
                "130px")
            .column(
                "indicadorSiEsCliente",
                "¿Es Cliente?",
                bean -> StringUtils.trimToEmpty(bean.getIndicadorSiEsCliente()),
                "100px")
            .column(
                "departamento",
                "Departamento",
                bean -> StringUtils.trimToEmpty(bean.getDepartamento()),
                "120px")
            .column("correo", "Correo", bean -> StringUtils.trimToEmpty(bean.getCorreo()), "200px")
            .column(
                "telefono",
                "Teléfono",
                bean -> StringUtils.trimToEmpty(bean.getTelefono()),
                "120px")
            .column("placa", "Placa", bean -> StringUtils.trimToEmpty(bean.getPlaca()), "100px")
            .column("marca", "Marca", bean -> StringUtils.trimToEmpty(bean.getMarca()), "120px")
            .column("modelo", "Modelo", bean -> StringUtils.trimToEmpty(bean.getModelo()), "120px")
            .column(
                "anhoFabricacion",
                "Año de Fabricación",
                bean -> StringUtils.trimToEmpty(bean.getAnhoFabricacion()),
                "120px")
            .column(
                "valorComercial",
                "Valor Comercial",
                bean -> StringUtils.trimToEmpty(bean.getValorComercial()),
                "120px")
            .column(
                "indicadorConversionMotor",
                "Indicador Conversión Motor",
                bean -> StringUtils.trimToEmpty(bean.getIndicadorConversionMotorVehiculo()),
                "160px")
            .column(
                "planNombre",
                "Nombre del Plan",
                bean -> StringUtils.trimToEmpty(bean.getPlanNombre()),
                "150px")
            .column(
                "planCuotasMoneda",
                "Plan Cuotas Moneda",
                bean -> StringUtils.trimToEmpty(bean.getPlanCuotasMoneda()),
                "140px")
            .column(
                "planCuotasMonto",
                "Plan Cuotas Monto",
                bean -> StringUtils.trimToEmpty(bean.getPlanCuotasMonto()),
                "140px")
            .column(
                "primaMontoMoneda",
                "Prima Moneda",
                bean -> StringUtils.trimToEmpty(bean.getPrimaMontoMoneda()),
                "120px")
            .column(
                "primaMonto",
                "Prima Monto",
                bean -> StringUtils.trimToEmpty(bean.getPrimaMonto()),
                "120px")
            .column(
                "primaFrecuencia",
                "Prima Frecuencia",
                bean -> StringUtils.trimToEmpty(bean.getPrimaFrecuencia()),
                "130px")
            .column(
                "objetoCotizacion",
                "Objeto Cotización",
                bean -> obtenerNumeroCotizacion(bean),
                "150px")
            .column(
                "codigoOficina",
                "Código Oficina",
                bean -> StringUtils.trimToEmpty(bean.getCodigoOficina()),
                "120px")
            .column(
                "codigoUsuario",
                "Código Usuario",
                bean -> StringUtils.trimToEmpty(bean.getCodigoUsuario()),
                "120px")
            .column(
                "horarioContacto",
                "Horario de Contacto",
                bean -> StringUtils.trimToEmpty(bean.getHorarioContacto()),
                "140px")
            .column("canal", "Canal", bean -> StringUtils.trimToEmpty(bean.getCanal()), "100px")
            .column(
                "urlOrigen",
                "URL Origen Cotización",
                bean -> StringUtils.trimToEmpty(bean.getUrlOrigen()),
                "200px")
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
