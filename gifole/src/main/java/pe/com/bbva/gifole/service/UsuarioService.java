package pe.com.bbva.gifole.service;

import java.util.List;
import pe.com.bbva.gifole.domain.Usuario;

/** Interfaz de servicio para operaciones relacionadas con Usuario */
public interface UsuarioService {

  public void guardarUsuario(Usuario usuario);

  public void eliminarUsuario(Long id);

  List<Usuario> buscarUsuario(Usuario usuario);
  // List<Usuario> obtenerUsuario(UsuarioRequestDTO usuarioRequestDTO);
}
