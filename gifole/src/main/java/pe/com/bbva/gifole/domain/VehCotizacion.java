package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_COTIZACION")
public class VehCotizacion implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SeqGenV")
  @SequenceGenerator(name = "SeqGenV", sequenceName = "SQ_COTIZACION", allocationSize = 1)
  @Column(name = "ID")
  private Long id;

  @Column(name = "NUM_PLACA", length = 10)
  private String numPlaca;

  // bidirectional many-to-one association to VEH_MARCA
  @ManyToOne
  @JoinColumn(name = "MARCA")
  private VehMarca marca;

  // bi-directional many-to-one association to VEH_MODELO
  @ManyToOne
  @JoinColumn(name = "MODELO")
  private VehModelo modelo;

  // bi-directional many-to-one association to VEH_TIPO
  @ManyToOne
  @JoinColumn(name = "TIPO")
  private VehTipo tipo;

  @Column(name = "ANHO_FABRICACION", length = 4)
  private String anhoFabricacion;

  @Column(name = "CAMBIO_GAS", length = 1)
  private String cambioGas;

  @Column(name = "DIVISA", length = 3)
  private String divisa;

  @Column(name = "VALOR_COMERCIAL", precision = 15, scale = 2)
  private BigDecimal valorComercial;

  @Column(name = "LUGAR_REGISTRO", length = 20)
  private String lugarRegistro;

  @Column(name = "NOMBRE", length = 100)
  private String nombre;

  @Column(name = "TIPO_DOCUMENTO", length = 20)
  private String tipoDocumento;

  @Column(name = "DOCUMENTO", length = 20)
  private String documento;

  @Column(name = "CORREO", length = 80)
  private String correo;

  @Column(name = "TELEFONO", length = 20)
  private String telefono;

  @Column(name = "HORARIO", length = 20)
  private String horario;

  @Column(name = "PROCESADO", length = 1)
  private String procesado;

  @Column(name = "FECHA_REGISTRO")
  private Date fechaRegistro;

  @Column(name = "AUTORIZACION", length = 1)
  private String autorizacion;

  @Column(name = "NRO_COTIZACION", length = 10)
  private String nroCotizacion;

  @Column(name = "PLAN_ELEGIDO", length = 50)
  private String planElegido;

  @Column(name = "DIVISA_PLAN_ELEGIDO", length = 10)
  private String divisaPlanElegido;

  @Column(name = "MONTO_PLAN_ELEGIDO", length = 20)
  private String montoPlanElegido;

  @Column(name = "CANAL", length = 10)
  private String canal;

  @Column(name = "FECHA_MODIFICACION")
  private Date fechaModificacion;

  @Transient private List<VehCotizacionDetalle> cotizacionDetalles;

  @Transient private String codigoUso;

  @Transient private String codigoUbicacion;

  @Transient private String errorCalculo;
}
