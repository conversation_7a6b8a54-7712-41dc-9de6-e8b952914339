package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.Canal;

@Component
public class CanalAppMapper implements RowMapper<Canal> {

  @Override
  public Canal mapRow(ResultSet rs, int i) throws SQLException {

    Canal canal = new Canal();

    canal.setId(rs.getString("ID"));
    canal.setNombre(rs.getString("NOMBRE"));
    canal.setDescripcion(rs.getString("DESCRIPCION"));
    canal.setEstado(rs.getString("ESTADO"));
    canal.setFecha_registro(rs.getString("FECHA_REGISTRO"));
    canal.setCreador(rs.getString("CREADOR"));
    canal.setFecha_modificacion(rs.getString("FECHA_MODIFICACION"));
    canal.setEditor(rs.getString("EDITOR"));

    return canal;
  }
}
