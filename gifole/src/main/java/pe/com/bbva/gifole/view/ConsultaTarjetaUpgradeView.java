package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.TarjetaUpgradeBean;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Tarjeta Upgrade")
@Route(value = "reporte/tarjetas/consulta-tarjeta-upgrade", layout = MainLayout.class)
public class ConsultaTarjetaUpgradeView extends VerticalLayout {

  public ConsultaTarjetaUpgradeView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Tarjeta Upgrade");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    DataTable<TarjetaUpgradeBean> dataTable =
        DataTable.<TarjetaUpgradeBean>builder()
            .id("tabla-tarjeta-upgrade")
            .column(
                "codigoCentral",
                "Código Central",
                bean -> StringUtils.trimToEmpty(bean.getCodigoCentral()),
                "100px")
            .column(
                "tipoDocumento",
                "Tipo Documento",
                bean -> StringUtils.trimToEmpty(bean.getTipoDocumento()),
                "120px")
            .column(
                "numeroDocumento",
                "Número de Documento",
                bean -> StringUtils.trimToEmpty(bean.getNumeroDocumento()),
                "130px")
            .column(
                "nombreCompleto",
                "Nombre Completo",
                bean -> StringUtils.trimToEmpty(bean.getNombreCompleto()),
                "200px")
            .column(
                "telefono",
                "Teléfono",
                bean -> StringUtils.trimToEmpty(bean.getTelefono()),
                "120px")
            .column("correo", "Correo", bean -> StringUtils.trimToEmpty(bean.getCorreo()), "200px")
            .column(
                "nombreTarjetaOrigen",
                "Nombre Tarjeta Origen",
                bean -> StringUtils.trimToEmpty(bean.getNombreTarjetaOrigen()),
                "150px")
            .column(
                "tipoTarjetaOrigen",
                "Tipo Tarjeta Origen",
                bean -> StringUtils.trimToEmpty(bean.getTipoTarjetaOrigen()),
                "140px")
            .column(
                "nombreNuevaTarjeta",
                "Nombre Nueva Tarjeta",
                bean -> StringUtils.trimToEmpty(bean.getNombreNuevaTarjeta()),
                "150px")
            .column(
                "tipoNuevaTarjeta",
                "Tipo Nueva Tarjeta",
                bean -> StringUtils.trimToEmpty(bean.getTipoNuevaTarjeta()),
                "140px")
            .column("moneda", "Moneda", bean -> StringUtils.trimToEmpty(bean.getMoneda()), "100px")
            .column(
                "lineaCredito",
                "Línea de Crédito",
                bean -> StringUtils.trimToEmpty(bean.getLineaCredito()),
                "120px")
            .column("canal", "Canal", bean -> StringUtils.trimToEmpty(bean.getCanal()), "100px")
            .column("estado", "Estado", bean -> StringUtils.trimToEmpty(bean.getEstado()), "120px")
            .column(
                "creador", "Creador", bean -> StringUtils.trimToEmpty(bean.getCreador()), "120px")
            .column("fechaCreacion", "Fecha Creación", bean -> bean.getFechaCreacion(), "150px")
            .column("editor", "Editor", bean -> StringUtils.trimToEmpty(bean.getEditor()), "120px")
            .column(
                "fechaModificacion",
                "Fecha Modificación",
                bean -> bean.getFechaModificacion(),
                "150px")
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
