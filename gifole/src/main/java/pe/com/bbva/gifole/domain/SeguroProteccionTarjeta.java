package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "SEGURO_PROTECCION_TARJETA")
public class SeguroProteccionTarjeta implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SeqGenProtTarj")
  @SequenceGenerator(
      name = "SeqGenProtTarj",
      sequenceName = "SQ_SEGURO_PROTECCION_TARJETA",
      allocationSize = 1)
  @Column(name = "ID")
  private Long id;

  @Column(name = "NUMERO_CERTIFICADO", length = 30)
  private String numeroCertificado;

  @Column(name = "FECHA_EMISION")
  private Date fechaEmision;

  @Column(name = "FECHA_INICIO_SEGURO")
  private Date fechaInicioSeguro;

  @Column(name = "OFICINA_GESTORA", length = 10)
  private String oficinaGestora;

  @Column(name = "COD_VENDEDOR", length = 20)
  private String codigoVendedor;

  @Column(name = "NUMERO_CTATARJETA_CARGO", length = 30)
  private String numeroCtaTarjetaCargo;

  @Column(name = "NUMERO_TARJETA_ASEGURADA", length = 30)
  private String numeroTarjetaAsegurada;

  @Column(name = "NOMBRE", length = 50)
  private String nombre;

  @Column(name = "TIPO_DOCUMENTO", length = 20)
  private String tipoDocumento;

  @Column(name = "DOCUMENTO", length = 20)
  private String documento;

  @Column(name = "FECHA_NACIMIENTO")
  private Date fechaNacimiento;

  @Column(name = "SEXO", length = 20)
  private String sexo;

  @Column(name = "CORREO", length = 80)
  private String correo;

  @Column(name = "ESTADO_CIVIL", length = 20)
  private String estadoCivil;

  @Column(name = "TELEFONO", length = 20)
  private String telefono;

  @Column(name = "DIRECCION", length = 50)
  private String direccion;

  @Column(name = "DIRECCION_OPCIONAL", length = 50)
  private String direccionOpcional;

  @Column(name = "NOMBRE_TIT", length = 50)
  private String nombreTitular;

  @Column(name = "TIPO_DOCUMENTO_TIT", length = 20)
  private String tipoDocumentoTitular;

  @Column(name = "DOCUMENTO_TIT", length = 20)
  private String documentoTitular;

  @Column(name = "FECHA_NACIMIENTO_TIT")
  private Date fechaNacimientoTitular;

  @Column(name = "SEXO_TIT", length = 20)
  private String sexoTitular;

  @Column(name = "CORREO_TIT", length = 80)
  private String correoTitular;

  @Column(name = "ESTADO_CIVIL_TIT", length = 20)
  private String estadoCivilTitular;

  @Column(name = "TELEFONO_TIT", length = 20)
  private String telefonoTitular;

  @Column(name = "DIRECCION_TIT", length = 50)
  private String direccionTitular;

  @Column(name = "DIRECCION_OPCIONAL_TIT", length = 50)
  private String direccionOpcionalTitular;

  @Column(name = "MODALIDAD", length = 20)
  private String modalidad;

  @Column(name = "MONEDA", length = 10)
  private String moneda;

  @Column(name = "FORMA_PAGO", length = 20)
  private String formaPago;

  @Column(name = "PRIMA_SEGURO", length = 10)
  private String primaSeguro;

  @Column(name = "PROCESADO", length = 1)
  private String procesado;

  @Column(name = "FECHA_REGISTRO")
  private Date fechaRegistro;

  @Column(name = "LUGAR_FECHA_EMISION", length = 50)
  private String lugarFechaEmision;

  @Column(name = "CANAL", length = 8)
  private String canal;

  @Column(name = "NOMBRE_EMAIL", length = 80)
  private String nombreEmailPT;

  @Transient private String fechaOperacion;

  @Transient private String nombreEmail;

  @Transient private String fechaEmisionAlt;

  @Transient private String fechaInicioSeguroAlt;

  @Transient private String fechaNacimientoAlt;

  @Transient private String fechaNacimientoTitularAlt;

  @Transient private String tipoMedioPago;

  @Transient private String nombreCtaTarjetaCargo;
}
