package pe.com.bbva.gifole.components;

import com.vaadin.flow.component.Component;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.data.provider.ListDataProvider;
import pe.com.bbva.gifole.dto.GridColumn;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import com.vaadin.flow.function.ValueProvider;

/**
 * Componente DataTable reutilizable con paginación
 * 
 * @param <T> Tipo de objeto que contendrá la tabla
 */
@CssImport("./themes/bbva/components/datatable.css")
public class DataTable<T> extends VerticalLayout {

    private final Grid<T> grid;
    private final List<T> allItems;
    private final List<T> currentPageItems;
    private final int pageSize;

    // Componentes de paginación
    private Button previousButton;
    private Button nextButton;
    private Span pageInfo;
    private int currentPage = 0;
    private int totalPages = 0;

    // Configuración de columnas
    private final List<GridColumn> columns;

    /**
     * Constructor del DataTable
     * 
     * @param columns  Lista de columnas a mostrar
     * @param items    Lista de items a mostrar
     * @param pageSize Número de items por página
     */
    public DataTable(List<GridColumn> columns, List<T> items, int pageSize) {
        this.columns = columns;
        this.allItems = new ArrayList<>(items);
        this.currentPageItems = new ArrayList<>();
        this.pageSize = pageSize;
        this.grid = new Grid<>();

        setWidthFull();
        setHeight("auto"); // Ajustar altura automáticamente
        setSpacing(false);
        setPadding(false);
        setMargin(false);
        addClassName("bbva-datatable");

        configureGrid();
        createPaginationControls();
        updatePagination();

        add(grid, createPaginationLayout());
        setFlexGrow(0, grid); // No expandir el grid más allá de su contenido
    }

    /**
     * Configura el grid con las columnas especificadas
     */
    private void configureGrid() {
        grid.removeAllColumns();
        grid.setWidthFull();
        grid.setHeightByRows(true); // Ajustar altura automáticamente según el contenido
        grid.addClassName("bbva-grid");

        // Agregar columnas dinámicamente
        for (GridColumn column : columns) {
            Grid.Column<T> gridColumn = grid.addColumn(createValueProvider(column.key()))
                    .setHeader(column.header())
                    .setSortable(true);

            if (column.width() != null && !column.width().isEmpty()) {
                gridColumn.setWidth(column.width());
            }
        }

        // Configurar el data provider
        ListDataProvider<T> dataProvider = new ListDataProvider<>(currentPageItems);
        grid.setDataProvider(dataProvider);
    }

    /**
     * Crea un value provider para obtener el valor de una propiedad del objeto
     * Soporta navegación anidada usando notación de punto (ej:
     * "departamento.nombre")
     */
    private ValueProvider<T, String> createValueProvider(String propertyName) {
        return item -> {
            try {
                return getNestedPropertyValue(item, propertyName);
            } catch (Exception e) {
                return "N/A";
            }
        };
    }

    /**
     * Obtiene el valor de una propiedad anidada usando reflection
     * Soporta navegación como "departamento.nombre" o "direccion.ciudad"
     */
    private String getNestedPropertyValue(Object obj, String propertyPath) throws Exception {
        if (obj == null) {
            return "";
        }

        // Si no hay punto, es una propiedad simple
        if (!propertyPath.contains(".")) {
            return getSimplePropertyValue(obj, propertyPath);
        }

        // Dividir el path en partes
        String[] parts = propertyPath.split("\\.", 2);
        String currentProperty = parts[0];
        String remainingPath = parts[1];

        // Obtener el objeto anidado
        Object nestedObject = getSimplePropertyValue(obj, currentProperty);

        // Si el objeto anidado es null, retornar vacío
        if (nestedObject == null) {
            return "";
        }

        // Recursivamente obtener el valor del path restante
        return getNestedPropertyValue(nestedObject, remainingPath);
    }

    /**
     * Obtiene el valor de una propiedad simple (sin anidamiento)
     */
    private String getSimplePropertyValue(Object obj, String propertyName) throws Exception {
        // Intentar con getter estándar
        try {
            String getterName = "get" + capitalize(propertyName);
            Method getter = obj.getClass().getMethod(getterName);
            Object value = getter.invoke(obj);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            // Si falla, intentar con el nombre directo del método
            try {
                Method getter = obj.getClass().getMethod(propertyName);
                Object value = getter.invoke(obj);
                return value != null ? value.toString() : "";
            } catch (Exception ex) {
                // Si falla, intentar con getter booleano
                try {
                    String booleanGetterName = "is" + capitalize(propertyName);
                    Method getter = obj.getClass().getMethod(booleanGetterName);
                    Object value = getter.invoke(obj);
                    return value != null ? value.toString() : "";
                } catch (Exception exc) {
                    throw new Exception("No se pudo acceder a la propiedad: " + propertyName);
                }
            }
        }
    }

    /**
     * Capitaliza la primera letra de una cadena
     */
    private String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    /**
     * Crea los controles de paginación
     */
    private void createPaginationControls() {
        previousButton = new Button("Anterior", VaadinIcon.ANGLE_LEFT.create());
        previousButton.addClassName("bbva-pagination-button");
        previousButton.addClickListener(e -> previousPage());

        nextButton = new Button("Siguiente", VaadinIcon.ANGLE_RIGHT.create());
        nextButton.addClassName("bbva-pagination-button");
        nextButton.addClickListener(e -> nextPage());

        pageInfo = new Span();
        pageInfo.addClassName("pagination-info");
    }

    /**
     * Crea el layout de paginación
     */
    private Component createPaginationLayout() {
        HorizontalLayout paginationLayout = new HorizontalLayout();
        paginationLayout.addClassName("bbva-pagination");
        paginationLayout.setWidthFull();
        paginationLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
        paginationLayout.setAlignItems(FlexComponent.Alignment.CENTER);

        // Información de página a la izquierda
        pageInfo.addClassName("bbva-pagination-info");

        // Controles de navegación a la derecha
        HorizontalLayout controls = new HorizontalLayout();
        controls.addClassName("bbva-pagination-controls");
        controls.setSpacing(true);
        controls.add(previousButton, nextButton);

        paginationLayout.add(pageInfo, controls);

        return paginationLayout;
    }

    /**
     * Navega a la página anterior
     */
    private void previousPage() {
        if (currentPage > 0) {
            currentPage--;
            updatePagination();
        }
    }

    /**
     * Navega a la página siguiente
     */
    private void nextPage() {
        if (currentPage < totalPages - 1) {
            currentPage++;
            updatePagination();
        }
    }

    /**
     * Actualiza la paginación y los datos mostrados
     */
    private void updatePagination() {
        // Calcular páginas totales
        totalPages = (int) Math.ceil((double) allItems.size() / pageSize);

        // Calcular índices de inicio y fin
        int startIndex = currentPage * pageSize;
        int endIndex = Math.min(startIndex + pageSize, allItems.size());

        // Actualizar items de la página actual
        currentPageItems.clear();
        if (startIndex < allItems.size()) {
            currentPageItems.addAll(allItems.subList(startIndex, endIndex));
        }

        // Actualizar el grid
        grid.getDataProvider().refreshAll();

        // Actualizar controles de paginación
        previousButton.setEnabled(currentPage > 0);
        nextButton.setEnabled(currentPage < totalPages - 1);

        // Actualizar información de página
        if (totalPages > 0 && allItems.size() > 0) {
            int displayStart = startIndex + 1;
            int displayEnd = endIndex;
            pageInfo.setText(String.format("Mostrando %d - %d de %d registros (Página %d de %d)",
                    displayStart, displayEnd, allItems.size(), currentPage + 1, totalPages));
            pageInfo.setVisible(true);
        } else {
            pageInfo.setText("");
            pageInfo.setVisible(false);
        }

        // Manejar estado vacío del grid
        if (allItems.isEmpty()) {
            grid.getElement().setAttribute("empty", "true");
        } else {
            grid.getElement().removeAttribute("empty");
        }
    }

    /**
     * Actualiza los datos de la tabla
     */
    public void setItems(List<T> newItems) {
        allItems.clear();
        allItems.addAll(newItems);
        currentPage = 0;
        updatePagination();
    }

    /**
     * Obtiene el grid interno para configuraciones adicionales
     */
    public Grid<T> getGrid() {
        return grid;
    }

    /**
     * Obtiene la página actual
     */
    public int getCurrentPage() {
        return currentPage;
    }

    /**
     * Obtiene el número total de páginas
     */
    public int getTotalPages() {
        return totalPages;
    }

    /**
     * Obtiene el tamaño de página
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * Obtiene todos los items
     */
    public List<T> getAllItems() {
        return new ArrayList<>(allItems);
    }

    /**
     * Obtiene los items de la página actual
     */
    public List<T> getCurrentPageItems() {
        return new ArrayList<>(currentPageItems);
    }

    /**
     * Navega a una página específica
     */
    public void goToPage(int page) {
        if (page >= 0 && page < totalPages) {
            currentPage = page;
            updatePagination();
        }
    }

    /**
     * Refresca los datos de la tabla
     */
    public void refresh() {
        updatePagination();
    }
}