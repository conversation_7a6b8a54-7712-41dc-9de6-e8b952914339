package pe.com.bbva.gifole.components;

import com.vaadin.flow.component.Component;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.data.provider.ListDataProvider;
import com.vaadin.flow.function.ValueProvider;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import pe.com.bbva.gifole.dto.GridColumn;

/**
 * Componente DataTable reutilizable con paginación
 *
 * @param <T> Tipo de objeto que contendrá la tabla
 */
@CssImport("./themes/bbva/components/datatable.css")
public class DataTable<T> extends VerticalLayout {

  private final Grid<T> grid;
  private final List<T> allItems;
  private final List<T> currentPageItems;
  private final int pageSize;
  private String tableId;

  // Componentes de paginación
  private Button previousButton;
  private Button nextButton;
  private Span pageInfo;
  private int currentPage = 0;
  private int totalPages = 0;

  // Configuración de columnas
  private final List<GridColumn> columns;

  // Listeners de selección y acciones
  private final Set<ItemSelectedListener<T>> itemSelectedListeners = new HashSet<>();
  private final Set<ItemDeselectedListener<T>> itemDeselectedListeners = new HashSet<>();
  private final Set<ItemActionListener<T>> itemEditListeners = new HashSet<>();
  private final Set<ItemActionListener<T>> itemDeleteListeners = new HashSet<>();
  private T selectedItem = null;

  /** Interfaz para notificar cuando se selecciona un ítem */
  @FunctionalInterface
  public interface ItemSelectedListener<T> {
    void onItemSelected(T selectedItem);
  }

  /** Interfaz para notificar cuando se deselecciona un ítem */
  @FunctionalInterface
  public interface ItemDeselectedListener<T> {
    void onItemDeselected(T deselectedItem);
  }

  @FunctionalInterface
  public interface ItemActionListener<T> {
    void onAction(T item);
  }

  /**
   * Constructor del DataTable
   *
   * @param columns Lista de columnas a mostrar
   * @param items Lista de items a mostrar
   * @param pageSize Número de items por página
   */
  public DataTable(String id, List<GridColumn> columns, List<T> items, int pageSize) {
    this.tableId = id;
    this.columns = columns;
    this.allItems = new ArrayList<>(items);
    this.currentPageItems = new ArrayList<>();
    this.pageSize = pageSize;
    this.grid = new Grid<>();

    // Configurar el ID en el elemento raíz
    if (id != null && !id.isEmpty()) {
      this.setId(id);
      this.getElement().setAttribute("data-table-id", id);
    }

    setSizeFull();
    setSpacing(false);
    setPadding(false);
    addClassName("bbva-datatable");

    configureGrid();
    createPaginationControls();
    updatePagination();

    add(grid, createPaginationLayout());
    setFlexGrow(1, grid);
  }

  /** Agrega un listener para la acción de editar */
  public void onItemEdit(ItemActionListener<T> listener) {
    itemEditListeners.add(listener);
  }

  /** Elimina un listener de la acción de editar */
  public void removeItemEditListener(ItemActionListener<T> listener) {
    itemEditListeners.remove(listener);
  }

  /** Agrega un listener para la acción de eliminar */
  public void onItemDelete(ItemActionListener<T> listener) {
    itemDeleteListeners.add(listener);
  }

  /** Elimina un listener de la acción de eliminar */
  public void removeItemDeleteListener(ItemActionListener<T> listener) {
    itemDeleteListeners.remove(listener);
  }

  /** Notifica a los listeners sobre una acción de edición */
  private void notifyItemEdit(T item) {
    itemEditListeners.forEach(listener -> listener.onAction(item));
  }

  /** Notifica a los listeners sobre una acción de eliminación */
  private void notifyItemDelete(T item) {
    itemDeleteListeners.forEach(listener -> listener.onAction(item));
  }

  /** Configura el grid con las columnas especificadas */
  /** Agrega un listener para cuando se selecciona un ítem */
  public void onItemSelected(ItemSelectedListener<T> listener) {
    itemSelectedListeners.add(listener);
  }

  /** Remueve un listener de selección de ítem */
  public void removeItemSelectedListener(ItemSelectedListener<T> listener) {
    itemSelectedListeners.remove(listener);
  }

  /** Agrega un listener para cuando se deselecciona un ítem */
  public void onItemDeselected(ItemDeselectedListener<T> listener) {
    itemDeselectedListeners.add(listener);
  }

  /** Remueve un listener de deselección de ítem */
  public void removeItemDeselectedListener(ItemDeselectedListener<T> listener) {
    itemDeselectedListeners.remove(listener);
  }

  /** Notifica a los listeners sobre una selección */
  private void notifyItemSelected(T item) {
    itemSelectedListeners.forEach(listener -> listener.onItemSelected(item));
  }

  /** Notifica a los listeners sobre una deselección */
  private void notifyItemDeselected(T item) {
    itemDeselectedListeners.forEach(listener -> listener.onItemDeselected(item));
  }

  /** Obtiene el ítem actualmente seleccionado */
  public T getSelectedItem() {
    return selectedItem;
  }

  /** Obtiene el ID de la tabla */
  public String getTableId() {
    return tableId;
  }

  private void configureGrid() {
    grid.removeAllColumns();
    grid.setSizeFull();
    grid.addClassName("bbva-grid");

    // Configurar el manejador de selección
    grid.setSelectionMode(Grid.SelectionMode.SINGLE);
    grid.addSelectionListener(
        event -> {
          if (event.isFromClient()) {
            event
                .getFirstSelectedItem()
                .ifPresentOrElse(
                    item -> {
                      if (selectedItem != item) {
                        // Notificar deselección del ítem anterior
                        if (selectedItem != null) {
                          notifyItemDeselected(selectedItem);
                        }
                        // Actualizar ítem seleccionado y notificar selección
                        selectedItem = item;
                        notifyItemSelected(selectedItem);
                      }
                    },
                    () -> {
                      // Se deseleccionó el ítem actual
                      if (selectedItem != null) {
                        T deselectedItem = selectedItem;
                        selectedItem = null;
                        notifyItemDeselected(deselectedItem);
                      }
                    });
          }
        });

    // Agregar columnas dinámicamente
    for (GridColumn column : columns) {
      if (column.key().startsWith("action-")) {
        String actionType = column.key().substring("action-".length());
        Grid.Column<T> actionColumn =
            grid.addComponentColumn(
                item -> {
                  HorizontalLayout buttons = new HorizontalLayout();
                  buttons.setSpacing(true);
                  buttons.addClassName("action-buttons");

                  if (actionType.contains("edit") || actionType.contains("actions")) {
                    Button editBtn = new Button("", VaadinIcon.EDIT.create());
                    editBtn.addThemeVariants(ButtonVariant.LUMO_TERTIARY, ButtonVariant.LUMO_SMALL);
                    editBtn.getElement().setAttribute("title", "Editar");
                    editBtn.getElement().setAttribute("aria-label", "Editar");
                    editBtn.addClickListener(e -> notifyItemEdit(item));
                    buttons.add(editBtn);
                  }

                  if (actionType.contains("delete") || actionType.contains("actions")) {
                    Button deleteBtn = new Button("", VaadinIcon.TRASH.create());
                    deleteBtn.addThemeVariants(
                        ButtonVariant.LUMO_TERTIARY, ButtonVariant.LUMO_SMALL);
                    deleteBtn.getElement().setAttribute("title", "Eliminar");
                    deleteBtn.getElement().setAttribute("aria-label", "Eliminar");
                    deleteBtn.addClickListener(e -> notifyItemDelete(item));
                    buttons.add(deleteBtn);
                  }

                  return buttons;
                });

        actionColumn.setHeader(column.header());
        actionColumn.setAutoWidth(true);
        if (column.width() != null && !column.width().isEmpty()) {
          actionColumn.setWidth(column.width());
        }
        actionColumn.setFlexGrow(0);
      } else {
        Grid.Column<T> gridColumn =
            grid.addColumn(createValueProvider(column.key()))
                .setHeader(column.header())
                .setSortable(true);

        if (column.width() != null && !column.width().isEmpty()) {
          gridColumn.setWidth(column.width());
        }
      }
    }

    // Configurar el data provider
    ListDataProvider<T> dataProvider = new ListDataProvider<>(currentPageItems);
    grid.setDataProvider(dataProvider);
  }

  /**
   * Crea un value provider para obtener el valor de una propiedad del objeto Soporta navegación
   * anidada usando notación de punto (ej: "departamento.nombre")
   */
  private ValueProvider<T, String> createValueProvider(String propertyName) {
    return item -> {
      try {
        return getNestedPropertyValue(item, propertyName);
      } catch (Exception e) {
        return "N/A";
      }
    };
  }

  /**
   * Obtiene el valor de una propiedad anidada usando reflection Soporta navegación como
   * "departamento.nombre" o "direccion.ciudad"
   */
  private String getNestedPropertyValue(Object obj, String propertyPath) throws Exception {
    if (obj == null) {
      return "";
    }

    // Si no hay punto, es una propiedad simple
    if (!propertyPath.contains(".")) {
      return getSimplePropertyValue(obj, propertyPath);
    }

    // Dividir el path en partes
    String[] parts = propertyPath.split("\\.", 2);
    String currentProperty = parts[0];
    String remainingPath = parts[1];

    // Obtener el objeto anidado
    Object nestedObject = getSimplePropertyValue(obj, currentProperty);

    // Si el objeto anidado es null, retornar vacío
    if (nestedObject == null) {
      return "";
    }

    // Recursivamente obtener el valor del path restante
    return getNestedPropertyValue(nestedObject, remainingPath);
  }

  /** Obtiene el valor de una propiedad simple (sin anidamiento) */
  private String getSimplePropertyValue(Object obj, String propertyName) throws Exception {
    // Intentar con getter estándar
    try {
      String getterName = "get" + capitalize(propertyName);
      Method getter = obj.getClass().getMethod(getterName);
      Object value = getter.invoke(obj);
      return value != null ? value.toString() : "";
    } catch (Exception e) {
      // Si falla, intentar con el nombre directo del método
      try {
        Method getter = obj.getClass().getMethod(propertyName);
        Object value = getter.invoke(obj);
        return value != null ? value.toString() : "";
      } catch (Exception ex) {
        // Si falla, intentar con getter booleano
        try {
          String booleanGetterName = "is" + capitalize(propertyName);
          Method getter = obj.getClass().getMethod(booleanGetterName);
          Object value = getter.invoke(obj);
          return value != null ? value.toString() : "";
        } catch (Exception exc) {
          throw new Exception("No se pudo acceder a la propiedad: " + propertyName);
        }
      }
    }
  }

  /** Capitaliza la primera letra de una cadena */
  private String capitalize(String str) {
    if (str == null || str.isEmpty()) {
      return str;
    }
    return str.substring(0, 1).toUpperCase() + str.substring(1);
  }

  /** Crea los controles de paginación */
  private void createPaginationControls() {
    previousButton = new Button("Anterior", VaadinIcon.ANGLE_LEFT.create());
    previousButton.addClassName("bbva-pagination-button");
    previousButton.addClickListener(e -> previousPage());

    nextButton = new Button("Siguiente", VaadinIcon.ANGLE_RIGHT.create());
    nextButton.addClassName("bbva-pagination-button");
    nextButton.addClickListener(e -> nextPage());

    pageInfo = new Span();
    pageInfo.addClassName("pagination-info");
  }

  /** Crea el layout de paginación */
  private Component createPaginationLayout() {
    HorizontalLayout paginationLayout = new HorizontalLayout();
    paginationLayout.addClassName("bbva-pagination");
    paginationLayout.setWidthFull();
    paginationLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    paginationLayout.setAlignItems(FlexComponent.Alignment.CENTER);

    // Información de página a la izquierda
    pageInfo.addClassName("bbva-pagination-info");

    // Controles de navegación a la derecha
    HorizontalLayout controls = new HorizontalLayout();
    controls.addClassName("bbva-pagination-controls");
    controls.setSpacing(true);
    controls.add(previousButton, nextButton);

    paginationLayout.add(pageInfo, controls);

    return paginationLayout;
  }

  /** Navega a la página anterior */
  private void previousPage() {
    if (currentPage > 0) {
      currentPage--;
      updatePagination();
    }
  }

  /** Navega a la página siguiente */
  private void nextPage() {
    if (currentPage < totalPages - 1) {
      currentPage++;
      updatePagination();
    }
  }

  /** Actualiza la paginación y los datos mostrados */
  private void updatePagination() {
    // Calcular páginas totales
    totalPages = (int) Math.ceil((double) allItems.size() / pageSize);

    // Calcular índices de inicio y fin
    int startIndex = currentPage * pageSize;
    int endIndex = Math.min(startIndex + pageSize, allItems.size());

    // Actualizar items de la página actual
    currentPageItems.clear();
    if (startIndex < allItems.size()) {
      currentPageItems.addAll(allItems.subList(startIndex, endIndex));
    }

    // Actualizar el grid
    grid.getDataProvider().refreshAll();

    // Actualizar controles de paginación
    previousButton.setEnabled(currentPage > 0);
    nextButton.setEnabled(currentPage < totalPages - 1);

    // Actualizar información de página
    if (totalPages > 0 && allItems.size() > 0) {
      int displayStart = startIndex + 1;
      int displayEnd = endIndex;
      pageInfo.setText(
          String.format(
              "Mostrando %d - %d de %d registros (Página %d de %d)",
              displayStart, displayEnd, allItems.size(), currentPage + 1, totalPages));
      pageInfo.setVisible(true);
    } else {
      pageInfo.setText("");
      pageInfo.setVisible(false);
    }

    // Manejar estado vacío del grid
    if (allItems.isEmpty()) {
      grid.getElement().setAttribute("empty", "true");
    } else {
      grid.getElement().removeAttribute("empty");
    }
  }

  /** Actualiza los datos de la tabla */
  public void setItems(List<T> newItems) {
    allItems.clear();
    allItems.addAll(newItems);
    currentPage = 0;
    updatePagination();
  }

  /** Obtiene el grid interno para configuraciones adicionales */
  public Grid<T> getGrid() {
    return grid;
  }

  /** Obtiene la página actual */
  public int getCurrentPage() {
    return currentPage;
  }

  /** Obtiene el número total de páginas */
  public int getTotalPages() {
    return totalPages;
  }

  /** Obtiene el tamaño de página */
  public int getPageSize() {
    return pageSize;
  }

  /** Obtiene todos los items */
  public List<T> getAllItems() {
    return new ArrayList<>(allItems);
  }

  /** Obtiene los items de la página actual */
  public List<T> getCurrentPageItems() {
    return new ArrayList<>(currentPageItems);
  }

  /** Navega a una página específica */
  public void goToPage(int page) {
    if (page >= 0 && page < totalPages) {
      currentPage = page;
      updatePagination();
    }
  }

  /** Refresca los datos de la tabla */
  public void refresh() {
    updatePagination();
  }
}
