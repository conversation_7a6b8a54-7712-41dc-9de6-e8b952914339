package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;

public class ReprogramacionDeudaBean implements Serializable {

  private static final long serialVersionUID = -7159291926871341657L;

  private Long id;

  // Datos del cliente titular
  private String tipoDocumento;
  private String numeroDocumento;
  private String codigoCentral;
  private String nombres;
  private String apellidoPaterno;
  private String apellidoMaterno;
  private String nombresApellidos;
  private String correo;
  private String celular;

  private String codigoProducto;
  private String descripcionProducto;
  private String subProducto;
  private String numeroContrato;
  private String numeroTarjetaTitular;
  private String nroMesReprogramacion;
  private String nroCoutasProrroga;
  private String cuentaCargo;
  private String tieneDatazo;

  // Datos del registro de la solicitud
  private String canal;
  private String usuarioActualiza;
  private String fechaRegistro;
  private String fechaModificacion;
  private String fechaDesde;
  private String fechaHasta;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public String getNumeroDocumento() {
    return numeroDocumento;
  }

  public void setNumeroDocumento(String numeroDocumento) {
    this.numeroDocumento = numeroDocumento;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getNombres() {
    return nombres;
  }

  public void setNombres(String nombres) {
    this.nombres = nombres;
  }

  public String getApellidoPaterno() {
    return apellidoPaterno;
  }

  public void setApellidoPaterno(String apellidoPaterno) {
    this.apellidoPaterno = apellidoPaterno;
  }

  public String getApellidoMaterno() {
    return apellidoMaterno;
  }

  public void setApellidoMaterno(String apellidoMaterno) {
    this.apellidoMaterno = apellidoMaterno;
  }

  public String getNombresApellidos() {
    return nombresApellidos;
  }

  public void setNombresApellidos(String nombresApellidos) {
    this.nombresApellidos = nombresApellidos;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public String getCelular() {
    return celular;
  }

  public void setCelular(String celular) {
    this.celular = celular;
  }

  public String getCodigoProducto() {
    return codigoProducto;
  }

  public void setCodigoProducto(String codigoProducto) {
    this.codigoProducto = codigoProducto;
  }

  public String getDescripcionProducto() {
    return descripcionProducto;
  }

  public void setDescripcionProducto(String descripcionProducto) {
    this.descripcionProducto = descripcionProducto;
  }

  public String getSubProducto() {
    return subProducto;
  }

  public void setSubProducto(String subProducto) {
    this.subProducto = subProducto;
  }

  public String getNumeroContrato() {
    return numeroContrato;
  }

  public void setNumeroContrato(String numeroContrato) {
    this.numeroContrato = numeroContrato;
  }

  public String getNumeroTarjetaTitular() {
    return numeroTarjetaTitular;
  }

  public void setNumeroTarjetaTitular(String numeroTarjetaTitular) {
    this.numeroTarjetaTitular = numeroTarjetaTitular;
  }

  public String getNroMesReprogramacion() {
    return nroMesReprogramacion;
  }

  public void setNroMesReprogramacion(String nroMesReprogramacion) {
    this.nroMesReprogramacion = nroMesReprogramacion;
  }

  public String getNroCoutasProrroga() {
    return nroCoutasProrroga;
  }

  public void setNroCoutasProrroga(String nroCoutasProrroga) {
    this.nroCoutasProrroga = nroCoutasProrroga;
  }

  public String getCuentaCargo() {
    return cuentaCargo;
  }

  public void setCuentaCargo(String cuentaCargo) {
    this.cuentaCargo = cuentaCargo;
  }

  public String getTieneDatazo() {
    return tieneDatazo;
  }

  public void setTieneDatazo(String tieneDatazo) {
    this.tieneDatazo = tieneDatazo;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public String getUsuarioActualiza() {
    return usuarioActualiza;
  }

  public void setUsuarioActualiza(String usuarioActualiza) {
    this.usuarioActualiza = usuarioActualiza;
  }

  public String getFechaRegistro() {
    return fechaRegistro;
  }

  public void setFechaRegistro(String fechaRegistro) {
    this.fechaRegistro = fechaRegistro;
  }

  public String getFechaModificacion() {
    return fechaModificacion;
  }

  public void setFechaModificacion(String fechaModificacion) {
    this.fechaModificacion = fechaModificacion;
  }

  public String getFechaDesde() {
    return fechaDesde;
  }

  public void setFechaDesde(String fechaDesde) {
    this.fechaDesde = fechaDesde;
  }

  public String getFechaHasta() {
    return fechaHasta;
  }

  public void setFechaHasta(String fechaHasta) {
    this.fechaHasta = fechaHasta;
  }
}
