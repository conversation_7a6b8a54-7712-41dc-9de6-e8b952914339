package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.domain.SisItem;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Mantenimiento de Items del Sistema")
@Route(value = "sis-item-mantenimiento", layout = MainLayout.class)
public class SisItemMantenimientoView extends VerticalLayout {

  public SisItemMantenimientoView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Mantenimiento de Items del Sistema");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    DataTable<SisItem> dataTable =
        DataTable.<SisItem>builder()
            .id("tabla-items")
            .column(
                "descripcion",
                "Descripción",
                item -> StringUtils.trimToEmpty(item.getDescripcion()),
                "300px")
            .column(
                "opcion",
                "Opción",
                item ->
                    item.getItemId() != null
                        ? StringUtils.trimToEmpty(item.getItemId().getDescripcion())
                        : "Sin opción",
                "300px")
            .column(
                "estado",
                "Estado",
                item -> "1".equals(item.getEstado()) ? "Activo" : "Inactivo",
                "100px")
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
