package pe.com.bbva.gifole.common.bean;

public class TCReferidoSinTablonBean {

  private String nombreCompletoReferidor;

  private String numeroDocumentoReferidor;

  private String marcaEmpleadoReferidor;

  private String numeroDocumentoReferido;

  private String codigoCentralReferido;

  private String celularReferido;

  private String nombreCompletoReferido;

  private String nbrTarjetaReferido;

  private String tipoTarjetaReferido;

  private String lineaReferido;

  private String tasaReferido;

  private String correoReferido;

  private String fechaLeadGenerado;

  private Long id;

  public String getNombreCompletoReferidor() {
    return nombreCompletoReferidor;
  }

  public void setNombreCompletoReferidor(String nombreCompletoReferidor) {
    this.nombreCompletoReferidor = nombreCompletoReferidor;
  }

  public String getNumeroDocumentoReferidor() {
    return numeroDocumentoReferidor;
  }

  public void setNumeroDocumentoReferidor(String numeroDocumentoReferidor) {
    this.numeroDocumentoReferidor = numeroDocumentoReferidor;
  }

  public String getMarcaEmpleadoReferidor() {
    return marcaEmpleadoReferidor;
  }

  public void setMarcaEmpleadoReferidor(String marcaEmpleadoReferidor) {
    this.marcaEmpleadoReferidor = marcaEmpleadoReferidor;
  }

  public String getNumeroDocumentoReferido() {
    return numeroDocumentoReferido;
  }

  public void setNumeroDocumentoReferido(String numeroDocumentoReferido) {
    this.numeroDocumentoReferido = numeroDocumentoReferido;
  }

  public String getCodigoCentralReferido() {
    return codigoCentralReferido;
  }

  public void setCodigoCentralReferido(String codigoCentralReferido) {
    this.codigoCentralReferido = codigoCentralReferido;
  }

  public String getCelularReferido() {
    return celularReferido;
  }

  public void setCelularReferido(String celularReferido) {
    this.celularReferido = celularReferido;
  }

  public String getNombreCompletoReferido() {
    return nombreCompletoReferido;
  }

  public void setNombreCompletoReferido(String nombreCompletoReferido) {
    this.nombreCompletoReferido = nombreCompletoReferido;
  }

  public String getNbrTarjetaReferido() {
    return nbrTarjetaReferido;
  }

  public void setNbrTarjetaReferido(String nbrTarjetaReferido) {
    this.nbrTarjetaReferido = nbrTarjetaReferido;
  }

  public String getTipoTarjetaReferido() {
    return tipoTarjetaReferido;
  }

  public void setTipoTarjetaReferido(String tipoTarjetaReferido) {
    this.tipoTarjetaReferido = tipoTarjetaReferido;
  }

  public String getLineaReferido() {
    return lineaReferido;
  }

  public void setLineaReferido(String lineaReferido) {
    this.lineaReferido = lineaReferido;
  }

  public String getTasaReferido() {
    return tasaReferido;
  }

  public void setTasaReferido(String tasaReferido) {
    this.tasaReferido = tasaReferido;
  }

  public String getCorreoReferido() {
    return correoReferido;
  }

  public void setCorreoReferido(String correoReferido) {
    this.correoReferido = correoReferido;
  }

  public String getFechaLeadGenerado() {
    return fechaLeadGenerado;
  }

  public void setFechaLeadGenerado(String fechaLeadGenerado) {
    this.fechaLeadGenerado = fechaLeadGenerado;
  }

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }
}
