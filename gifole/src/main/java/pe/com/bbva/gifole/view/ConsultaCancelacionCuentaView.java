package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.CancelacionProductoDetalleBean; // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.util.GifoleUtil; // Asumo que existe, ajusta según tu proyecto
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Cancelación Cuentas")
@Route(value = "reporte/cancelaciones/cuentas", layout = MainLayout.class)
public class ConsultaCancelacionCuentaView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<CancelacionProductoDetalleBean> allData = new ArrayList<>();
    private DataTable<CancelacionProductoDetalleBean> dataTable;

    public ConsultaCancelacionCuentaView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Cancelación Cuentas");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<CancelacionProductoDetalleBean>builder()
                .id("tabla-cancelacion-cuentas") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaCancelacionCuentaUI)
                .column("codigoCentral", "Código Central",
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getCodigoCentral() : ""), "100px")
                .column("nombresApellidos", "Nombre Cliente",
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getNombreCompletoCliente() : ""), "300px")
                .column("tipoDocumento", "Tipo de Documento",
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getTipoDocumento() : ""), "60px")
                .column("numeroDocumento", "Número de Documento",
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getNroDocumento() : ""), "80px")
                .column("fechaHoraRegistro", "Fecha Registro",
                    bean -> bean.getCreacion() != null ? FORMATO_FECHA.format(bean.getCreacion()) : "", "170px")
                .column("subProducto", "Sub Producto",
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getSubProducto() : ""), "200px")
                .column("nroContrato", "Número de Contrato",
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        GifoleUtil.obtenerNumeroCuentaFormateado(
                            bean.getCancelacionProductoBean().getNroContrato()) : ""), "170px") // Asegúrate de tener GifoleUtil
                .column("correo", "Correo",
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getCorreoCliente() : ""), "200px")
                .column("celularCliente", "Celular",
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getCelularCliente() : ""), "120px")
                .column("estado", "Estado Solicitud",
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getEstado() : ""), "110px")
                .column("motivoRechazo", "Motivo de Rechazo",
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getMotivoRechazo() : ""), "300px")
                .column("otroMotivoRechazo", "Detalle Otros",
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getOtroMotivoRechazo() : ""), "150px")
                .column("fechaHoraModificacion", "Fecha Modificacion",
                    bean -> bean.getEdicion() != null ? FORMATO_FECHA.format(bean.getEdicion()) : "", "170px")
                .column("registroModificacion", "Registro Externo",
                    bean -> StringUtils.trimToEmpty(bean.getEditor()), "80px") // Del detalle bean
                .column("canal", "Canal",
                    bean -> StringUtils.trimToEmpty(
                        bean.getCancelacionProductoBean() != null ?
                        bean.getCancelacionProductoBean().getCanal() : ""), "70px")
                // Datos (inicialmente vacío)
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}