package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.TCEstado;
import pe.com.bbva.gifole.domain.TCMotivo;
import pe.com.bbva.gifole.domain.TCToc;
import pe.com.bbva.gifole.domain.TCTocDetEstado;

@Component
public class CreditoMapper implements RowMapper<TCTocDetEstado> {

  @Override
  public TCTocDetEstado mapRow(ResultSet rs, int i) throws SQLException {

    TCTocDetEstado credito = new TCTocDetEstado();
    credito.setTcToc(new TCToc());
    credito.setTcEstado(new TCEstado());
    credito.setTcMotivo(new TCMotivo());
    credito.getTcToc().setCodigoCentral(rs.getString("COD_CENTRAL"));
    credito.getTcToc().setNombres(rs.getString("TITULAR"));
    credito.getTcToc().setFechaRegistro(rs.getTimestamp("FECHA_REGISTRO"));
    credito.getTcToc().setTarjeta(rs.getString("TARJETA"));
    credito.getTcToc().setTipoTarjeta(rs.getString("TIPO_TARJETA"));
    credito.getTcToc().setNroTarjeta(rs.getString("NRO_TARJETA"));
    credito.getTcToc().setProgramaBenef(rs.getString("PROGRAMA_BENEF"));
    credito.getTcToc().setFechaPago(rs.getString("FECHA_PAGO"));
    credito.getTcToc().setCuentaCargo(rs.getString("CUENTA_CARGO"));
    credito.getTcToc().setEnvioEc(rs.getString("ENVIO_EC"));
    credito.getTcToc().setDireccion(rs.getString("DIRECCION"));
    credito.getTcToc().setTelefono(rs.getString("TELEFONO"));
    credito.getTcToc().setNroContrato(rs.getString("NRO_CONTRATO"));
    credito.getTcToc().setCodreservaMoi(rs.getString("COD_RESERVA_MOI"));
    credito.getTcToc().setNroTarjeta(rs.getString("NRO_TARJETA"));
    credito.getTcToc().setOfertaDatazo(rs.getString("OFERTA_DATAZO"));
    credito.getTcToc().setDisposicionEfectivo(rs.getString("DISPOSICION_EFECTIVO"));
    TCEstado estado = new TCEstado();
    estado.setNombre(rs.getString("ESTADO"));
    credito.setTcEstado(estado);
    credito.getTcMotivo().setNombre(rs.getString("MOTIVO"));
    credito.setObservacion(rs.getString("OBSERVACION"));
    credito.setCreacion(rs.getTimestamp("CREACION"));
    credito.getTcToc().setValoracion(rs.getInt("VALORACION"));
    credito.getTcToc().setLinea(rs.getString("LINEA_CREDITO"));
    credito.getTcToc().setCorreo(rs.getString("CORREO"));
    credito.getTcToc().setId(rs.getLong("ID"));
    credito.getTcToc().setCombo(rs.getString("COMBO"));
    credito.getTcToc().setCanal(rs.getString("CANAL"));
    credito.getTcToc().setOficinaSolicitante(rs.getString("OFICINA_SOLICITANTE"));
    credito.getTcToc().setEjecutivo(rs.getString("EJECUTIVO"));
    credito.getTcToc().setOficinaRecojo(rs.getString("OFICINA_RECOJO"));
    credito.getTcToc().setCorreoEjecutivo(rs.getString("CORREO_EJECUTIVO"));
    credito.getTcToc().setPasajeroFrecuente(rs.getString("PASAJERO_FRECUENTE"));
    credito.getTcToc().setTea(rs.getString("TEA"));
    credito.getTcToc().setTipoDocumento(rs.getString("TIPO_DOCUMENTO"));
    credito.getTcToc().setNroDocumento(rs.getString("NRO_DOCUMENTO"));
    credito.getTcToc().setFlagNotificaciones(rs.getString("FLAG_NOTIFICACIONES"));
    credito.getTcToc().setTipoGarantia(rs.getString("TIPO_GARANTIA"));

    return credito;
  }
}
