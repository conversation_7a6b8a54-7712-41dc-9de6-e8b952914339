package pe.com.bbva.gifole.common.bean;

public class FondoMutuoDetalleBean {

  private Long id;
  private String nroCuenta;
  private String tipoFondo;
  private String fondo;
  private String monto;
  private String tipoDivisa;
  private String idContratoFondo;
  private String idClaseFondo;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getNroCuenta() {
    return nroCuenta;
  }

  public void setNroCuenta(String nroCuenta) {
    this.nroCuenta = nroCuenta;
  }

  public String getTipoFondo() {
    return tipoFondo;
  }

  public void setTipoFondo(String tipoFondo) {
    this.tipoFondo = tipoFondo;
  }

  public String getFondo() {
    return fondo;
  }

  public void setFondo(String fondo) {
    this.fondo = fondo;
  }

  public String getMonto() {
    return monto;
  }

  public void setMonto(String monto) {
    this.monto = monto;
  }

  public String getTipoDivisa() {
    return tipoDivisa;
  }

  public void setTipoDivisa(String tipoDivisa) {
    this.tipoDivisa = tipoDivisa;
  }

  public String getIdContratoFondo() {
    return idContratoFondo;
  }

  public void setIdContratoFondo(String idContratoFondo) {
    this.idContratoFondo = idContratoFondo;
  }

  public String getIdClaseFondo() {
    return idClaseFondo;
  }

  public void setIdClaseFondo(String idClaseFondo) {
    this.idClaseFondo = idClaseFondo;
  }
}
