package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.TCReferidoReevaluacionMotorBean;

@SuppressWarnings("rawtypes")
@Component
public class TCReferidoReevaluacionMotorMapper implements RowMapper {

  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    TCReferidoReevaluacionMotorBean param = new TCReferidoReevaluacionMotorBean();

    String tipoDoc = "";
    if (rs.getString("TIPODOCUMENTOREFERIDO") != null) {
      if (rs.getString("TIPODOCUMENTOREFERIDO").equals("DNI")) {
        tipoDoc = "L";
      }
    }

    param.setIdReferido(rs.getLong("IDREFERIDO"));
    param.setNombreCompletoReferidor(
        rs.getString("NOMBREREFERIDOR") + " " + rs.getString("APELLIDOPATERNOREFERIDOR"));
    param.setNumeroDocumentoReferidor(rs.getString("NRODOCUMENTOREFERIDOR"));
    param.setNumeroDocumentoReferido(rs.getString("NRODOCUMENTOREFERIDO"));
    param.setTipoDocumentoReferido(tipoDoc);
    param.setCodigoCentralReferido(rs.getString("CODIGOCENTRALREFERIDO"));
    param.setNombreCompletoReferido(
        rs.getString("NOMBREREFERIDO") + " " + rs.getString("APELLIDOPATERNOREFERIDO"));
    param.setNbrTarjetaReferido(rs.getString("NOMBRETARJETAREFERIDO"));
    param.setTipoTarjetaReferido(rs.getString("TIPOTARJETAREFERIDO"));
    param.setLineaReferido(rs.getString("LINEAREFERIDO"));
    param.setTasaReferido(rs.getString("TASAREFERIDO"));
    param.setHistoricoEvaluacion(rs.getString("IDEVALUACION"));
    param.setCorreoReferido(rs.getString("CORREOREFERIDO"));

    return param;
  }
}
