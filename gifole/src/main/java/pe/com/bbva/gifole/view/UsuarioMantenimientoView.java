package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;

@PageTitle("Mantenimiento de Usuarios")
@Route(value = "usuario-mantenimiento", layout = MainLayout.class)
public class UsuarioMantenimientoView extends VerticalLayout {

  // Filtros
  private final TextField filtroRegistro = new TextField("Registro");
  private final TextField filtroNombre = new TextField("Nombre");
  private final TextField filtroPaterno = new TextField("Apellido Paterno");
  private final TextField filtroMaterno = new TextField("Apellido Materno");

  public UsuarioMantenimientoView() {
    addClassName("app-main");
    setSizeFull();
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Filtros de Mantenimiento de Usuarios");
    title.addClassName("bbva-page-title");

    // Panel de filtros
    HorizontalLayout filtersLayout = createFiltersLayout();

    mainLayout.add(title, filtersLayout);
    add(mainLayout);
  }

  private HorizontalLayout createFiltersLayout() {
    HorizontalLayout filtersLayout = new HorizontalLayout();
    filtersLayout.addClassName("bbva-filters-card");
    filtersLayout.setWidthFull();
    filtersLayout.setAlignItems(Alignment.END);
    filtersLayout.setSpacing(true);
    filtersLayout.setPadding(true);

    filtroRegistro.addClassName("bbva-input-floating");
    filtroRegistro.setWidth("150px");

    filtroNombre.addClassName("bbva-input-floating");
    filtroNombre.setWidth("200px");

    filtroPaterno.addClassName("bbva-input-floating");
    filtroPaterno.setWidth("200px");

    filtroMaterno.addClassName("bbva-input-floating");
    filtroMaterno.setWidth("200px");

    filtersLayout.add(filtroRegistro, filtroNombre, filtroPaterno, filtroMaterno);
    return filtersLayout;
  }
}
