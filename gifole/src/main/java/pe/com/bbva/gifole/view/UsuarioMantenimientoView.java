package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import java.util.List;
import pe.com.bbva.gifole.domain.Usuario;

@PageTitle("Mantenimiento de Usuarios")
@Route(value = "usuario-mantenimiento", layout = MainLayout.class)
public class UsuarioMantenimientoView extends VerticalLayout {

  // Componentes de la interfaz
  private final Grid<Usuario> grid = new Grid<>(Usuario.class, false);
  private final List<Usuario> usuariosList = new ArrayList<>();
  private final List<Usuario> allUsuarios = new ArrayList<>();

  // Componentes del drawer
  private VerticalLayout drawerPanel;

  // Filtros
  private TextField filtroRegistro = new TextField("Registro");
  private TextField filtroNombre = new TextField("Nombre");
  private TextField filtroPaterno = new TextField("Apellido Paterno");
  private TextField filtroMaterno = new TextField("Apellido Materno");

  // Formulario
  private TextField txtRegistro = new TextField("Registro");
  private TextField txtNombre = new TextField("Nombre");
  private TextField txtPaterno = new TextField("Apellido Paterno");
  private TextField txtMaterno = new TextField("Apellido Materno");
  private Button btnGuardar = new Button("Guardar");
  private Button btnCancelar = new Button("Cancelar");
  private Button btnNuevo = new Button("Nuevo Usuario", VaadinIcon.PLUS.create());
  private Button btnCerrarDrawer = new Button(VaadinIcon.CLOSE.create());

  public UsuarioMantenimientoView() {
    addClassName("app-main");
    addClassName("usuario-mantenimiento-view");
    setSizeFull();
    setPadding(true);
    setSpacing(true);
    getStyle().set("position", "relative");

    // Configurar la cuadrícula
    configureGrid();

    // Configurar filtros
    configureFilters();

    // Configurar formulario
    configureForm();

    // Crear el drawer
    createDrawer();

    // Layout principal
    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setPadding(false);
    mainLayout.setSpacing(false);

    // Header con título y botón nuevo
    HorizontalLayout headerLayout = createHeaderLayout();

    // Panel de filtros
    HorizontalLayout filtersLayout = createFiltersLayout();

    // Agregar componentes al layout principal
    mainLayout.add(headerLayout, filtersLayout, grid);
    mainLayout.setFlexGrow(1, grid);

    add(mainLayout);

    // Cargar datos de ejemplo
    loadSampleData();
    updateGrid();
  }

  private void configureGrid() {
    grid.addClassName("bbva-grid");
    grid.setSizeFull();

    // Configurar columnas según los campos solicitados
    grid.addColumn(Usuario::getRegistro).setHeader("Registro").setSortable(true).setWidth("150px");

    grid.addColumn(Usuario::getNombre).setHeader("Nombre").setSortable(true).setFlexGrow(1);

    grid.addColumn(Usuario::getPaterno)
        .setHeader("Apellido Paterno")
        .setSortable(true)
        .setFlexGrow(1);

    grid.addColumn(Usuario::getMaterno)
        .setHeader("Apellido Materno")
        .setSortable(true)
        .setFlexGrow(1);

    // Configurar selección
    grid.setSelectionMode(Grid.SelectionMode.SINGLE);
    grid.addSelectionListener(
        event -> {
          event.getFirstSelectedItem().ifPresent(this::editUsuario);
        });
  }

  private void configureFilters() {
    // Configurar filtros con clases CSS globales
    filtroRegistro.addClassName("bbva-input-floating");
    filtroRegistro.addValueChangeListener(e -> applyFilters());

    filtroNombre.addClassName("bbva-input-floating");
    filtroNombre.addValueChangeListener(e -> applyFilters());

    filtroPaterno.addClassName("bbva-input-floating");
    filtroPaterno.addValueChangeListener(e -> applyFilters());

    filtroMaterno.addClassName("bbva-input-floating");
    filtroMaterno.addValueChangeListener(e -> applyFilters());
  }

  private void configureForm() {
    // Configurar campos del formulario con clases CSS globales
    txtRegistro.addClassName("bbva-form-field");
    txtRegistro.setMaxLength(7);
    txtRegistro.setRequired(true);

    txtNombre.addClassName("bbva-form-field");
    txtNombre.setMaxLength(50);
    txtNombre.setRequired(true);

    txtPaterno.addClassName("bbva-form-field");
    txtPaterno.setMaxLength(50);
    txtPaterno.setRequired(true);

    txtMaterno.addClassName("bbva-form-field");
    txtMaterno.setMaxLength(50);

    // Configurar botones
    btnGuardar.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnGuardar.addClassName("bbva-button");
    btnGuardar.addClickListener(e -> saveUsuario());

    btnCancelar.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
    btnCancelar.addClassName("bbva-button");
    btnCancelar.addClickListener(e -> cancelEdit());

    btnNuevo.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnNuevo.addClassName("bbva-button");
    btnNuevo.addClickListener(e -> newUsuario());

    btnCerrarDrawer.addThemeVariants(ButtonVariant.LUMO_TERTIARY_INLINE);
    btnCerrarDrawer.addClickListener(e -> closeDrawer());
  }

  private HorizontalLayout createHeaderLayout() {
    HorizontalLayout headerLayout = new HorizontalLayout();
    headerLayout.addClassName("bbva-page-header");
    headerLayout.setWidthFull();
    headerLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    headerLayout.setAlignItems(FlexComponent.Alignment.CENTER);

    H2 title = new H2("Mantenimiento de Usuarios");
    title.addClassName("bbva-page-title");

    headerLayout.add(title, btnNuevo);
    return headerLayout;
  }

  private HorizontalLayout createFiltersLayout() {
    HorizontalLayout filtersLayout = new HorizontalLayout();
    filtersLayout.addClassName("bbva-filters-card");
    filtersLayout.setWidthFull();
    filtersLayout.setSpacing(true);
    filtersLayout.setAlignItems(FlexComponent.Alignment.END);

    // Configurar anchos de filtros
    filtroRegistro.setWidth("150px");
    filtroNombre.setWidth("200px");
    filtroPaterno.setWidth("200px");
    filtroMaterno.setWidth("200px");

    filtersLayout.add(filtroRegistro, filtroNombre, filtroPaterno, filtroMaterno);

    return filtersLayout;
  }

  private void createDrawer() {
    drawerPanel = new VerticalLayout();
    drawerPanel.addClassName("bbva-drawer-panel");
    drawerPanel.setWidth("400px");
    drawerPanel.setHeight("100%");
    drawerPanel.setPadding(true);
    drawerPanel.setSpacing(true);
    drawerPanel.getStyle().set("position", "fixed");
    drawerPanel.getStyle().set("top", "0");
    drawerPanel.getStyle().set("right", "-400px");
    drawerPanel.getStyle().set("z-index", "1000");
    drawerPanel.getStyle().set("background", "white");
    drawerPanel.getStyle().set("box-shadow", "-2px 0 10px rgba(0,0,0,0.1)");
    drawerPanel.getStyle().set("transition", "right 0.3s ease");

    // Header del drawer
    HorizontalLayout drawerHeader = new HorizontalLayout();
    drawerHeader.setWidthFull();
    drawerHeader.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    drawerHeader.setAlignItems(FlexComponent.Alignment.CENTER);

    H2 drawerTitle = new H2("Usuario");
    drawerTitle.addClassName("bbva-page-title");

    drawerHeader.add(drawerTitle, btnCerrarDrawer);

    // Formulario
    VerticalLayout formLayout = new VerticalLayout();
    formLayout.setPadding(false);
    formLayout.setSpacing(true);
    formLayout.add(txtRegistro, txtNombre, txtPaterno, txtMaterno);

    // Botones del formulario
    HorizontalLayout buttonsLayout = new HorizontalLayout();
    buttonsLayout.setWidthFull();
    buttonsLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.END);
    buttonsLayout.add(btnCancelar, btnGuardar);

    drawerPanel.add(drawerHeader, formLayout, buttonsLayout);
    drawerPanel.setFlexGrow(1, formLayout);

    add(drawerPanel);
  }

  private void applyFilters() {
    usuariosList.clear();

    String registroFilter = filtroRegistro.getValue();
    String nombreFilter = filtroNombre.getValue();
    String paternoFilter = filtroPaterno.getValue();
    String maternoFilter = filtroMaterno.getValue();

    for (Usuario usuario : allUsuarios) {
      boolean matches = true;

      // Filtro por registro
      if (registroFilter != null && !registroFilter.trim().isEmpty()) {
        if (usuario.getRegistro() == null
            || !usuario.getRegistro().toLowerCase().contains(registroFilter.toLowerCase())) {
          matches = false;
        }
      }

      // Filtro por nombre
      if (nombreFilter != null && !nombreFilter.trim().isEmpty()) {
        if (usuario.getNombre() == null
            || !usuario.getNombre().toLowerCase().contains(nombreFilter.toLowerCase())) {
          matches = false;
        }
      }

      // Filtro por apellido paterno
      if (paternoFilter != null && !paternoFilter.trim().isEmpty()) {
        if (usuario.getPaterno() == null
            || !usuario.getPaterno().toLowerCase().contains(paternoFilter.toLowerCase())) {
          matches = false;
        }
      }

      // Filtro por apellido materno
      if (maternoFilter != null && !maternoFilter.trim().isEmpty()) {
        if (usuario.getMaterno() == null
            || !usuario.getMaterno().toLowerCase().contains(maternoFilter.toLowerCase())) {
          matches = false;
        }
      }

      if (matches) {
        usuariosList.add(usuario);
      }
    }

    updateGrid();
  }

  private void updateGrid() {
    grid.setItems(usuariosList);
  }

  private void newUsuario() {
    clearForm();
    openDrawer();
  }

  private void editUsuario(Usuario usuario) {
    loadUsuarioToForm(usuario);
    openDrawer();
  }

  private void loadUsuarioToForm(Usuario usuario) {
    txtRegistro.setValue(usuario.getRegistro() != null ? usuario.getRegistro() : "");
    txtNombre.setValue(usuario.getNombre() != null ? usuario.getNombre() : "");
    txtPaterno.setValue(usuario.getPaterno() != null ? usuario.getPaterno() : "");
    txtMaterno.setValue(usuario.getMaterno() != null ? usuario.getMaterno() : "");
  }

  private void clearForm() {
    txtRegistro.clear();
    txtNombre.clear();
    txtPaterno.clear();
    txtMaterno.clear();
  }

  private void saveUsuario() {
    if (validateForm()) {
      Usuario usuario = new Usuario();

      // Mapear campos del formulario al objeto
      usuario.setRegistro(txtRegistro.getValue());
      usuario.setNombre(txtNombre.getValue());
      usuario.setPaterno(txtPaterno.getValue());
      usuario.setMaterno(txtMaterno.getValue());

      // Agregar a la lista (sin conectar con service)
      allUsuarios.add(usuario);
      applyFilters();
      closeDrawer();

      Notification.show("Usuario guardado exitosamente")
          .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
    }
  }

  private boolean validateForm() {
    if (txtRegistro.getValue() == null || txtRegistro.getValue().trim().isEmpty()) {
      Notification.show("El registro es requerido")
          .addThemeVariants(NotificationVariant.LUMO_ERROR);
      return false;
    }

    if (txtNombre.getValue() == null || txtNombre.getValue().trim().isEmpty()) {
      Notification.show("El nombre es requerido").addThemeVariants(NotificationVariant.LUMO_ERROR);
      return false;
    }

    if (txtPaterno.getValue() == null || txtPaterno.getValue().trim().isEmpty()) {
      Notification.show("El apellido paterno es requerido")
          .addThemeVariants(NotificationVariant.LUMO_ERROR);
      return false;
    }

    return true;
  }

  private void cancelEdit() {
    closeDrawer();
  }

  private void openDrawer() {
    drawerPanel.getStyle().set("right", "0");
  }

  private void closeDrawer() {
    drawerPanel.getStyle().set("right", "-400px");
    clearForm();
    grid.deselectAll();
  }

  private void loadSampleData() {
    // Datos de ejemplo sin conectar con service
    allUsuarios.add(createUsuarioItem("USR0001", "Juan", "Pérez", "García"));
    allUsuarios.add(createUsuarioItem("USR0002", "María", "López", "Martínez"));
    allUsuarios.add(createUsuarioItem("USR0003", "Carlos", "Rodríguez", "Sánchez"));
    allUsuarios.add(createUsuarioItem("USR0004", "Ana", "González", "Fernández"));
    allUsuarios.add(createUsuarioItem("USR0005", "Luis", "Martín", "Jiménez"));
    allUsuarios.add(createUsuarioItem("USR0006", "Carmen", "Ruiz", "Moreno"));
    allUsuarios.add(createUsuarioItem("USR0007", "Pedro", "Díaz", "Muñoz"));
    allUsuarios.add(createUsuarioItem("USR0008", "Laura", "Torres", "Álvarez"));
    allUsuarios.add(createUsuarioItem("USR0009", "Miguel", "Herrera", "Castro"));
    allUsuarios.add(createUsuarioItem("USR0010", "Isabel", "Vargas", "Ramos"));
  }

  private Usuario createUsuarioItem(
      String registro, String nombre, String paterno, String materno) {
    Usuario usuario = new Usuario();
    usuario.setId(System.currentTimeMillis() + (long) (Math.random() * 1000)); // ID temporal único
    usuario.setRegistro(registro);
    usuario.setNombre(nombre);
    usuario.setPaterno(paterno);
    usuario.setMaterno(materno);

    return usuario;
  }
}
