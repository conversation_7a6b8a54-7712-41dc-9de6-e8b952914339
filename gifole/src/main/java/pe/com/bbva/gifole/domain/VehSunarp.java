package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_SUNARP")
public class VehSunarp implements Serializable {

  @Id
  @Column(name = "NUM_PLACA_VIG", length = 10)
  private String numPlacaVig;

  @Column(name = "NUM_PLACA_NO_VIG", length = 10)
  private String numPlacaNoVig;

  @Column(name = "MARCA", length = 4)
  private String marca;

  @Column(name = "MODELO", length = 8)
  private String modelo;

  @Column(name = "CLASE", length = 4)
  private String clase;

  @Column(name = "TIPO", length = 7)
  private String tipo;

  @Column(name = "ANHO_FABRICACION", length = 4)
  private String anhoFabricacion;
}
