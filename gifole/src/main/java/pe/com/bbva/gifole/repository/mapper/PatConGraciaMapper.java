package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.PrestamoAlToqueBean;

@Component
public class PatConGraciaMapper implements RowMapper<PrestamoAlToqueBean> {

  @Override
  public PrestamoAlToqueBean mapRow(ResultSet rs, int rowNum) throws SQLException {
    PrestamoAlToqueBean prestamoAlToqueBean = new PrestamoAlToqueBean();
    prestamoAlToqueBean.setId(rs.getLong("ID"));
    prestamoAlToqueBean.setCodigoCentral(rs.getString("CODIGO_CENTRAL"));
    prestamoAlToqueBean.setNombreCompleto(rs.getString("NOMBRE_COMPLETO"));
    prestamoAlToqueBean.setCorreo(rs.getString("CORREO"));
    prestamoAlToqueBean.setNumeroContrato(rs.getString("NUMERO_CONTRATO"));
    prestamoAlToqueBean.setCanal(rs.getString("CANAL"));
    prestamoAlToqueBean.setCorreoExtorno(rs.getString("CORREO_EXTORNO"));
    prestamoAlToqueBean.setFechaRegistro(rs.getTimestamp("FECHA_SOLICITUD"));
    prestamoAlToqueBean.setFechaAprobacionPeriodoGracia(rs.getTimestamp("FECHA_APROBACION"));
    prestamoAlToqueBean.setFechaSolicitudExtorno(rs.getTimestamp("FECHA_SOLICITUD_EXTORNO"));
    prestamoAlToqueBean.setFechaExtorno(rs.getTimestamp("FECHA_EXTORNO"));

    if (prestamoAlToqueBean.getFechaAprobacionPeriodoGracia() != null) {
      prestamoAlToqueBean.setEstadoPeriodoGracia("PROCESADO");
    } else {
      prestamoAlToqueBean.setEstadoPeriodoGracia("REGISTRADO");
    }

    if (prestamoAlToqueBean.getFechaSolicitudExtorno() != null) {
      prestamoAlToqueBean.setEstadoExtorno("PENDIENTE DE EXTORNAR");
    }

    if (prestamoAlToqueBean.getFechaExtorno() != null) {
      prestamoAlToqueBean.setEstadoExtorno("EXTORNO PROCESADO");
    }

    return prestamoAlToqueBean;
  }
}
