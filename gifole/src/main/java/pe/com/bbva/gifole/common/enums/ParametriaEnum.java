package pe.com.bbva.gifole.common.enums;

/**
 * <AUTHOR>
 */
public enum ParametriaEnum {
  PATH_OUT_INTRANET_ANEXO_AFILIACION_VIP(
      "JB_PATH_OUT_INTRANET_ANEXO_CUENTA_VIP",
      "VIP - Documentos TyC",
      "JB_CANTIDAD_DOCUMENTOS_ANEXO_AFILIACION_VIP",
      1),

  PATH_OUT_INTRANET_TARJ_TOC(
      "JB_PATH_OUT_INTRANET_TARJ_TOC",
      "Subir archivos TOC (Pdfs)",
      "JB_CANTIDAD_DOCUMENTOS_ANEXO_TARJETA_TOC",
      2),

  PATH_OUT_INTRANET_TARJ_CERO(
      "JB_PATH_OUT_INTRANET_TARJ_CERO",
      "Subir archivos Tarjeta Cero (Pdfs)",
      "JB_CANTIDAD_DOCUMENTOS_ANEXO_TARJETA_CERO",
      2),

  PATH_OUT_INTRANET_<PERSON>ARJ_CONGELADA(
      "JB_PATH_OUT_INTRANET_TARJ_CONGELADA",
      "Subir archivos Tarjeta Cuota Fija (Pdfs)",
      "JB_CANTIDAD_DOCUMENTOS_ANEXO_TARJETA_CONGELADA",
      2);

  private final String codigo;
  private final String descripcion;
  private final String codigoDocumentosAnexo;
  private final int cantidadInicialDocumentosAnexo;

  private ParametriaEnum(
      final String codigo,
      final String descripcion,
      final String codigoDocumentosAnexo,
      final int cantidadInicialDocumentosAnexo) {
    this.codigo = codigo;
    this.descripcion = descripcion;
    this.codigoDocumentosAnexo = codigoDocumentosAnexo;
    this.cantidadInicialDocumentosAnexo = cantidadInicialDocumentosAnexo;
  }

  public String getCodigo() {
    return codigo;
  }

  public String getDescripcion() {
    return descripcion;
  }

  public String getCodigoDocumentosAnexo() {
    return codigoDocumentosAnexo;
  }

  public int getCantidadInicialDocumentosAnexo() {
    return cantidadInicialDocumentosAnexo;
  }

  public static ParametriaEnum getCodigoParametria(String codigo) {
    if (codigo == null) {
      return null;
    }
    for (ParametriaEnum parametria : ParametriaEnum.values()) {
      if (codigo.equals(parametria.getCodigo())) {
        return parametria;
      }
    }
    return null;
  }
}
