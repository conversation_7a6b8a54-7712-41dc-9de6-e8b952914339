package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.TCTocDetEstado;

@Component
public class CreditoConsultaEstadoMapper implements RowMapper<TCTocDetEstado> {

  @Override
  public TCTocDetEstado mapRow(ResultSet rs, int i) throws SQLException {

    TCTocDetEstado credito = new TCTocDetEstado();
    credito.setId(rs.getLong("ID"));
    return credito;
  }
}
