package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@Entity
@Table(name = "AFILIACION_EECC_FFMM")
public class AfiliacionEECCFFMM implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_AFILIACION_EECC_FFMM")
  @TableGenerator(
      name = "SEQ_AFILIACION_EECC_FFMM",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.AfiliacionEECCFFMM",
      allocationSize = 1)
  private Long id;

  @Column(name = "NOMBRE", length = 150)
  private String nombre;

  @Column(name = "CODIGO_CENTRAL", length = 8)
  private String codigoCentral;

  @Column(name = "CORREO1", length = 80)
  private String correo1;

  @Column(name = "PROCESADO", length = 1)
  private String procesado;

  @Column(name = "FECHA_REGISTRO")
  private Date fechaRegistro;

  @Transient private List<AfiliacionEECCFFMMDetalle> afiliacionEECCFFMMDetalles;

  @Transient private String nombreEmail;

  @Transient private String fechaOperacion;

  @Transient private String tipoBanca;
}
