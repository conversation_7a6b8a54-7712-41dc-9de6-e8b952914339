package pe.com.bbva.gifole.domain;

import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
public class SisAuditoria implements Serializable {

  private Long id;
  private String tabla;
  private String tipo;
  private String data;
  private String usuario;
  private Date fecha;
  private String id_registro;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getTipo() {
    return tipo;
  }

  public void setTipo(String tipo) {
    this.tipo = tipo;
  }

  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public Date getFecha() {
    return fecha;
  }

  public void setFecha(Date fecha) {
    this.fecha = fecha;
  }

  public String getTabla() {
    return tabla;
  }

  public void setTabla(String tabla) {
    this.tabla = tabla;
  }

  public String getData() {
    return data;
  }

  public void setData(String data) {
    this.data = data;
  }

  public String getId_registro() {
    return id_registro;
  }

  public void setId_registro(String id_registro) {
    this.id_registro = id_registro;
  }
}
