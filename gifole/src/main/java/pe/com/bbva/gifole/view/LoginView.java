package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.html.Div;
import com.vaadin.flow.component.html.H1;
import com.vaadin.flow.component.html.Paragraph;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.PasswordField;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import org.springframework.beans.factory.annotation.Autowired;
import pe.com.bbva.gifole.domain.Parametro;
import pe.com.bbva.gifole.domain.SisPerfil;
import pe.com.bbva.gifole.domain.Usuario;
import pe.com.bbva.gifole.service.ParametroService;
import pe.com.bbva.gifole.service.SeguridadService;
import pe.com.bbva.gifole.util.Constants;
import pe.com.bbva.gifole.view.theme.BBVATheme;

/** Vista de login con diseno BBVA - Solo UI (sin MainLayout) */
@CssImport("./themes/bbva/views/login-view.css")
@Route(value = "login", layout = EmptyLayout.class)
@PageTitle("Iniciar Sesion | BBVA")
public class LoginView extends VerticalLayout {

  private TextField usernameField;
  private PasswordField passwordField;
  private Button loginButton;

  @Autowired private ParametroService parametroService;

  @Autowired SeguridadService seguridadService;

  public LoginView() {
    initializeView();
    createContent();
  }

  private void initializeView() {
    setSizeFull();
    setMargin(false);
    setPadding(false);
    setSpacing(false);
    setJustifyContentMode(FlexComponent.JustifyContentMode.CENTER);
    setAlignItems(FlexComponent.Alignment.CENTER);

    // Aplicar clase CSS especifica para esta vista
    addClassName("app-main");
  }

  private void createContent() {
    // Contenedor principal de la tarjeta de login
    VerticalLayout loginCard = createLoginCard();
    add(loginCard);
  }

  private VerticalLayout createLoginCard() {
    VerticalLayout card = new VerticalLayout();
    card.setPadding(true);
    card.setSpacing(true);

    // Aplicar clase CSS para el contenedor del formulario
    card.addClassName("login-form-container");

    // Logo y header
    card.add(createHeader());

    // Formulario de login
    card.add(createLoginForm());

    // Footer con enlaces
    card.add(createFooter());

    return card;
  }

  private VerticalLayout createHeader() {
    VerticalLayout header = new VerticalLayout();
    header.setSpacing(false);
    header.setPadding(false);
    header.setAlignItems(FlexComponent.Alignment.CENTER);

    // Logo BBVA simulado con rectangulos de colores
    HorizontalLayout logoContainer = createBBVALogo();
    logoContainer.addClassName("login-logo");

    // Titulo principal
    H1 title = new H1("Bienvenido a BBVA");
    title.addClassName("bbva-text-navy");

    // Subtitulo
    Paragraph subtitle = new Paragraph("Inicia sesion para acceder a tu cuenta");
    subtitle.addClassName("bbva-text-navy");

    header.add(logoContainer, title, subtitle);
    return header;
  }

  private HorizontalLayout createBBVALogo() {
    HorizontalLayout logoContainer = new HorizontalLayout();
    logoContainer.setSpacing(false);
    logoContainer.setAlignItems(FlexComponent.Alignment.CENTER);
    logoContainer.setJustifyContentMode(FlexComponent.JustifyContentMode.CENTER);

    // Simulando el logo BBVA con rectangulos de colores
    Div blueLogo = createLogoBar(BBVATheme.BBVA_BLUE);
    Div lightBlueLogo = createLogoBar(BBVATheme.BBVA_LIGHT_BLUE);
    Div aquaLogo = createLogoBar(BBVATheme.BBVA_AQUA);

    logoContainer.add(blueLogo, lightBlueLogo, aquaLogo);
    return logoContainer;
  }

  private Div createLogoBar(String color) {
    Div logoBar = new Div();
    logoBar.getStyle().set("width", "8px");
    logoBar.getStyle().set("height", "24px");
    logoBar.getStyle().set("background-color", color);
    logoBar.getStyle().set("margin-right", "2px");
    logoBar.getStyle().set("border-radius", "1px");
    return logoBar;
  }

  private VerticalLayout createLoginForm() {
    VerticalLayout form = new VerticalLayout();
    form.setSpacing(true);
    form.setPadding(false);

    // Campo de usuario con icono
    usernameField = new TextField();
    usernameField.setPlaceholder("Usuario o correo electronico");
    usernameField.setWidthFull();
    usernameField.setPrefixComponent(new Icon(VaadinIcon.USER));
    usernameField.addClassName("login-field");

    // Campo de contrasena con icono
    passwordField = new PasswordField();
    passwordField.setPlaceholder("Contrasena");
    passwordField.setWidthFull();
    passwordField.setPrefixComponent(new Icon(VaadinIcon.LOCK));
    passwordField.addClassName("login-field");

    // Icono de mostrar/ocultar contrasena
    Icon eyeIcon = new Icon(VaadinIcon.EYE_SLASH);
    eyeIcon.getStyle().set("cursor", "pointer");
    eyeIcon.getStyle().set("color", "#666");
    passwordField.setSuffixComponent(eyeIcon);

    // Boton de login
    loginButton = new Button("Iniciar sesion");
    loginButton.setWidthFull();
    loginButton.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    loginButton.addClassName("login-button");

    // Evento de click del boton (solo UI)
    loginButton.addClickListener(e -> handleLogin());

    form.add(usernameField, passwordField, loginButton);
    return form;
  }

  private VerticalLayout createFooter() {
    VerticalLayout footer = new VerticalLayout();
    footer.setSpacing(false);
    footer.setPadding(false);
    footer.setAlignItems(FlexComponent.Alignment.CENTER);
    footer.addClassName("login-footer");

    // Texto de proteccion
    Paragraph helpText = new Paragraph("Protegido por BBVA. Consulta nuestra ");

    // Enlaces
    HorizontalLayout links = new HorizontalLayout();
    links.setSpacing(false);
    links.setJustifyContentMode(FlexComponent.JustifyContentMode.CENTER);

    Button privacyLink = createFooterLink("Politica de privacidad");
    Paragraph separator = new Paragraph(" y ");
    Button termsLink = createFooterLink("Terminos de servicio");

    links.add(privacyLink, separator, termsLink);

    footer.add(helpText, links);
    return footer;
  }

  private Button createFooterLink(String text) {
    Button link = new Button(text);
    link.addThemeVariants(ButtonVariant.LUMO_TERTIARY_INLINE);
    link.addClassName("bbva-text-primary");
    return link;
  }

  private void handleLogin() {
    String username = usernameField.getValue();
    String password = passwordField.getValue();

    // Validacion simple solo para la UI
    if (username.isEmpty() || password.isEmpty()) {
      usernameField.setInvalid(username.isEmpty());
      passwordField.setInvalid(password.isEmpty());

      Notification.show(
          "Por favor, completa todos los campos", 3000, Notification.Position.TOP_CENTER);
      return;
    }

    try {
      // Obtener parametro LDAP sin BD
      String pFlagLDAPSinBD =
          parametroService
              .obtenerParametroPorCodigo(
                  Parametro.CODIGO.LDAP.FLAGLDAPSINBD, Parametro.TIPO.CONFIGURACION)
              .getValor();

      if (Constants.MODO_PERFIL.equals("desarrollo")) {
        Usuario usuario = new Usuario();
        usuario.setId(Long.valueOf(1));
        usuario.setRegistro("P020092");
        SisPerfil sisPerfil = new SisPerfil();
        sisPerfil.setId(Long.valueOf(1));
        sisPerfil.setDescripcion("ADMINISTRADOR");
        usuario.setSisPerfil(sisPerfil);
        // return usuario;
      } else {
        if (pFlagLDAPSinBD.equalsIgnoreCase("1")) {
          seguridadService.loginLdapGlobalGiam(username, password);
        } else {
          seguridadService.loginEnGifole(username);
        }
      }

      // Mostrar el valor obtenido para verificacion
      Notification.show(
          "Flag LDAP sin BD: " + pFlagLDAPSinBD, 5000, Notification.Position.TOP_CENTER);

    } catch (Exception e) {
      Notification.show(
          "Error al obtener parametro LDAP: " + e.getMessage(),
          5000,
          Notification.Position.TOP_CENTER);
    }

    // Simulacion de login exitoso - solo para demostrar la UI
    Notification.show("Bienvenido! Login simulado exitoso", 3000, Notification.Position.TOP_CENTER);

    // Redirigir a la pagina principal despues del login
    getUI().ifPresent(ui -> ui.navigate(""));
  }
}
