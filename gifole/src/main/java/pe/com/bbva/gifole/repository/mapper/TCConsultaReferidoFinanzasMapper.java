package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.TCEnvioReferidoFinanzasBean;
import pe.com.bbva.gifole.common.enums.DocumentoIdentidadEnum;

@SuppressWarnings("rawtypes")
@Component
public class TCConsultaReferidoFinanzasMapper implements RowMapper {

  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    TCEnvioReferidoFinanzasBean param = new TCEnvioReferidoFinanzasBean();

    String tipoDoc = "";
    if (rs.getString("TIPODOCUMENTOREFERIDO") != null) {
      if (rs.getString("TIPODOCUMENTOREFERIDO").equals("DNI")) {
        tipoDoc = "L";
      }
    }
    String tipoDocReferidor = "";
    if (StringUtils.isNotBlank(rs.getString("TIPODOCUMENTOREFERIDOR"))) {
      DocumentoIdentidadEnum doc =
          DocumentoIdentidadEnum.obtenerDocPorNombre(rs.getString("TIPODOCUMENTOREFERIDOR"));
      if (doc != null) {
        tipoDocReferidor = doc.getCodigo(); // L, E, etc
      }
    }
    param.setIdReferido(rs.getLong("IDREFERIDO"));
    param.setNombreCompletoReferidor(
        rs.getString("NOMBREREFERIDOR") + " " + rs.getString("APELLIDOPATERNOREFERIDOR"));
    param.setTipoDocumentoReferidor(tipoDocReferidor);
    param.setNumeroDocumentoReferidor(rs.getString("NRODOCUMENTOREFERIDOR"));
    if (StringUtils.isNotBlank(rs.getString("EMPLEADO"))
        && rs.getString("EMPLEADO").equals("EMPLOYEE")) {
      param.setMarcaEmpleadoReferidor("SI");
    } else {
      param.setMarcaEmpleadoReferidor("NO");
    }
    String fechaRegistro = "";
    DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    if (Objects.nonNull(rs.getTimestamp("FECHAREGISTRO"))) {
      fechaRegistro = dateFormat.format(rs.getTimestamp("FECHAREGISTRO"));
    }
    param.setFechaRegistro(fechaRegistro);
    String fechaEvaluacion = "";
    if (rs.getString("ESTADOREFERIDO").equals("RECHAZO_RIESGO")) {
      param.setFechaEvaluacion(fechaRegistro);
    } else {
      if (Objects.nonNull(rs.getTimestamp("FECHA_EVALUACION"))) {
        fechaEvaluacion = dateFormat.format(rs.getTimestamp("FECHA_EVALUACION"));
      }
      param.setFechaEvaluacion(fechaEvaluacion);
    }
    param.setNumeroDocumentoReferido(rs.getString("NRODOCUMENTOREFERIDO"));
    param.setTipoDocumentoReferido(tipoDoc);
    // param.setCodigoCentralReferido(rs.getString("CODIGOCENTRALREFERIDO"));
    param.setNombreCompletoReferido(
        rs.getString("NOMBREREFERIDO") + " " + rs.getString("APELLIDOPATERNOREFERIDO"));
    param.setEstadoReferido(rs.getString("ESTADOREFERIDO"));
    param.setCelularReferido(rs.getString("CELULAREFERIDO"));
    param.setNbrTarjetaReferido(rs.getString("NOMBRETARJETAREFERIDO"));
    param.setTipoTarjetaReferido(rs.getString("TIPOTARJETAREFERIDO")); // BIN REFERIDO
    param.setLineaReferido(rs.getString("LINEAREFERIDO"));
    param.setTasaReferido(rs.getString("TASAREFERIDO"));
    if (Objects.nonNull(rs.getString("IDEVALUACION"))) {
      param.setHistoricoEvaluacion(rs.getString("IDEVALUACION"));
    } else {
      param.setHistoricoEvaluacion(rs.getString("EVALUACION_PENDIENTE"));
    }
    param.setHistoricoObservacion(rs.getString("OBSERVACION"));

    if (StringUtils.isNotBlank(rs.getString("ORIGENREFERIDO"))) {
      String nombreArchivo = rs.getString("ORIGENREFERIDO");
      nombreArchivo = nombreArchivo.replaceAll(".txt", StringUtils.EMPTY);
      String anio = nombreArchivo.substring(nombreArchivo.length() - 8, nombreArchivo.length() - 4);
      String mes = nombreArchivo.substring(nombreArchivo.length() - 4, nombreArchivo.length() - 2);
      String dia = nombreArchivo.substring(nombreArchivo.length() - 2, nombreArchivo.length());
      param.setOrigenReferido(anio + "-" + mes + "-" + dia);
    }

    if (StringUtils.isNotBlank(rs.getString("OBSERVACIONPREMIOREFERIDO"))) {
      param.setObservacionPremioReferido(rs.getString("OBSERVACIONPREMIOREFERIDO"));
    }

    param.setCorreoReferido(rs.getString("CORREOREFERIDO"));
    param.setCampaniaReferido(rs.getString("CAMPANAREFERIDO"));
    param.setCampaniaReferidor(rs.getString("CAMPANAREFERIDOR"));
    param.setPasoMotor(rs.getString("PASOMOTOR"));
    param.setMultibinReferido(rs.getString("MULTIBINREFERIDO"));
    param.setNroContratoTablon(rs.getString("NROCONTRATOTABLONREFERIDO"));
    param.setNroTarjetaTablon(rs.getString("NROTARJETATABLONREFERIDO"));
    param.setFechaActivacionTablon(rs.getString("FECHAACTIVATABLONREFERIDO"));
    param.setFechaContratacionTablon(rs.getString("FECHACONTRATATABLONREFERIDO"));

    return param;
  }
}
