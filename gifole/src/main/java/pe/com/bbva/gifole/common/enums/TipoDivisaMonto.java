package pe.com.bbva.gifole.common.enums;

public enum TipoDivisaMonto {
  SOLES("SOLES", "S/"),
  DOLARES("DOLARES", "$");

  private final String nombre;
  private final String simbolo;

  private TipoDivisaMonto(String nombre, String simbolo) {

    this.nombre = nombre;
    this.simbolo = simbolo;
  }

  public String getNombre() {
    return nombre;
  }

  public String getSimbolo() {
    return simbolo;
  }
}
