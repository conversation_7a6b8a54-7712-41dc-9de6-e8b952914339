package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_PRIMA_MINIMA")
public class VehPrimaMinima implements Serializable {

  @Id
  @Column(name = "CATEGORIA", length = 4)
  private String categoria;

  @Id
  // bidirectional many-to-one association to VehPlan
  @ManyToOne
  @JoinColumn(name = "PLAN")
  private VehPlan plan;

  @Column(name = "MONTO", precision = 15, scale = 2)
  private BigDecimal monto;

  @Column(name = "DIVISA", length = 3)
  private String divisa;
}
