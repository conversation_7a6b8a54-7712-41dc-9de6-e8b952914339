package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.CotizacionSeguroRoyalBean; // Ajusta el paquete si es necesario
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Cotización Seguro Royal")
@Route(value = "reporte/seguros/cotizacion-royal", layout = MainLayout.class)
public class ConsultaCotizacionSeguroRoyalView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<CotizacionSeguroRoyalBean> allData = new ArrayList<>();
    private DataTable<CotizacionSeguroRoyalBean> dataTable;

    public ConsultaCotizacionSeguroRoyalView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Cotización Seguro Royal");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<CotizacionSeguroRoyalBean>builder()
                .id("tabla-cotizacion-seguro-royal") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaCotizacionSeguroRoyalUI)
                // Seleccionadas según las columnas del DTO proporcionado y el análisis de la UI antigua
                // Selecciono las que parecen más relevantes basadas en el DTO
                .column("cotizacionId", "Código Cotización", // Equivalente a "codigoCotizacion" del DTO
                    bean -> StringUtils.trimToEmpty(bean.getNroContizacion()), "130px")
                .column("productoName", "Tipo Seguro", // Equivalente a "tipoSeguro" del DTO
                    bean -> StringUtils.trimToEmpty(bean.getNombreProducto()), "180px")
                 .column("nombreCompleto", "Cliente", // Combinación de nombre y apellido, equivalente a "cliente" del DTO
                    bean -> StringUtils.trimToEmpty(
                        (StringUtils.trimToEmpty(bean.getNombre()) + " " + StringUtils.trimToEmpty(bean.getApellido())).trim()
                    ), "250px")
                .column("tipoDocumento", "Tipo Documento", // Parte de "Documento Identidad"
                    bean -> StringUtils.trimToEmpty(bean.getTipoDocumento()), "110px")
                .column("numeroDocumento", "Número Documento", // Parte de "Documento Identidad"
                    bean -> StringUtils.trimToEmpty(bean.getNroDocumento()), "145px")
                .column("primaTotal", "Prima", // Combinación de primaMonto y primaMontoMoneda, equivalente a "prima" del DTO
                    bean -> StringUtils.trimToEmpty(
                        (StringUtils.trimToEmpty(bean.getMontoPrima()) + " " + StringUtils.trimToEmpty(bean.getMonedaPrima())).trim()
                    ), "150px")
                .column("fechaRegistroCotizacion", "Fecha Cotización", // Equivalente a "fechaCotizacion" del DTO
                    bean -> bean.getFechaRegistroDesde() != null ? FORMATO_FECHA.format(bean.getFechaRegistroDesde()) : "", "170px")
                .column("fechaVigFin", "Vigencia Hasta", // Equivalente a "vigenciaHasta" del DTO
                    bean -> bean.getFechaRegistroHasta() != null ? FORMATO_FECHA.format(bean.getFechaRegistroHasta()) : "", "170px")
                // Puedes agregar más columnas aquí si las necesitas, siguiendo el mismo patrón
                // Ejemplos de otras columnas disponibles en el bean:
                // .column("id", "Id", ...)
                // .column("apellido", "Apellido", ...)
                // .column("correo", "Correo", ...)
                // .column("telefono", "Teléfono", ...)
                // .column("productoId", "ID Producto", ...)
                // .column("planCuotasMoneda", "Plan Cuotas Moneda", ...)
                // .column("planCuotasPeriodo", "Plan Cuotas Período", ...)
                // .column("planCuotasMonto", "Plan Cuotas Monto", ...)
                // .column("planNumeroCuotasTotal", "Número Total de Cuotas", ...)
                // .column("sumaAseguradaMoneda", "Suma Asegurada Moneda", ...)
                // .column("sumaAsegurada", "Suma Asegurada", ...)
                // .column("fechaVigIni", "Fecha Vigencia Inicio", ...)
                // .column("metodoPago", "Método de Pago", ...)
                // .column("nroCuentaOTarjeta", "Número de Tarjeta/Cuenta", ...)
                // .column("canal", "Canal", ...)
                // .column("subChannel", "Subcanal", ...)
                // .column("informacionBanco", "Información Banco", ...)
                // .column("codigoOficina", "Código Oficina", ...)
                // .column("couponCode", "Cupón", ...)
                // Datos (inicialmente vacío)
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}