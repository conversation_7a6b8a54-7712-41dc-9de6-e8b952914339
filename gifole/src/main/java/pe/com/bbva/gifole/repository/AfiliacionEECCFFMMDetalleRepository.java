package pe.com.bbva.gifole.repository;

import java.util.Date;
import java.util.List;
import pe.com.bbva.gifole.domain.AfiliacionEECCFFMMDetalle;

public interface AfiliacionEECCFFMMDetalleRepository {
  void crear(AfiliacionEECCFFMMDetalle afiliacionEECCFFMMDetalle);

  List<AfiliacionEECCFFMMDetalle> buscarAfiliacionEECCFFMMNoProcesado();

  List<AfiliacionEECCFFMMDetalle> buscarAfiliacionEECCFFMMNoPorFecha(Date desde, Date hasta);
}
