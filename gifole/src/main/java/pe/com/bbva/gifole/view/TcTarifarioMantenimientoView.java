package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;

@PageTitle("Mantenimiento de Costos (Tarifario)")
@Route(value = "tc-tarifario-mantenimiento", layout = MainLayout.class)
public class TcTarifarioMantenimientoView extends VerticalLayout {

  // Filtros
  private final ComboBox<String> filtroEstado = new ComboBox<>("Estado");
  private final TextField filtroPrioridad = new TextField("Priorid.");
  private final TextField filtroBin = new TextField("BIN");
  private final ComboBox<String> filtroMarca = new ComboBox<>("Marca");
  private final ComboBox<String> filtroPrograma = new ComboBox<>("Programa Lealtad");
  private final TextField filtroTcea = new TextField("TCEA");
  private final TextField filtroMembresia = new TextField("Membresia");
  private final TextField filtroEecc = new TextField("EECC");
  private final TextField filtroSeguro = new TextField("Seg. Des.");
  private final TextField filtroPrioridadDos = new TextField("Prioridad");
  private final ComboBox<String> filtroOrientacion = new ComboBox<>("Orientación");
  private final TextField filtroBono = new TextField("Bono");

  public TcTarifarioMantenimientoView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Filtros de Mantenimiento de Costos (Tarifario)");
    title.addClassName("bbva-grid-title");

    // Panel de filtros
    HorizontalLayout filtersPanel = createFiltersPanel();

    mainLayout.add(title, filtersPanel);
    add(mainLayout);
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.END);
    filtersPanel.setSpacing(false);

    filtroEstado.addClassName("bbva-input-floating");
    filtroEstado.setWidth("90px");
    filtroEstado.setItems("Todos", "ACTIVO", "INACTIVO");
    filtroEstado.setValue("Todos");

    filtroPrioridad.addClassName("bbva-input-floating");
    filtroPrioridad.setWidth("70px");

    filtroBin.addClassName("bbva-input-floating");
    filtroBin.setWidth("70px");

    filtroMarca.addClassName("bbva-input-floating");
    filtroMarca.setWidth("90px");
    filtroMarca.setItems("Todas", "Visa", "Mastercard", "American Express");
    filtroMarca.setValue("Todas");

    filtroPrograma.addClassName("bbva-input-floating");
    filtroPrograma.setWidth("140px");
    filtroPrograma.setItems("Todos", "Millas para viajar", "Puntos para comprar");
    filtroPrograma.setValue("Todos");

    filtroTcea.addClassName("bbva-input-floating");
    filtroTcea.setWidth("70px");

    filtroMembresia.addClassName("bbva-input-floating");
    filtroMembresia.setWidth("90px");

    filtroEecc.addClassName("bbva-input-floating");
    filtroEecc.setWidth("70px");

    filtroSeguro.addClassName("bbva-input-floating");
    filtroSeguro.setWidth("80px");

    filtroPrioridadDos.addClassName("bbva-input-floating");
    filtroPrioridadDos.setWidth("80px");

    filtroOrientacion.addClassName("bbva-input-floating");
    filtroOrientacion.setWidth("100px");
    filtroOrientacion.setItems("Todas", "Horizontal", "Vertical");
    filtroOrientacion.setValue("Todas");

    filtroBono.addClassName("bbva-input-floating");
    filtroBono.setWidth("70px");

    filtersPanel.add(
        filtroEstado,
        filtroPrioridad,
        filtroBin,
        filtroMarca,
        filtroPrograma,
        filtroTcea,
        filtroMembresia,
        filtroEecc,
        filtroSeguro,
        filtroPrioridadDos,
        filtroOrientacion,
        filtroBono);

    return filtersPanel;
  }
}
