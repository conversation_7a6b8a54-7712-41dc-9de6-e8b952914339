package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.shared.Tooltip;
import com.vaadin.flow.component.textfield.TextArea;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import java.util.List;
import pe.com.bbva.gifole.domain.Parametro;
import pe.com.bbva.gifole.domain.TcTarifario;

@PageTitle("Mantenimiento de Costos (Tarifario)")
@Route(value = "tc-tarifario-mantenimiento", layout = MainLayout.class)
public class TcTarifarioMantenimientoView extends VerticalLayout {

  // Constantes
  private static final String ACTIVO = "ACTIVO";
  private static final String INACTIVO = "INACTIVO";

  private final Grid<TcTarifario> grid = new Grid<>(TcTarifario.class, false);
  private final List<TcTarifario> tarifariosList = new ArrayList<>();
  private final List<TcTarifario> allTarifarios = new ArrayList<>();

  // Variables de paginación
  private int currentPage = 0;
  private final int pageSize = 10;
  private int totalPages = 0;

  // Componentes de paginación
  private Button previousButton;
  private Button nextButton;
  private Span pageInfo;

  // Filtros superiores
  private final ComboBox<String> filtroEstado = new ComboBox<>();
  private final TextField filtroPrioridad = new TextField();
  private final TextField filtroBin = new TextField();
  private final ComboBox<String> filtroMarca = new ComboBox<>();
  private final ComboBox<String> filtroPrograma = new ComboBox<>();
  private final TextField filtroTcea = new TextField();
  private final TextField filtroMembresia = new TextField();
  private final TextField filtroEecc = new TextField();
  private final TextField filtroSeguro = new TextField();
  private final TextField filtroPrioridadDos = new TextField();
  private final ComboBox<String> filtroOrientacion = new ComboBox<>();
  private final TextField filtroBono = new TextField();

  // Campos del formulario
  private final TextField txtBin = new TextField();
  private final ComboBox<String> cmbMarca = new ComboBox<>();
  private final ComboBox<String> cmbPrograma = new ComboBox<>();
  private final TextField txtTcea = new TextField();
  private final TextField txtMembresia = new TextField();
  private final TextField txtEecc = new TextField();
  private final TextField txtSeguro = new TextField();
  private final TextField txtImagen = new TextField();
  private final ComboBox<String> cmbOrientacion = new ComboBox<>();
  private final TextField txtDescripcion = new TextField();
  private final TextArea txtObservacion = new TextArea();
  private final ComboBox<String> cmbFlagTdh = new ComboBox<>();
  private final TextField txtImagenTdh = new TextField();
  private final TextField txtNombreTdh = new TextField();
  private final ComboBox<String> cmbOrientacionTdh = new ComboBox<>();
  private final ComboBox<String> cmbEstado = new ComboBox<>();
  private final TextField txtRangoTcea = new TextField();
  private final TextField txtBono = new TextField();
  private final TextField txtConsumoBono = new TextField();
  private final TextField txtDiasBono = new TextField();
  private final TextField txtConsumoMembresia = new TextField();
  private final TextField txtDescripcionZp = new TextField();

  // Botones
  private final Button btnCrear = new Button("Crear");
  private final Button btnNuevo = new Button("Nuevo", VaadinIcon.PLUS.create());
  private final Button btnCerrarDrawer = new Button(VaadinIcon.CLOSE.create());

  // Drawer/Panel flotante
  private VerticalLayout drawerPanel;
  private boolean drawerVisible = false;

  public TcTarifarioMantenimientoView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);
    getStyle().set("position", "relative"); // Para posicionamiento del drawer

    // Crear layout principal
    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Panel de filtros superiores
    HorizontalLayout filtersPanel = createFiltersPanel();

    // Header con título y botón nuevo
    HorizontalLayout headerLayout = createHeaderLayout();

    // Card para el Grid (ahora ocupa todo el ancho)
    VerticalLayout gridCard = createGridCard();
    gridCard.setSizeFull();

    mainLayout.add(filtersPanel, headerLayout, gridCard);
    mainLayout.setFlexGrow(1, gridCard);

    // Crear drawer flotante
    drawerPanel = createDrawerPanel();

    add(mainLayout, drawerPanel);

    // Configurar componentes
    configureComponents();

    // Cargar datos de ejemplo
    loadSampleData();
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.END);
    filtersPanel.setSpacing(false); // Controlado por CSS gap

    // Configurar filtros con anchos específicos y clase CSS
    filtroEstado.setLabel("Estado");
    filtroEstado.addClassName("bbva-input-floating");
    filtroEstado.setWidth("90px");
    filtroEstado.setItems("Todos", ACTIVO, INACTIVO);
    filtroEstado.setValue("Todos");

    filtroPrioridad.setLabel("Priorid.");
    filtroPrioridad.addClassName("bbva-input-floating");
    filtroPrioridad.setWidth("70px");

    filtroBin.setLabel("BIN");
    filtroBin.addClassName("bbva-input-floating");
    filtroBin.setWidth("70px");

    filtroMarca.setLabel("Marca");
    filtroMarca.addClassName("bbva-input-floating");
    filtroMarca.setWidth("90px");
    filtroMarca.setItems("Todas", "Visa", "Mastercard", "American Express");
    filtroMarca.setValue("Todas");

    filtroPrograma.setLabel("Programa Lealtad");
    filtroPrograma.addClassName("bbva-input-floating");
    filtroPrograma.setWidth("140px");
    filtroPrograma.setItems("Todos", "Millas para viajar", "Puntos para comprar");
    filtroPrograma.setValue("Todos");

    filtroTcea.setLabel("TCEA");
    filtroTcea.addClassName("bbva-input-floating");
    filtroTcea.setWidth("70px");

    filtroMembresia.setLabel("Membresia");
    filtroMembresia.addClassName("bbva-input-floating");
    filtroMembresia.setWidth("90px");

    filtroEecc.setLabel("EECC");
    filtroEecc.addClassName("bbva-input-floating");
    filtroEecc.setWidth("70px");

    filtroSeguro.setLabel("Seg. Des.");
    filtroSeguro.addClassName("bbva-input-floating");
    filtroSeguro.setWidth("80px");

    filtroPrioridadDos.setLabel("Prioridad");
    filtroPrioridadDos.addClassName("bbva-input-floating");
    filtroPrioridadDos.setWidth("80px");

    filtroOrientacion.setLabel("Orientación");
    filtroOrientacion.addClassName("bbva-input-floating");
    filtroOrientacion.setWidth("100px");
    filtroOrientacion.setItems("Todas", "Horizontal", "Vertical");
    filtroOrientacion.setValue("Todas");

    filtroBono.setLabel("Bono");
    filtroBono.addClassName("bbva-input-floating");
    filtroBono.setWidth("70px");

    // Agregar todos los filtros directamente al panel principal
    filtersPanel.add(
        filtroEstado,
        filtroPrioridad,
        filtroBin,
        filtroMarca,
        filtroPrograma,
        filtroTcea,
        filtroMembresia,
        filtroEecc,
        filtroSeguro,
        filtroPrioridadDos,
        filtroOrientacion,
        filtroBono);

    return filtersPanel;
  }

  private HorizontalLayout createHeaderLayout() {
    HorizontalLayout headerLayout = new HorizontalLayout();
    headerLayout.setWidthFull();
    headerLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    headerLayout.setAlignItems(FlexComponent.Alignment.CENTER);
    headerLayout.setPadding(false);
    headerLayout.setSpacing(true);

    H2 pageTitle = new H2("Lista de Tarifarios");
    pageTitle.addClassName("bbva-grid-title");
    pageTitle.getStyle().set("margin", "0");

    // Botón para abrir el drawer
    btnNuevo.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnNuevo.addClassName("bbva-filters-button");
    btnNuevo.addClickListener(e -> toggleDrawer());
    Tooltip.forComponent(btnNuevo).withText("Crear nuevo tarifario");

    headerLayout.add(pageTitle, btnNuevo);
    return headerLayout;
  }

  private VerticalLayout createDrawerPanel() {
    VerticalLayout drawer = new VerticalLayout();
    drawer.addClassName("drawer-panel");
    drawer.setWidth("450px");
    drawer.setHeight("100%");
    drawer.setPadding(true);
    drawer.setSpacing(true);

    // Estilos CSS para el drawer flotante
    drawer
        .getStyle()
        .set("position", "fixed")
        .set("top", "0")
        .set("right", "-450px") // Inicialmente oculto
        .set("background", "var(--bbva-white)")
        .set("box-shadow", "-4px 0 20px rgba(0, 0, 0, 0.15)")
        .set("z-index", "1000")
        .set("transition", "right 0.3s ease-in-out")
        .set("overflow-y", "auto");

    // Header del drawer
    HorizontalLayout drawerHeader = new HorizontalLayout();
    drawerHeader.setWidthFull();
    drawerHeader.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    drawerHeader.setAlignItems(FlexComponent.Alignment.CENTER);

    H2 drawerTitle = new H2("Formulario de Tarifario");
    drawerTitle.addClassName("bbva-page-title");
    drawerTitle.getStyle().set("margin", "0");

    btnCerrarDrawer.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
    btnCerrarDrawer.addClickListener(e -> toggleDrawer());
    Tooltip.forComponent(btnCerrarDrawer).withText("Cerrar formulario");

    drawerHeader.add(drawerTitle, btnCerrarDrawer);

    // Contenido del formulario
    VerticalLayout formContent = createFormContent();

    drawer.add(drawerHeader, formContent);
    drawer.setFlexGrow(1, formContent);

    return drawer;
  }

  private VerticalLayout createFormContent() {
    VerticalLayout formContent = new VerticalLayout();
    formContent.setSpacing(true);
    formContent.setPadding(false);

    // Configurar campos del formulario usando clases globales
    txtBin.setLabel("BIN:");
    txtBin.addClassName("bbva-form-field");
    txtBin.setMaxLength(6);

    cmbMarca.setLabel("Marca:");
    cmbMarca.addClassName("bbva-form-field");
    cmbMarca.setItems("Visa", "Mastercard", "American Express");

    cmbPrograma.setLabel("Programa:");
    cmbPrograma.addClassName("bbva-form-field");
    cmbPrograma.setItems("Millas para viajar", "Puntos para comprar");

    txtTcea.setLabel("Rango TCEA:");
    txtTcea.addClassName("bbva-form-field");

    txtMembresia.setLabel("Membresia:");
    txtMembresia.addClassName("bbva-form-field");

    txtEecc.setLabel("EECC:");
    txtEecc.addClassName("bbva-form-field");

    txtSeguro.setLabel("Seguro:");
    txtSeguro.addClassName("bbva-form-field");

    txtImagen.setLabel("Imagen:");
    txtImagen.addClassName("bbva-form-field");

    cmbOrientacion.setLabel("Orientación:");
    cmbOrientacion.addClassName("bbva-form-field");
    cmbOrientacion.setItems("Horizontal", "Vertical");

    txtDescripcion.setLabel("Descripción:");
    txtDescripcion.addClassName("bbva-form-field");

    txtObservacion.setLabel("Observación:");
    txtObservacion.addClassName("bbva-form-field");
    txtObservacion.setHeight("80px");

    cmbFlagTdh.setLabel("Flag TDH:");
    cmbFlagTdh.addClassName("bbva-form-field");
    cmbFlagTdh.setItems("SI", "NO");

    txtImagenTdh.setLabel("Imagen TDH:");
    txtImagenTdh.addClassName("bbva-form-field");

    txtNombreTdh.setLabel("Nombre TDH:");
    txtNombreTdh.addClassName("bbva-form-field");

    cmbOrientacionTdh.setLabel("Orientación TDH:");
    cmbOrientacionTdh.addClassName("bbva-form-field");
    cmbOrientacionTdh.setItems("Horizontal", "Vertical");

    cmbEstado.setLabel("Estado:");
    cmbEstado.addClassName("bbva-form-field");
    cmbEstado.setItems(ACTIVO, INACTIVO);
    cmbEstado.setValue(ACTIVO);

    txtRangoTcea.setLabel("Rango TCEA:");
    txtRangoTcea.setWidth("100%");

    txtBono.setLabel("Bono:");
    txtBono.setWidth("100%");

    txtConsumoBono.setLabel("Consumo para Bono:");
    txtConsumoBono.setWidth("100%");

    txtDiasBono.setLabel("Días para bono:");
    txtDiasBono.setWidth("100%");

    txtConsumoMembresia.setLabel("Consumo para exonerar membresía:");
    txtConsumoMembresia.setWidth("100%");

    txtDescripcionZp.setLabel("Descripción Zona Pública:");
    txtDescripcionZp.setWidth("100%");

    // Configurar botón usando clase global
    btnCrear.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnCrear.addClassName("bbva-button");
    btnCrear.addClickListener(e -> crearTarifario());

    formContent.add(
        txtBin,
        cmbMarca,
        cmbPrograma,
        txtTcea,
        txtMembresia,
        txtEecc,
        txtSeguro,
        txtImagen,
        cmbOrientacion,
        txtDescripcion,
        txtObservacion,
        cmbFlagTdh,
        txtImagenTdh,
        txtNombreTdh,
        cmbOrientacionTdh,
        cmbEstado,
        txtRangoTcea,
        txtBono,
        txtConsumoBono,
        txtDiasBono,
        txtConsumoMembresia,
        txtDescripcionZp,
        btnCrear);

    return formContent;
  }

  private void toggleDrawer() {
    drawerVisible = !drawerVisible;
    if (drawerVisible) {
      drawerPanel.getStyle().set("right", "0");
      btnNuevo.setText("Cerrar");
      btnNuevo.setIcon(VaadinIcon.CLOSE.create());
    } else {
      drawerPanel.getStyle().set("right", "-450px");
      btnNuevo.setText("Nuevo");
      btnNuevo.setIcon(VaadinIcon.PLUS.create());
    }
  }

  private VerticalLayout createFormCard() {
    VerticalLayout formCard = new VerticalLayout();
    formCard.addClassName("bbva-grid-card");
    formCard.setSpacing(true);
    formCard.setPadding(true);

    H2 formTitle = new H2("Formulario de Tarifario");
    formTitle.addClassName("bbva-page-title");

    // Configurar campos del formulario usando clases globales
    txtBin.setLabel("BIN:");
    txtBin.addClassName("bbva-form-field");
    txtBin.setMaxLength(6);

    cmbMarca.setLabel("Marca:");
    cmbMarca.addClassName("bbva-form-field");
    cmbMarca.setItems("Visa", "Mastercard", "American Express");

    cmbPrograma.setLabel("Programa:");
    cmbPrograma.addClassName("bbva-form-field");
    cmbPrograma.setItems("Millas para viajar", "Puntos para comprar");

    txtTcea.setLabel("Rango TCEA:");
    txtTcea.addClassName("bbva-form-field");

    txtMembresia.setLabel("Membresia:");
    txtMembresia.addClassName("bbva-form-field");

    txtEecc.setLabel("EECC:");
    txtEecc.addClassName("bbva-form-field");

    txtSeguro.setLabel("Seguro:");
    txtSeguro.addClassName("bbva-form-field");

    txtImagen.setLabel("Imagen:");
    txtImagen.addClassName("bbva-form-field");

    cmbOrientacion.setLabel("Orientación:");
    cmbOrientacion.addClassName("bbva-form-field");
    cmbOrientacion.setItems("Horizontal", "Vertical");

    txtDescripcion.setLabel("Descripción:");
    txtDescripcion.addClassName("bbva-form-field");

    txtObservacion.setLabel("Observación:");
    txtObservacion.addClassName("bbva-form-field");
    txtObservacion.setHeight("80px");

    cmbFlagTdh.setLabel("Flag TDH:");
    cmbFlagTdh.addClassName("bbva-form-field");
    cmbFlagTdh.setItems("SI", "NO");

    txtImagenTdh.setLabel("Imagen TDH:");
    txtImagenTdh.addClassName("bbva-form-field");

    txtNombreTdh.setLabel("Nombre TDH:");
    txtNombreTdh.addClassName("bbva-form-field");

    cmbOrientacionTdh.setLabel("Orientación TDH:");
    cmbOrientacionTdh.addClassName("bbva-form-field");
    cmbOrientacionTdh.setItems("Horizontal", "Vertical");

    cmbEstado.setLabel("Estado:");
    cmbEstado.addClassName("bbva-form-field");
    cmbEstado.setItems(ACTIVO, INACTIVO);
    cmbEstado.setValue(ACTIVO);

    txtRangoTcea.setLabel("Rango TCEA:");
    txtRangoTcea.setWidth("100%");

    txtBono.setLabel("Bono:");
    txtBono.setWidth("100%");

    txtConsumoBono.setLabel("Consumo para Bono:");
    txtConsumoBono.setWidth("100%");

    txtDiasBono.setLabel("Días para bono:");
    txtDiasBono.setWidth("100%");

    txtConsumoMembresia.setLabel("Consumo para exonerar membresía:");
    txtConsumoMembresia.setWidth("100%");

    txtDescripcionZp.setLabel("Descripción Zona Pública:");
    txtDescripcionZp.setWidth("100%");

    // Configurar botón usando clase global
    btnCrear.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnCrear.addClassName("bbva-button");
    btnCrear.addClickListener(e -> crearTarifario());

    formCard.add(
        formTitle,
        txtBin,
        cmbMarca,
        cmbPrograma,
        txtTcea,
        txtMembresia,
        txtEecc,
        txtSeguro,
        txtImagen,
        cmbOrientacion,
        txtDescripcion,
        txtObservacion,
        cmbFlagTdh,
        txtImagenTdh,
        txtNombreTdh,
        cmbOrientacionTdh,
        cmbEstado,
        txtRangoTcea,
        txtBono,
        txtConsumoBono,
        txtDiasBono,
        txtConsumoMembresia,
        txtDescripcionZp,
        btnCrear);

    return formCard;
  }

  private VerticalLayout createGridCard() {
    VerticalLayout gridCard = new VerticalLayout();
    gridCard.setSizeFull();
    gridCard.addClassName("bbva-grid-card");
    gridCard.setSpacing(true);
    gridCard.setPadding(true);

    configureGrid();

    // Crear controles de paginación
    HorizontalLayout paginationControls = createPaginationControls();

    gridCard.add(grid, paginationControls);
    gridCard.setFlexGrow(1, grid);

    return gridCard;
  }

  private void configureGrid() {
    grid.setSizeFull();
    grid.addClassName("bbva-grid");

    // Configurar todas las columnas con ancho automático basado en el header
    grid.addColumn(this::getEstadoDisplay).setHeader("Estado").setSortable(true).setAutoWidth(true);

    grid.addColumn(TcTarifario::getPrioridad)
        .setHeader("Priorid.")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(TcTarifario::getBin).setHeader("BIN").setSortable(true).setAutoWidth(true);

    grid.addColumn(this::getMarcaDisplay).setHeader("Marca").setSortable(true).setAutoWidth(true);

    grid.addColumn(this::getProgramaDisplay)
        .setHeader("Programa Lealtad")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(
            tarifario -> tarifario.getRango_tcea() != null ? tarifario.getRango_tcea() + "%" : "")
        .setHeader("TCEA")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(
            tarifario -> tarifario.getMembresia() != null ? "S/ " + tarifario.getMembresia() : "")
        .setHeader("Membresia")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(tarifario -> tarifario.getEecc() != null ? "S/ " + tarifario.getEecc() : "")
        .setHeader("EECC")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(
            tarifario ->
                tarifario.getSeg_desgravamen() != null
                    ? "S/ " + tarifario.getSeg_desgravamen()
                    : "")
        .setHeader("Seg. Desgravamen")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(TcTarifario::getImagen)
        .setHeader("Imagen de la tarjeta (URL)")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0); // Evita que se expanda demasiado

    grid.addColumn(this::getOrientacionDisplay)
        .setHeader("Orientacion")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(TcTarifario::getFuncion_tagueo)
        .setHeader("Descripcion")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(1); // Permite expansión para texto largo

    grid.addColumn(TcTarifario::getObservacion)
        .setHeader("Observación")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(1); // Permite expansión para texto largo

    grid.addColumn(this::getFlagTarjetaHinchaDisplay)
        .setHeader("Flag Tarj. Hincha")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(TcTarifario::getImagen_tarjeta_hincha)
        .setHeader("Imagen Tarj. Hincha")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(0); // Evita que se expanda demasiado

    grid.addColumn(TcTarifario::getNombre_tarjeta_hincha)
        .setHeader("Nombre Tarj. Hincha")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(this::getOrientacionTarjetaHinchaDisplay)
        .setHeader("Orientación Tarj. Hincha")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(TcTarifario::getBono).setHeader("Bono").setSortable(true).setAutoWidth(true);

    grid.addColumn(TcTarifario::getConsumoBono)
        .setHeader("Consumo para Bono")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(TcTarifario::getDiasBono)
        .setHeader("Días para bono")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(TcTarifario::getConsumoExoneracionMembresia)
        .setHeader("Consumo exonerar membresía")
        .setSortable(true)
        .setAutoWidth(true);

    grid.addColumn(TcTarifario::getDescripcion_zona_publica)
        .setHeader("Descripción Zona Pública")
        .setSortable(true)
        .setAutoWidth(true)
        .setFlexGrow(1); // Permite expansión para texto largo

    // Configurar selección
    grid.setSelectionMode(Grid.SelectionMode.SINGLE);
    grid.addSelectionListener(
        event -> {
          event
              .getFirstSelectedItem()
              .ifPresent(
                  tarifario -> {
                    loadTarifarioToForm(tarifario);
                    if (!drawerVisible) {
                      toggleDrawer(); // Abrir drawer automáticamente al seleccionar
                    }
                  });
        });
  }

  private void configureComponents() {
    // Configurar listeners y validaciones adicionales si es necesario
  }

  private HorizontalLayout createPaginationControls() {
    HorizontalLayout paginationLayout = new HorizontalLayout();
    paginationLayout.addClassName("bbva-pagination");
    paginationLayout.setWidthFull();
    paginationLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    paginationLayout.setAlignItems(FlexComponent.Alignment.CENTER);

    // Información de página
    pageInfo = new Span();
    pageInfo.addClassName("bbva-pagination-info");

    // Controles de navegación
    HorizontalLayout controls = new HorizontalLayout();
    controls.addClassName("bbva-pagination-controls");
    controls.setSpacing(true);

    previousButton = new Button("Anterior", VaadinIcon.ANGLE_LEFT.create());
    previousButton.addClassName("bbva-pagination-button");
    previousButton.addClickListener(e -> goToPreviousPage());

    nextButton = new Button("Siguiente", VaadinIcon.ANGLE_RIGHT.create());
    nextButton.addClassName("bbva-pagination-button");
    nextButton.addClickListener(e -> goToNextPage());

    controls.add(previousButton, nextButton);
    paginationLayout.add(pageInfo, controls);

    return paginationLayout;
  }

  private void updatePagination() {
    totalPages = (int) Math.ceil((double) allTarifarios.size() / pageSize);

    // Calcular índices
    int startIndex = currentPage * pageSize;
    int endIndex = Math.min(startIndex + pageSize, allTarifarios.size());

    // Obtener elementos de la página actual
    List<TcTarifario> currentPageItems = allTarifarios.subList(startIndex, endIndex);
    grid.setItems(currentPageItems);

    // Actualizar información de página
    if (allTarifarios.isEmpty()) {
      pageInfo.setText("No hay elementos");
    } else {
      int displayStart = startIndex + 1;
      pageInfo.setText(
          String.format(
              "Mostrando %d-%d de %d elementos (Página %d de %d)",
              displayStart, endIndex, allTarifarios.size(), currentPage + 1, totalPages));
    }

    // Actualizar estado de botones
    previousButton.setEnabled(currentPage > 0);
    nextButton.setEnabled(currentPage < totalPages - 1);
  }

  private void goToPreviousPage() {
    if (currentPage > 0) {
      currentPage--;
      updatePagination();
    }
  }

  private void goToNextPage() {
    if (currentPage < totalPages - 1) {
      currentPage++;
      updatePagination();
    }
  }

  private void crearTarifario() {
    if (validarCampos()) {
      TcTarifario nuevoTarifario = new TcTarifario();

      // Mapear campos del formulario al objeto
      nuevoTarifario.setBin(txtBin.getValue());
      nuevoTarifario.setRango_tcea(txtTcea.getValue());
      nuevoTarifario.setMembresia(txtMembresia.getValue());
      nuevoTarifario.setEecc(txtEecc.getValue());
      nuevoTarifario.setSeg_desgravamen(txtSeguro.getValue());
      nuevoTarifario.setImagen(txtImagen.getValue());
      nuevoTarifario.setFuncion_tagueo(txtDescripcion.getValue());
      nuevoTarifario.setObservacion(txtObservacion.getValue());
      nuevoTarifario.setImagen_tarjeta_hincha(txtImagenTdh.getValue());
      nuevoTarifario.setNombre_tarjeta_hincha(txtNombreTdh.getValue());
      nuevoTarifario.setBono(txtBono.getValue());
      nuevoTarifario.setConsumoBono(txtConsumoBono.getValue());
      nuevoTarifario.setDiasBono(txtDiasBono.getValue());
      nuevoTarifario.setConsumoExoneracionMembresia(txtConsumoMembresia.getValue());
      nuevoTarifario.setDescripcion_zona_publica(txtDescripcionZp.getValue());

      // Crear objetos Parametro para los combos
      if (cmbMarca.getValue() != null) {
        Parametro marca = new Parametro();
        marca.setNombre(cmbMarca.getValue());
        nuevoTarifario.setTcMarcaTarjeta(marca);
      }

      if (cmbPrograma.getValue() != null) {
        Parametro programa = new Parametro();
        programa.setNombre(cmbPrograma.getValue());
        nuevoTarifario.setTcProgramaLealtad(programa);
      }

      if (cmbEstado.getValue() != null) {
        Parametro estado = new Parametro();
        estado.setNombre(cmbEstado.getValue());
        nuevoTarifario.setEstado(estado);
      }

      // Agregar a la lista
      allTarifarios.add(nuevoTarifario);
      updatePagination();
      limpiarFormulario();

      // Cerrar drawer después de crear
      if (drawerVisible) {
        toggleDrawer();
      }

      Notification.show("Tarifario creado exitosamente")
          .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
    }
  }

  private boolean validarCampos() {
    if (txtBin.getValue() == null || txtBin.getValue().trim().isEmpty()) {
      Notification.show("El BIN es requerido").addThemeVariants(NotificationVariant.LUMO_ERROR);
      return false;
    }

    if (cmbMarca.getValue() == null) {
      Notification.show("La marca es requerida").addThemeVariants(NotificationVariant.LUMO_ERROR);
      return false;
    }

    if (cmbPrograma.getValue() == null) {
      Notification.show("El programa es requerido")
          .addThemeVariants(NotificationVariant.LUMO_ERROR);
      return false;
    }

    return true;
  }

  private void loadTarifarioToForm(TcTarifario tarifario) {
    txtBin.setValue(tarifario.getBin() != null ? tarifario.getBin() : "");
    txtTcea.setValue(tarifario.getRango_tcea() != null ? tarifario.getRango_tcea() : "");
    txtMembresia.setValue(tarifario.getMembresia() != null ? tarifario.getMembresia() : "");
    txtEecc.setValue(tarifario.getEecc() != null ? tarifario.getEecc() : "");
    txtSeguro.setValue(
        tarifario.getSeg_desgravamen() != null ? tarifario.getSeg_desgravamen() : "");
    txtImagen.setValue(tarifario.getImagen() != null ? tarifario.getImagen() : "");
    txtDescripcion.setValue(
        tarifario.getFuncion_tagueo() != null ? tarifario.getFuncion_tagueo() : "");
    txtObservacion.setValue(tarifario.getObservacion() != null ? tarifario.getObservacion() : "");

    if (tarifario.getTcMarcaTarjeta() != null) {
      cmbMarca.setValue(tarifario.getTcMarcaTarjeta().getNombre());
    }

    if (tarifario.getTcProgramaLealtad() != null) {
      cmbPrograma.setValue(tarifario.getTcProgramaLealtad().getNombre());
    }

    if (tarifario.getEstado() != null) {
      cmbEstado.setValue(tarifario.getEstado().getNombre());
    }

    btnCrear.setText("Actualizar");
  }

  private void limpiarFormulario() {
    txtBin.clear();
    cmbMarca.clear();
    cmbPrograma.clear();
    txtTcea.clear();
    txtMembresia.clear();
    txtEecc.clear();
    txtSeguro.clear();
    txtImagen.clear();
    cmbOrientacion.clear();
    txtDescripcion.clear();
    txtObservacion.clear();
    cmbFlagTdh.clear();
    txtImagenTdh.clear();
    txtNombreTdh.clear();
    cmbOrientacionTdh.clear();
    cmbEstado.setValue(ACTIVO);
    txtRangoTcea.clear();
    txtBono.clear();
    txtConsumoBono.clear();
    txtDiasBono.clear();
    txtConsumoMembresia.clear();
    txtDescripcionZp.clear();

    btnCrear.setText("Crear");
    grid.deselectAll();
  }

  private void loadSampleData() {
    // Datos de ejemplo basados en la imagen
    allTarifarios.add(
        createTarifarioItem(
            ACTIVO, "5", "414068", "Visa", "Millas para viajar", "121.76", "330", "10", "0"));
    allTarifarios.add(
        createTarifarioItem(
            INACTIVO,
            "95",
            "414089",
            "Visa",
            "Millas para viajar",
            "121.42",
            "300",
            "10",
            "0.256"));
    allTarifarios.add(
        createTarifarioItem(
            ACTIVO, "9", "404293", "Visa", "Puntos para comprar", "88.45", "0", "20", "0"));
    allTarifarios.add(
        createTarifarioItem(
            ACTIVO, "1", "414075", "Visa", "Puntos para comprar", "127.96", "500", "20", "0"));
    allTarifarios.add(
        createTarifarioItem(
            ACTIVO, "6", "491909", "Visa", "Puntos para comprar", "100.89", "75", "20", "0"));
    allTarifarios.add(
        createTarifarioItem(
            ACTIVO, "95", "491931", "Visa", "Puntos para comprar", "88.35", "0", "0", "0"));
    allTarifarios.add(
        createTarifarioItem(
            ACTIVO, "4", "491914", "Visa", "Puntos para comprar", "103.57", "280", "20", "0"));
    allTarifarios.add(
        createTarifarioItem(
            INACTIVO,
            "2",
            "414791",
            "Visa",
            "Puntos para comprar",
            "103.44",
            "350",
            "20",
            "0.256"));

    updatePagination();
  }

  private TcTarifario createTarifarioItem(
      String estado,
      String prioridad,
      String bin,
      String marca,
      String programa,
      String tcea,
      String membresia,
      String eecc,
      String seguro) {
    TcTarifario tarifario = new TcTarifario();
    tarifario.setBin(bin);
    tarifario.setPrioridad(prioridad);
    tarifario.setRango_tcea(tcea);
    tarifario.setMembresia(membresia);
    tarifario.setEecc(eecc);
    tarifario.setSeg_desgravamen(seguro);

    // Crear parámetros
    Parametro estadoParam = new Parametro();
    estadoParam.setNombre(estado);
    tarifario.setEstado(estadoParam);

    Parametro marcaParam = new Parametro();
    marcaParam.setNombre(marca);
    tarifario.setTcMarcaTarjeta(marcaParam);

    Parametro programaParam = new Parametro();
    programaParam.setNombre(programa);
    tarifario.setTcProgramaLealtad(programaParam);

    return tarifario;
  }

  // Métodos auxiliares para mostrar datos en el grid
  private String getEstadoDisplay(TcTarifario tarifario) {
    return tarifario.getEstado() != null ? tarifario.getEstado().getNombre() : "";
  }

  private String getMarcaDisplay(TcTarifario tarifario) {
    return tarifario.getTcMarcaTarjeta() != null ? tarifario.getTcMarcaTarjeta().getNombre() : "";
  }

  private String getProgramaDisplay(TcTarifario tarifario) {
    return tarifario.getTcProgramaLealtad() != null
        ? tarifario.getTcProgramaLealtad().getNombre()
        : "";
  }

  private String getOrientacionDisplay(TcTarifario tarifario) {
    return (tarifario.getOrientacion() == null)
        ? ""
        : (tarifario.getOrientacion().getId() == null)
            ? ""
            : tarifario.getOrientacion().getId().toString();
  }

  private String getFlagTarjetaHinchaDisplay(TcTarifario tarifario) {
    return (tarifario.getFlag_tarjeta_hincha() == null)
        ? ""
        : (tarifario.getFlag_tarjeta_hincha().getId() == null)
            ? ""
            : tarifario.getFlag_tarjeta_hincha().getId().toString();
  }

  private String getOrientacionTarjetaHinchaDisplay(TcTarifario tarifario) {
    return (tarifario.getOrientacion_tarjeta_hincha() == null)
        ? ""
        : (tarifario.getOrientacion_tarjeta_hincha().getId() == null)
            ? ""
            : tarifario.getOrientacion_tarjeta_hincha().getId().toString();
  }
}
