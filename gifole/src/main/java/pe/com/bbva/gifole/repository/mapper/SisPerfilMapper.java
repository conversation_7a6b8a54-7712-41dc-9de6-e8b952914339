package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.SisPerfil;

@Component
public class SisPerfilMapper implements RowMapper<SisPerfil> {

  @Override
  public SisPerfil mapRow(ResultSet rs, int i) throws SQLException {
    SisPerfil sisPerfil = new SisPerfil();

    sisPerfil.setId(rs.getLong("ID"));
    sisPerfil.setDescripcion(rs.getString("DESCRIPCION"));
    sisPerfil.setEstado(rs.getString("ESTADO"));

    return sisPerfil;
  }
}
