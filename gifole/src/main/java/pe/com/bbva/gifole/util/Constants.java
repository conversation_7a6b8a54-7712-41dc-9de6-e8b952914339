package pe.com.bbva.gifole.util;

import java.util.ResourceBundle;

public interface Constants {

  String VALOR = "valores";
  ResourceBundle rg = ResourceBundle.getBundle(VALOR);

  String MODO_PERFIL = rg.getString("modo.perfil");
  String PASS_GIFOLE_WS = rg.getString("pass.gifole.ws");

  String RUTA_GIFOLE_WS = rg.getString("ruta.gifole.ws");
  String RUTA_GIFOLE_WS_INCENTIVACION_TOC = rg.getString("ruta.gifole.ws.incentivacion.toc");
  String RUTA_GIFOLE_WS_INCENTIVACION_TA = rg.getString("ruta.gifole.ws.incentivacion.ta");

  // Se agrega ruta generica para servicio gifole ws
  String RUTA_GIFOLE_WS_TARJETA_DEL_HINCHA =
      rg.getString("servicios.rest.gifole.ws.tarjeta.hincha");
  String RUTA_GIFOLE_WS_GESTOR_CAMPANIAS = rg.getString("servicios.rest.gifole.ws.gestor.campania");
  String RUTA_GIFOLE_WS_SEGURO_VIDA_RENTA = rg.getString("servicios.rest.gifole.ws.seg.vida.renta");
  String RUTA_GIFOLE_WS_CAMPANIAS_MULTIPLICA =
      rg.getString("servicios.rest.gifole.ws.campania.multiplica");
  String RUTA_GIFOLE_WS_BANNER_CROSS_SELL =
      rg.getString("servicios.rest.gifole.ws.banner.cross.sell");
  String RUTA_GIFOLE_WS_PRESTAMO_VEHICULAR =
      rg.getString("servicios.rest.gifole.ws.consultar.prestamo.vehicular");

  // Cancelacion de productos
  String BUZON_CANCELACION_PRODUCTOS = rg.getString("cancelacion.productos.buzon.envio.mail");
  String RUTA_GIFOLE_WS_CANCELACION_PRODUCTO =
      rg.getString("servicios.rest.gifole.ws.consultar.cancelacion.productos");
  String RUTA_GIFOLE_WS_CANCELACION_ACTUALIZAR_PRODUCTO =
      rg.getString("servicios.rest.gifole.ws.actualizar.cancelacion.productos");
  String RUTA_GIFOLE_WS_MOTIVOS_RECHAZO = rg.getString("servicios.rest.gifole.ws.motivos.rechazo");

  // Cambio Modalidad EECC
  String BUZON_CAMBIO_MODALIDAD_EECC = rg.getString("cambio.modalidad.eecc.buzon.envio.mail");
  String RUTA_GIFOLE_WS_CAMBIO_MODALIDAD_EECC_CONSULTAR =
      rg.getString("servicios.rest.gifole.ws.consultar.cambio.modalidad.eecc");
  String RUTA_GIFOLE_WS_CAMBIO_MODALIDAD_EECC_ACTUALIZAR_PRODUCTO =
      rg.getString("servicios.rest.gifole.ws.actualizar.cambio.modalidad.eecc");
  String RUTA_GIFOLE_WS_CAMBIO_MODALIDAD_EECC_MOTIVOS_APROBACION =
      rg.getString("servicios.rest.gifole.ws.motivos.aprobacion.cambio.modalidad.ecc");

  // SEMAFORO DIGITAL
  String RUTA_GIFOLE_WS_SEMAFORO_DIGITAL_CONSULTAR =
      rg.getString("servicios.rest.gifole.ws.consultar.semaforo.digital");

  // Raspa y Gana
  String RUTA_GIFOLE_WS_RASPA_Y_GANA_CONSULTAR =
      rg.getString("servicios.rest.gifole.ws.consultar.raspa.y.gana");
  String RUTA_GIFOLE_WS_RASPA_Y_GANA_CONSULTAR_TOTALES =
      rg.getString("servicios.rest.gifole.ws.consultar.totales.raspa.y.gana");

  // URL ARCHIVOS
  String RUTA_REPO_ARCHIVOS = rg.getString("repositorio.url");

  // Seguro Vehicular Royal
  String RUTA_GIFOLE_WS_SEGURO_VEHICULAR_ROYAL =
      rg.getString("servicios.rest.gifole.ws.consultar.seguro.vehicular.royal");
  String RUTA_GIFOLE_WS_VENTA_SEGURO_VEHICULAR_ROYAL =
      rg.getString("servicios.rest.gifole.ws.consultar.venta.seguro.vehicular.royal");

  // Seguro Hogar Royal
  String RUTA_GIFOLE_WS_SEGURO_HOGAR_ROYAL =
      rg.getString("servicios.rest.gifole.ws.consultar.seguro.hogar.royal");

  // Seguro Negocio a tu Medida Royal
  String RUTA_GIFOLE_WS_SEGURO_NEGOCIO_A_TU_MEDIDA_ROYAL =
      rg.getString("servicios.rest.gifole.ws.consultar.seguro.negocioatumedida.royal");

  // Tarjeta Garantizada
  String RUTA_GIFOLE_WS_CONSULTAR_TARJETA_GARANTIZADA =
      rg.getString("servicios.rest.gifole.ws.consultar.tarjeta.garantizada");

  // Tarjeta Upgrade
  String RUTA_GIFOLE_WS_CONSULTAR_TARJETA_UPGRADE =
      rg.getString("servicios.rest.gifole.ws.consultar.tarjeta.upgrade");
  String RUTA_GIFOLE_WS_REGISTRAR_BENEFICIOS_UPGRADE =
      rg.getString("servicios.rest.gifole.ws.registrar.beneficios.upgrade");
  String RUTA_GIFOLE_WS_CONSULTAR_BENEFICIOS_UPGRADE =
      rg.getString("servicios.rest.gifole.ws.consultar.beneficios.upgrade");
  String RUTA_GIFOLE_WS_ACTUALIZAR_BENEFICIOS_UPGRADE =
      rg.getString("servicios.rest.gifole.ws.actualizar.beneficios.upgrade");
  String RUTA_GIFOLE_WS_ELIMINAR_BENEFICIOS_UPGRADE =
      rg.getString("servicios.rest.gifole.ws.eliminar.beneficios.upgrade");

  // Seguro Vida Royal
  String RUTA_GIFOLE_WS_SEGURO_VIDA_ROYAL =
      rg.getString("servicios.rest.gifole.ws.consultar.seguro.vida.royal");

  // producto seguro
  String RUTA_GIFOLE_WS_SEGURO_PRODUCTO =
      rg.getString("servicios.rest.gifole.ws.consultar.seguro.producto");
  // Seguro Desempleo
  String RUTA_GIFOLE_WS_SEGURO_ROYAL =
      rg.getString("servicios.rest.gifole.ws.consultar.seguro.royal");

  // Consulta lead Adquirencia - Mercurio
  String RUTA_GIFOLE_WS_MERCURIO_CONSULTAR =
      rg.getString("servicios.rest.gifole.ws.consultar.mercurio");

  // Seguro Negocio a tu Medida Royal
  String RUTA_GIFOLE_WS_SEGURO_VEHICULAR_BZNP_ROYAL_OPENPAY =
      rg.getString("servicios.rest.gifole.ws.consultar.seguro.vehicular.royal.openpay");

  String RUTA_LDAP_GLOBAL = rg.getString("ldap.global.url");
  String RUTA_LDAP_GLOBAL_ROLES = rg.getString("ldap.global.rama.roles");
  String RUTA_LDAP_GLOBAL_USUARIOS = rg.getString("ldap.global.rama.usuarios");
}
