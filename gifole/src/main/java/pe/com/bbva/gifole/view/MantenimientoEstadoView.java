package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.domain.Estado;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Mantenimiento de Estados")
@Route(value = "mantenimiento-estado", layout = MainLayout.class)
public class MantenimientoEstadoView extends VerticalLayout {

  public MantenimientoEstadoView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Mantenimiento de Estados");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    DataTable<Estado> dataTable =
        DataTable.<Estado>builder()
            .id("tabla-estados")
            .column("id", "Id", estado -> String.valueOf(estado.getId()), "80px")
            .column(
                "nombre", "Nombre", estado -> StringUtils.trimToEmpty(estado.getNombre()), "180px")
            .column("creacion", "Creación", estado -> estado.getCreacion(), "150px")
            .column(
                "creador",
                "Creador",
                estado -> StringUtils.trimToEmpty(estado.getCreador()),
                "120px")
            .column(
                "observacion",
                "Observación",
                estado -> StringUtils.trimToEmpty(estado.getObservacion()),
                "200px")
            .column(
                "textopedido",
                "Texto Pedido",
                estado -> StringUtils.trimToEmpty(estado.getTextopedido()),
                "200px")
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
