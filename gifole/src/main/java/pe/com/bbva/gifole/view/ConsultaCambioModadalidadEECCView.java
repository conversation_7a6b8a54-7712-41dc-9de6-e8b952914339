package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.Arrays;
import java.util.List;
import pe.com.bbva.gifole.common.bean.CambioModalidadEECCDetalleBean;
import pe.com.bbva.gifole.util.GifoleUtil;
import pe.com.bbva.gifole.util.Utilitario;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Cambio Modalidad EECC")
@Route(value = "reporte/tarjetas/consulta-cambio-modalidad-eecc", layout = MainLayout.class)
public class ConsultaCambioModadalidadEECCView extends VerticalLayout {

  public ConsultaCambioModadalidadEECCView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Cambio Modalidad EECC");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Datos de ejemplo para CambioModalidadEECCBean
    List<CambioModalidadEECCDetalleBean> listaDeDetalles = Arrays.asList();

    // Construir el DataTable usando el Builder
    DataTable<CambioModalidadEECCDetalleBean> dataTable =
        DataTable.<CambioModalidadEECCDetalleBean>builder()
            .id("tabla-cambio-modalidad")
            .column(
                "codigoCentral",
                "Código Central",
                detalle -> detalle.getCambioModalidadEECCBean().getCodigoCentral())
            .column(
                "nombresApellidos",
                "Nombre Cliente",
                detalle -> detalle.getCambioModalidadEECCBean().getNombreCompletoCliente())
            .column(
                "tipoDocumento",
                "Tipo Documento",
                detalle -> detalle.getCambioModalidadEECCBean().getTipoDocumento())
            .column(
                "numeroDocumento",
                "Número de Documento",
                detalle -> detalle.getCambioModalidadEECCBean().getNroDocumento())
            .column(
                "fechaHoraRegistro",
                "Fecha Registro",
                detalle -> Utilitario.formatearFecha(detalle.getCreacion(), "dd/MM/yyyy HH:mm"))
            .column(
                "subProducto",
                "Sub Producto",
                detalle -> detalle.getCambioModalidadEECCBean().getSubProducto())
            .column(
                "nroContrato",
                "Número de contrato",
                detalle ->
                    GifoleUtil.obtenerNumeroCuentaFormateado(
                        detalle.getCambioModalidadEECCBean().getNroContrato()))
            .column(
                "nroTarjetaTitular",
                "Número tarjeta titular",
                detalle -> detalle.getCambioModalidadEECCBean().getNroTarjetaTitular())
            .column(
                "correoContacto",
                "Correo de Contacto",
                detalle -> detalle.getCambioModalidadEECCBean().getCorreoClienteContacto())
            .column(
                "tipoModalidadEnvio",
                "Nueva Modalidad de envío",
                detalle -> detalle.getCambioModalidadEECCBean().getTipoModalidadEnvio())
            .column(
                "direccionEntrega",
                "Dirección de entrega",
                detalle -> detalle.getCambioModalidadEECCBean().getDireccionEntrega())
            .column(
                "correoEntrega",
                "Correo de entrega",
                detalle -> detalle.getCambioModalidadEECCBean().getCorreoClienteEntrega())
            .column(
                "estado",
                "Estado Solicitud",
                detalle -> detalle.getCambioModalidadEECCBean().getEstado())
            .column(
                "motivoAprobacion",
                "Detalle de procesado",
                detalle -> detalle.getCambioModalidadEECCBean().getMotivoProcesado())
            .column(
                "otroMotivoRechazo",
                "Motivo Rechazado",
                detalle -> detalle.getCambioModalidadEECCBean().getOtroMotivoRechazo())
            .column(
                "fechaHoraModificacion",
                "Fecha Modificación",
                detalle ->
                    detalle.getEdicion() != null
                        ? Utilitario.formatearFecha(detalle.getEdicion(), "dd/MM/yyyy HH:mm")
                        : "")
            .column("registroModificacion", "Registro Externo", detalle -> detalle.getEditor())
            .column("canal", "Canal", detalle -> detalle.getCambioModalidadEECCBean().getCanal())
            // Datos
            .items(listaDeDetalles) // List<CambioModalidadEECCDetalleBean>
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
