package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.CancelacionAutomaticaBean;

@SuppressWarnings("rawtypes")
@Component
public class CancelacionAutomaticaMapper implements RowMapper {
  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    CancelacionAutomaticaBean param = new CancelacionAutomaticaBean();
    param.setId(rs.getLong("ID"));
    param.setCodCentral(rs.getString("COD_CENTRAL"));
    param.setSubProducto(rs.getString("SUB_PRODUCTO"));
    param.setNroContrato(rs.getString("NUM_CONTRATO"));
    param.setCanal(rs.getString("CANAL"));

    return param;
  }
}
