package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

import pe.com.bbva.gifole.common.bean.DepositoPlazoFijoBean;
import pe.com.bbva.gifole.util.GifoleUtil;
import pe.com.bbva.gifole.util.Util;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Lead Depósito Plazo Fijo")
@Route(value = "reporte/leads/deposito-plazo-fijo", layout = MainLayout.class)
public class ConsultaLeadDepositoPlazoFijoView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<DepositoPlazoFijoBean> allData = new ArrayList<>();
    private DataTable<DepositoPlazoFijoBean> dataTable;

    public ConsultaLeadDepositoPlazoFijoView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Lead Depósito Plazo Fijo");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<DepositoPlazoFijoBean>builder()
                .id("tabla-lead-deposito-plazo-fijo") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaLeadDepositoPlazoFijoUI)
                .column("codigoCentral", "Código Central", 
                    bean -> StringUtils.trimToEmpty(bean.getCodigoCentral()), "100px")
                .column("nombres", "Nombres", 
                    bean -> StringUtils.trimToEmpty(bean.getNombres()), "200px")
                .column("correo", "Correo", 
                    bean -> StringUtils.trimToEmpty(bean.getCorreo()), "200px")
                .column("idContrato", "ID Contrato", 
                    bean -> StringUtils.trimToEmpty(bean.getIdContrato()), "100px")
                .column("tipoMoneda", "Tipo Moneda", 
                    bean -> StringUtils.trimToEmpty(bean.getTipoMoneda()), "100px")
                .column("montoInvertir", "Monto a Invertir", 
                    bean -> StringUtils.trimToEmpty(bean.getMontoInvertir()), "120px")
                .column("periodo", "Periodo", 
                    bean -> StringUtils.trimToEmpty(bean.getPeriodo()), "100px")
                .column("periodoSimulacion", "Periodo Simulación", 
                    bean -> StringUtils.trimToEmpty(bean.getPeriodoSimulacion()), "130px")
                .column("treaSimulacion", "TREA Simulación", 
                    bean -> StringUtils.trimToEmpty(bean.getTreaSimulacion()), "120px")
                .column("gananciaSimulacion", "Ganancia Simulación", 
                    bean -> StringUtils.trimToEmpty(bean.getGananciaSimulacion()), "140px")
                .column("nroCuentaCargo", "N° Cuenta Cargo", 
                    bean -> GifoleUtil.ofuscarCuenta(StringUtils.trimToEmpty(bean.getNroCuentaCargo()), 4, 4), "150px")
                .column("nroCuentaCargoInteres", "N° Cuenta Cargo Interés", 
                    bean -> GifoleUtil.ofuscarCuenta(StringUtils.trimToEmpty(bean.getNroCuentaCargoInteres()), 4, 4), "170px")
                .column("indRenovacion", "Ind. Renovación", 
                    bean -> StringUtils.trimToEmpty(bean.getIndRenovacion()), "120px")
                .column("fechaInicio", "Fecha Inicio", 
                    bean -> bean.getFechaInicio1(), "130px")
                .column("fechaVencimiento", "Fecha Vencimiento", 
                    bean -> bean.getFechaVencimiento1(), "130px")
                .column("indLpdp", "Ind. LPDP", 
                    bean -> StringUtils.trimToEmpty(bean.getIndLpdp()).equals("1") ? "SI" : "NO", "100px")
                .column("estado", "Estado", 
                    bean -> bean.getEstado(), "100px")
                .column("fechaCreacion", "Fecha Creación", 
                    bean -> Util.convertirFechaHoraAString(bean.getFechaCreacion()), "130px")
                .column("canal", "Canal", 
                    bean -> bean.getCanal(), "100px")
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

    // Método para cargar datos, puedes conectarlo a un servicio
    private void loadData(List<DepositoPlazoFijoBean> data) {
        // Limpiar datos existentes
        allData.clear();
        allData.addAll(data); // Asumiendo que 'data' viene del servicio

        // Actualizar la tabla con los nuevos datos
        dataTable.setItems(allData);
    }

}