package pe.com.bbva.gifole.common.enums;

public enum SubCanalEnum {
  KONECTA("05", "KONECTA"),
  GSS("02", "GSS"),
  EJECUTIVO_REMOTO("10", "EJECUTIVO REMOTO"),
  DEFECTO("", "");

  private String codigo;
  private String subcanal;

  private SubCanalEnum(String codigo, String subcanal) {
    this.codigo = codigo;
    this.subcanal = subcanal;
  }

  public String getCodigo() {
    return codigo;
  }

  public String getSubcanal() {
    return subcanal;
  }

  public static SubCanalEnum obtenerPorCodigo(String codigo) {
    SubCanalEnum[] values = SubCanalEnum.values();
    for (int i = 0; i < values.length; i++) {
      if (values[i].getCodigo().equals(codigo)) {
        return values[i];
      }
    }
    return DEFECTO;
  }
}
