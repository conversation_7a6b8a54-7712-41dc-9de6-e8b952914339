package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.ReferidoDatoBean;

@SuppressWarnings("rawtypes")
@Component
public class ReferidorNotificacionMapper implements RowMapper {

  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    ReferidoDatoBean param = new ReferidoDatoBean();

    param.setId(rs.getLong("IDREFERIDOR"));
    param.setNombre(rs.getString("NOMBREREFERIDOR"));
    param.setCorreo(rs.getString("CORREOREFERIDOR"));
    param.setIdReferido(rs.getLong("IDREFERIDO"));
    param.setNombreCompletoRef(rs.getString("NOMBRESREFERIDO"));
    param.setEstado("ESTADOREFERIDO");
    param.setCantidadPremioRef(rs.getInt("CANTIDADPREMIO"));

    return param;
  }
}
