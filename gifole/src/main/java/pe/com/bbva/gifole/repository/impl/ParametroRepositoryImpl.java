package pe.com.bbva.gifole.repository.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaQuery;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;
import pe.com.bbva.gifole.domain.Parametro;
import pe.com.bbva.gifole.repository.ParametroRepository;
import pe.com.bbva.gifole.util.GifoleUtil;

@Repository
public class ParametroRepositoryImpl implements ParametroRepository {

  @PersistenceContext private EntityManager entityManager;

  public void crear(Parametro parametro) {
    parametro.setCreador(GifoleUtil.obtenerUsuarioSesion());
    parametro.setCreacion(new Date());
    entityManager.persist(parametro);
  }

  @Override
  public void actualizar(Parametro parametro) {
    parametro.setEditor(GifoleUtil.obtenerUsuarioSesion());
    parametro.setEdicion(new Date());
    entityManager.merge(parametro);
  }

  @Override
  public void eliminar(Long id) {
    Parametro parametro = entityManager.find(Parametro.class, id);
    if (parametro != null) {
      entityManager.remove(parametro);
    }
  }

  @Override
  public List<Parametro> buscarParametro(CriteriaQuery criteriaQuery) {
    TypedQuery<Parametro> query = entityManager.createQuery(criteriaQuery);
    // Sin límite de resultados como en el original (setMaxResults(0))
    return query.getResultList();
  }

  @Override
  public Parametro obtenerParametro(CriteriaQuery criteriaQuery) {
    TypedQuery<Parametro> query = entityManager.createQuery(criteriaQuery);
    query.setMaxResults(1);
    List<Parametro> parametros = query.getResultList();
    if (!parametros.isEmpty()) {
      return parametros.get(0);
    }
    return null;
  }
}
