package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "SUSCRIPCION_PROGRAMADA_FFMM")
public class SuscripcionProgramadaFFMM implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_SUSCRIPCION_PROGRAMADA_FFMM")
  @TableGenerator(
      name = "SEQ_SUSCRIPCION_PROGRAMADA_FFMM",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.SuscripcionProgramadaFFMM",
      allocationSize = 1)
  private Long id;

  @Column(name = "NOMBRE", length = 150)
  private String nombre;

  @Column(name = "CODIGO_CENTRAL", length = 8)
  private String codigoCentral;

  @Column(name = "CORREO", length = 80)
  private String correo;

  @Column(name = "NOMBRE_FONDO", length = 50)
  private String nombreFondo;

  @Column(name = "NUMERO_FONDO", length = 25)
  private String numeroFondo;

  @Column(name = "NOMBRE_CUENTA", length = 50)
  private String nombreCuenta;

  @Column(name = "NUMERO_CUENTA", length = 25)
  private String numeroCuenta;

  @Column(name = "PERIODO", length = 20)
  private String periodo;

  @Column(name = "DIACARGO", length = 20)
  private String diaCargo;

  @Column(name = "APORTE_PROGRAMADO", precision = 15, scale = 2)
  private BigDecimal aporteProgramado;

  @Column(name = "DIVISA", length = 20)
  private String divisa;

  @Column(name = "PROCESADO", length = 1)
  private String procesado;

  @Column(name = "FECHA_REGISTRO")
  private Date fechaRegistro;

  @Transient private String nombreEmail;

  @Transient private String fechaOperacion;

  @Transient private String tipoBanca;
}
