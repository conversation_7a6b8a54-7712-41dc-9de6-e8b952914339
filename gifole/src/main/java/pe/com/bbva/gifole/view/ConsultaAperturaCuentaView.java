package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import pe.com.bbva.gifole.common.bean.AperturaCuentaBean;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Apertura de Cuenta")
@Route(value = "reporte/cuentas/consulta-apertura-cuenta", layout = MainLayout.class)
public class ConsultaAperturaCuentaView extends VerticalLayout {

  public ConsultaAperturaCuentaView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Apertura de Cuenta");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Datos de ejemplo para AperturaCuentaBean
    List<AperturaCuentaBean> aperturas =
        Arrays.asList(
            createSampleApertura(
                1L, "AHORRO", "SOLES", "12345678901", "<EMAIL>", "ACTIVA"),
            createSampleApertura(
                2L, "CORRIENTE", "DOLARES", "12345678902", "<EMAIL>", "PENDIENTE"),
            createSampleApertura(
                3L, "AHORRO", "SOLES", "12345678903", "<EMAIL>", "ACTIVA"),
            createSampleApertura(
                4L, "CORRIENTE", "SOLES", "12345678904", "<EMAIL>", "CERRADA"),
            createSampleApertura(
                5L, "AHORRO", "DOLARES", "12345678905", "<EMAIL>", "ACTIVA"));

    // Construir el DataTable usando el Builder
    DataTable<AperturaCuentaBean> dataTable =
        DataTable.<AperturaCuentaBean>builder()
            .id("apertura-cuenta-table")
            .items(aperturas)
            .pageSize(4)
            .column("id", "ID", apertura -> String.valueOf(apertura.getId()), "10%")
            .column("tipoCuenta", "Tipo Cuenta", AperturaCuentaBean::getTipoCuenta, "15%")
            .column("tipoMoneda", "Moneda", AperturaCuentaBean::getTipoMoneda, "15%")
            .column("codigoCentral", "Código Central", AperturaCuentaBean::getCodigoCentral, "20%")
            .column("email", "Email", AperturaCuentaBean::getEmail, "25%")
            .column("estadoActual", "Estado", AperturaCuentaBean::getEstadoActual, "15%")
            .build();

    card.add(dataTable);
    return card;
  }

  private AperturaCuentaBean createSampleApertura(
      Long id,
      String tipoCuenta,
      String tipoMoneda,
      String codigoCentral,
      String email,
      String estado) {
    AperturaCuentaBean apertura = new AperturaCuentaBean();
    apertura.setId(id);
    apertura.setTipoCuenta(tipoCuenta);
    apertura.setTipoMoneda(tipoMoneda);
    apertura.setCodigoCentral(codigoCentral);
    apertura.setEmail(email);
    apertura.setEstadoActual(estado);
    apertura.setFechaRegistro(new Date());
    return apertura;
  }
}
