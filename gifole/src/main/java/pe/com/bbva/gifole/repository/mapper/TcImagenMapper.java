package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.ImagenesTcCu;

@Component
public class TcImagenMapper implements RowMapper<ImagenesTcCu> {

  @Override
  public ImagenesTcCu mapRow(ResultSet rs, int i) throws SQLException {

    ImagenesTcCu imagenesTcCu = new ImagenesTcCu();

    imagenesTcCu.setBin(rs.getString("BIN"));
    imagenesTcCu.setEstado(rs.getString("ESTADO"));
    imagenesTcCu.setId(rs.getString("ID"));
    imagenesTcCu.setNombreImagen(rs.getString("NOMBRE_IMAGEN"));

    return imagenesTcCu;
  }
}
