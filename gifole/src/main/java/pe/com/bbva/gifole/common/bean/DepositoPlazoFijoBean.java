package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class DepositoPlazoFijoBean implements Serializable {

  private static final long serialVersionUID = 8791971568992119458L;
  private Long id;
  private String idContrato;
  private String codigoCentral;
  private String nombres;
  private String correo;
  private String tipoMoneda;
  private String montoInvertir;
  private String periodo;
  private String periodoSimulacion;
  private String treaSimulacion;
  private String gananciaSimulacion;
  private String nroCuentaCargo;
  private String nroCuentaCargoInteres;
  private String indRenovacion;
  private String indOpcionRequisitos;
  private Date fechaInicio;
  private String fechaInicio1;
  private Date fechaVencimiento;
  private String fechaVencimiento1;
  private String indLpdp;
  private String estado;
  private String canal;
  private String creador;
  private Date fechaCreacion;
  private String editor;
  private Date fechaModificacion;
  private String fechaDeCreacion;
  private String fechaIniCreacion;
  private String fechaFinCreacion;
  private Date fechaIniOp;
  private Date fechaFinOp;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getIdContrato() {
    return idContrato;
  }

  public void setIdContrato(String idContrato) {
    this.idContrato = idContrato;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getNombres() {
    return nombres;
  }

  public void setNombres(String nombres) {
    this.nombres = nombres;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public String getTipoMoneda() {
    return tipoMoneda;
  }

  public void setTipoMoneda(String tipoMoneda) {
    this.tipoMoneda = tipoMoneda;
  }

  public String getMontoInvertir() {
    return montoInvertir;
  }

  public void setMontoInvertir(String montoInvertir) {
    this.montoInvertir = montoInvertir;
  }

  public String getPeriodo() {
    return periodo;
  }

  public void setPeriodo(String periodo) {
    this.periodo = periodo;
  }

  public String getPeriodoSimulacion() {
    return periodoSimulacion;
  }

  public void setPeriodoSimulacion(String periodoSimulacion) {
    this.periodoSimulacion = periodoSimulacion;
  }

  public String getTreaSimulacion() {
    return treaSimulacion;
  }

  public void setTreaSimulacion(String treaSimulacion) {
    this.treaSimulacion = treaSimulacion;
  }

  public String getGananciaSimulacion() {
    return gananciaSimulacion;
  }

  public void setGananciaSimulacion(String gananciaSimulacion) {
    this.gananciaSimulacion = gananciaSimulacion;
  }

  public String getNroCuentaCargo() {
    return nroCuentaCargo;
  }

  public void setNroCuentaCargo(String nroCuentaCargo) {
    this.nroCuentaCargo = nroCuentaCargo;
  }

  public String getNroCuentaCargoInteres() {
    return nroCuentaCargoInteres;
  }

  public void setNroCuentaCargoInteres(String nroCuentaCargoInteres) {
    this.nroCuentaCargoInteres = nroCuentaCargoInteres;
  }

  public String getIndRenovacion() {
    return indRenovacion;
  }

  public void setIndRenovacion(String indRenovacion) {
    this.indRenovacion = indRenovacion;
  }

  public String getIndOpcionRequisitos() {
    return indOpcionRequisitos;
  }

  public void setIndOpcionRequisitos(String indOpcionRequisitos) {
    this.indOpcionRequisitos = indOpcionRequisitos;
  }

  public Date getFechaInicio() {
    return fechaInicio;
  }

  public void setFechaInicio(Date fechaInicio) {
    this.fechaInicio = fechaInicio;
  }

  public String getFechaInicio1() {
    return fechaInicio1;
  }

  public void setFechaInicio1(String fechaInicio1) {
    this.fechaInicio1 = fechaInicio1;
  }

  public Date getFechaVencimiento() {
    return fechaVencimiento;
  }

  public void setFechaVencimiento(Date fechaVencimiento) {
    this.fechaVencimiento = fechaVencimiento;
  }

  public String getFechaVencimiento1() {
    return fechaVencimiento1;
  }

  public void setFechaVencimiento1(String fechaVencimiento1) {
    this.fechaVencimiento1 = fechaVencimiento1;
  }

  public String getIndLpdp() {
    return indLpdp;
  }

  public void setIndLpdp(String indLpdp) {
    this.indLpdp = indLpdp;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public String getCreador() {
    return creador;
  }

  public void setCreador(String creador) {
    this.creador = creador;
  }

  public Date getFechaCreacion() {
    return fechaCreacion;
  }

  public void setFechaCreacion(Date fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
  }

  public String getEditor() {
    return editor;
  }

  public void setEditor(String editor) {
    this.editor = editor;
  }

  public Date getFechaModificacion() {
    return fechaModificacion;
  }

  public void setFechaModificacion(Date fechaModificacion) {
    this.fechaModificacion = fechaModificacion;
  }

  public String getFechaDeCreacion() {
    return fechaDeCreacion;
  }

  public void setFechaDeCreacion(String fechaDeCreacion) {
    this.fechaDeCreacion = fechaDeCreacion;
  }

  public String getFechaIniCreacion() {
    return fechaIniCreacion;
  }

  public void setFechaIniCreacion(String fechaIniCreacion) {
    this.fechaIniCreacion = fechaIniCreacion;
  }

  public String getFechaFinCreacion() {
    return fechaFinCreacion;
  }

  public void setFechaFinCreacion(String fechaFinCreacion) {
    this.fechaFinCreacion = fechaFinCreacion;
  }

  public Date getFechaIniOp() {
    return fechaIniOp;
  }

  public void setFechaIniOp(Date fechaIniOp) {
    this.fechaIniOp = fechaIniOp;
  }

  public Date getFechaFinOp() {
    return fechaFinOp;
  }

  public void setFechaFinOp(Date fechaFinOp) {
    this.fechaFinOp = fechaFinOp;
  }
}
