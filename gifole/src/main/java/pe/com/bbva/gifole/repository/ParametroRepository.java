package pe.com.bbva.gifole.repository;

import jakarta.persistence.criteria.CriteriaQuery;
import java.util.List;
import pe.com.bbva.gifole.domain.Parametro;

public interface ParametroRepository {

  void crear(Parametro parametro);

  void actualizar(Parametro parametro);

  void eliminar(Long id);

  List<Parametro> buscarParametro(CriteriaQuery criteriaQuery);

  Parametro obtenerParametro(CriteriaQuery criteriaQuery);
}
