package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.domain.PrestamoVehicular;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Lead Préstamo Vehicular")
@Route(value = "reporte/leads/prestamo-vehicular", layout = MainLayout.class)
public class ConsultaLeadPrestamoVehicularView extends VerticalLayout {

    // Formato de fecha para mostrar en la tabla
    private static final SimpleDateFormat FORMATO_FECHA = new SimpleDateFormat("dd/MM/yyyy HH:mm");

    private final List<PrestamoVehicular> allData = new ArrayList<>();
    private DataTable<PrestamoVehicular> dataTable;

    public ConsultaLeadPrestamoVehicularView() {
        setSizeFull();
        addClassName("app-main");
        setPadding(true);
        setSpacing(true);

        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Título
        H2 title = new H2("Consulta Lead Préstamo Vehicular");
        title.addClassName("bbva-grid-title"); // Ajustado el estilo del título

        // DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(title, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo (opcional)
        // loadSampleData();
    }

    private VerticalLayout createTableCard() {
        VerticalLayout card = new VerticalLayout();
        card.setSizeFull();
        card.addClassName("bbva-grid-card");
        card.setSpacing(true);
        card.setPadding(true);

        // Construir el DataTable usando el Builder, similar a las vistas anteriores
        dataTable =
            DataTable.<PrestamoVehicular>builder()
                .id("tabla-lead-prestamo-vehicular") // ID único para la tabla
                // Mapeo de columnas basado en la UI anterior (ConsultaLeadPrestamoVehicularUI)
                .column("codigoCentral", "Código Central", 
                    bean -> StringUtils.trimToEmpty(bean.getCodigoCentral()), "100px")
                .column("tipoDocumento", "Tipo Documento", 
                    bean -> StringUtils.trimToEmpty(bean.getTipoDocumento()), "120px")
                .column("numDocumento", "Número Documento", 
                    bean -> StringUtils.trimToEmpty(bean.getNumDocumento()), "120px")
                .column("nombres", "Nombres", 
                    bean -> StringUtils.trimToEmpty(bean.getNombres()), "150px")
                .column("paterno", "Apellido Paterno", 
                    bean -> StringUtils.trimToEmpty(bean.getPaterno()), "150px")
                .column("materno", "Apellido Materno", 
                    bean -> StringUtils.trimToEmpty(bean.getMaterno()), "150px")
                .column("correo", "Correo", 
                    bean -> StringUtils.trimToEmpty(bean.getCorreo()), "200px")
                .column("telefono", "Teléfono", 
                    bean -> StringUtils.trimToEmpty(bean.getTelefono()), "100px")
                .column("importeTotal", "Importe Total", 
                    bean -> StringUtils.trimToEmpty(bean.getImporteTotal()), "120px")
                .column("divisaTotal", "Divisa Total", 
                    bean -> StringUtils.trimToEmpty(bean.getDivisaTotal()), "100px")
                .column("importeCuota", "Importe Cuota", 
                    bean -> StringUtils.trimToEmpty(bean.getImporteCuota()), "120px")
                .column("divisaCuota", "Divisa Cuota", 
                    bean -> StringUtils.trimToEmpty(bean.getDivisaCuota()), "100px")
                .column("plazo", "Plazo", 
                    bean -> StringUtils.trimToEmpty(bean.getPlazo()), "100px")
                .column("frecuencia", "Frecuencia", 
                    bean -> StringUtils.trimToEmpty(bean.getFrecuencia()), "100px")
                .column("tea", "TEA", 
                    bean -> StringUtils.trimToEmpty(bean.getTea()), "100px")
                .column("tcea", "TCEA", 
                    bean -> StringUtils.trimToEmpty(bean.getTcea()), "100px")
                .column("tipoVehiculo", "Tipo Vehículo", 
                    bean -> StringUtils.trimToEmpty(bean.getTipoVehiculo()), "120px")
                .column("canal", "Canal", 
                    bean -> StringUtils.trimToEmpty(bean.getCanal()), "100px")
                .column("fechaDeRegistro", "Fecha de Registro", 
                    bean -> StringUtils.trimToEmpty(bean.getFechaDeRegistro()), "150px")
                .items(new ArrayList<>())
                .pageSize(10)
                .build();

        card.add(dataTable);
        return card;
    }

}