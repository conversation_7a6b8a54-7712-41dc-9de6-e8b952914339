package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class StockBean implements Serializable {

  /** */
  private static final long serialVersionUID = -2848203856474034958L;

  private Long id;
  private String tipoMoneda;
  private String premio;
  private Date fechaInicio;
  private Date fechaInicio1;
  private Date fechaInicio2;
  private Date fechaFin;
  private Date fechaFin1;
  private Date fechaFin2;
  private String estado;
  private String canal;
  private Integer nroStock;
  private String nroStockB;
  private Integer stockInicial;
  private String stockInicialB;
  private String detallePremio;
  private String condicionPremio;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getTipoMoneda() {
    return tipoMoneda;
  }

  public void setTipoMoneda(String tipoMoneda) {
    this.tipoMoneda = tipoMoneda;
  }

  public String getPremio() {
    return premio;
  }

  public void setPremio(String premio) {
    this.premio = premio;
  }

  public Date getFechaInicio() {
    return fechaInicio;
  }

  public void setFechaInicio(Date fechaInicio) {
    this.fechaInicio = fechaInicio;
  }

  public Date getFechaFin() {
    return fechaFin;
  }

  public void setFechaFin(Date fechaFin) {
    this.fechaFin = fechaFin;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public Integer getNroStock() {
    return nroStock;
  }

  public void setNroStock(Integer nroStock) {
    this.nroStock = nroStock;
  }

  public String getDetallePremio() {
    return detallePremio;
  }

  public void setDetallePremio(String detallePremio) {
    this.detallePremio = detallePremio;
  }

  public String getCondicionPremio() {
    return condicionPremio;
  }

  public void setCondicionPremio(String condicionPremio) {
    this.condicionPremio = condicionPremio;
  }

  public Integer getStockInicial() {
    return stockInicial;
  }

  public void setStockInicial(Integer stockInicial) {
    this.stockInicial = stockInicial;
  }

  public Date getFechaInicio1() {
    return fechaInicio1;
  }

  public void setFechaInicio1(Date fechaInicio1) {
    this.fechaInicio1 = fechaInicio1;
  }

  public Date getFechaInicio2() {
    return fechaInicio2;
  }

  public void setFechaInicio2(Date fechaInicio2) {
    this.fechaInicio2 = fechaInicio2;
  }

  public Date getFechaFin1() {
    return fechaFin1;
  }

  public void setFechaFin1(Date fechaFin1) {
    this.fechaFin1 = fechaFin1;
  }

  public Date getFechaFin2() {
    return fechaFin2;
  }

  public void setFechaFin2(Date fechaFin2) {
    this.fechaFin2 = fechaFin2;
  }

  public String getNroStockB() {
    return nroStockB;
  }

  public void setNroStockB(String nroStockB) {
    this.nroStockB = nroStockB;
  }

  public String getStockInicialB() {
    return stockInicialB;
  }

  public void setStockInicialB(String stockInicialB) {
    this.stockInicialB = stockInicialB;
  }
}
