package pe.com.bbva.gifole.domain;

import java.io.Serializable;
import lombok.Data;

@Data
public class TcTarifario implements Serializable {

  private Long id;
  private String bin;
  private Parametro tcMarcaTarjeta;
  private Parametro tcProgramaLealtad;
  private String rango_tcea;
  private String membresia;
  private String eecc;
  private String seg_desgravamen;
  private String imagen;
  private Parametro orientacion;
  private Parametro estado;
  private String prioridad;
  private String funcion_tagueo;
  private String observacion;
  private Parametro flag_tarjeta_hincha;
  private String imagen_tarjeta_hincha;
  private String nombre_tarjeta_hincha;
  private Parametro orientacion_tarjeta_hincha;
  private String bono;
  private String consumoBono;
  private String diasBono;
  private String consumoExoneracionMembresia;
  private String descripcion_zona_publica;
}
