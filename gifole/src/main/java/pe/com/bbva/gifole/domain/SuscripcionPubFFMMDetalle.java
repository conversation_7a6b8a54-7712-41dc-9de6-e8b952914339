package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Entity
@Table(name = "SUSCRIPCION_PUB_FFMM_DETALLE")
public class SuscripcionPubFFMMDetalle implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_SUSCRIPCION_PUB_FFMM_DETALLE")
  @TableGenerator(
      name = "SEQ_SUSCRIPCION_PUB_FFMM_DETALLE",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.SuscripcionPubFFMMDetalle",
      allocationSize = 1)
  private Long id;

  @Column(name = "PERIODO", length = 20)
  private String periodo;

  @Column(name = "MONTO_FUTURO", length = 15, precision = 2)
  private BigDecimal montoFuturo;

  @Column(name = "MONTO_CUOTA", length = 15, precision = 2)
  private BigDecimal montoCuota;

  @Column(name = "MONTO_RENTABILIDAD", length = 15, precision = 2)
  private BigDecimal montoRentabilidad;

  // bi-directional many-to-one association to SuscripcionPubFFMM
  @ManyToOne
  @JoinColumn(name = "SUSCRIPCION_PUB_FFMM")
  private SuscripcionPubFFMM suscripcionPubFFMM;
}
