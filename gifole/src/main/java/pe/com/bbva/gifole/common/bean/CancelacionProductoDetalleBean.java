package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class CancelacionProductoDetalleBean implements Serializable {

  /** */
  private static final long serialVersionUID = -8264769460087582356L;

  private Long id;
  private CancelacionProductoBean cancelacionProductoBean;
  private Date creacion;
  private String creador;
  private Date edicion;
  private String editor;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public CancelacionProductoBean getCancelacionProductoBean() {
    return cancelacionProductoBean;
  }

  public void setCancelacionProductoBean(CancelacionProductoBean cancelacionProductoBean) {
    this.cancelacionProductoBean = cancelacionProductoBean;
  }

  public Date getCreacion() {
    return creacion;
  }

  public void setCreacion(Date creacion) {
    this.creacion = creacion;
  }

  public String getCreador() {
    return creador;
  }

  public void setCreador(String creador) {
    this.creador = creador;
  }

  public Date getEdicion() {
    return edicion;
  }

  public void setEdicion(Date edicion) {
    this.edicion = edicion;
  }

  public String getEditor() {
    return editor;
  }

  public void setEditor(String editor) {
    this.editor = editor;
  }
}
