package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import pe.com.bbva.gifole.domain.AfiliacionEECCFFMMDetalle;
import pe.com.bbva.gifole.util.Util;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Afiliación EECCFFMM")
@Route(value = "reporte/fondos-mutuos/consulta-afiliacion-eeccffmm", layout = MainLayout.class)
public class ConsultaAfiliacionEECCFFMMView extends VerticalLayout {

  // DataTable
  private DataTable<AfiliacionEECCFFMMDetalle> dataTable;

  public ConsultaAfiliacionEECCFFMMView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Afiliación EECCFFMM");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Datos de ejemplo para AfiliacionEECCFFMM
    List<AfiliacionEECCFFMMDetalle> afiliaciones =
        Arrays.asList(
            createSampleAfiliacion(1L, "12345678901", "<EMAIL>", "ACTIVO"),
            createSampleAfiliacion(2L, "12345678902", "<EMAIL>", "PENDIENTE"),
            createSampleAfiliacion(3L, "12345678903", "<EMAIL>", "ACTIVO"),
            createSampleAfiliacion(4L, "12345678904", "<EMAIL>", "RECHAZADO"),
            createSampleAfiliacion(5L, "12345678905", "<EMAIL>", "ACTIVO"));

    // Construir el DataTable usando el Builder
    // Ahora el DataTable detecta automáticamente que getFechaRegistro retorna Date
    // y aplica Util.convertirFechaAString automáticamente
    dataTable =
        DataTable.<AfiliacionEECCFFMMDetalle>builder()
            .id("afiliacion-eeccffmm-table")
            .items(afiliaciones)
            .pageSize(4)
            .column("id", "ID", afiliacion -> String.valueOf(afiliacion.getId()), "15%")
            .column(
                "fecha",
                "Fecha",
                afiliacionEECCFFMM ->
                    afiliacionEECCFFMM.getAfiliacionEECCFFMM().getFechaOperacion(),
                "20%")
            .column(
                "fechaRegistro",
                "Fecha Registro",
                afiliacionEECCFFMM ->
                    Util.convertirFechaAString(
                        afiliacionEECCFFMM.getAfiliacionEECCFFMM().getFechaRegistro()),
                "25%")
            .column(
                "codigoCentral",
                "Código Central",
                afiliacionEECCFFMMDetalle ->
                    afiliacionEECCFFMMDetalle.getAfiliacionEECCFFMM().getCodigoCentral(),
                "20%")
            .column("nombreFondo", "Email", AfiliacionEECCFFMMDetalle::getNombreFondo, "35%")
            .column(
                "numeroFondo",
                "Numero Fondo",
                afiliacionEECCFFMM -> afiliacionEECCFFMM.getNumeroContrato(),
                "25%")
            .build();

    card.add(dataTable);
    return card;
  }

  private AfiliacionEECCFFMMDetalle createSampleAfiliacion(
      Long id, String codigoCentral, String email, String estado) {
    AfiliacionEECCFFMMDetalle afiliacion = new AfiliacionEECCFFMMDetalle();
    afiliacion.setId(id);
    afiliacion.getAfiliacionEECCFFMM().setFechaRegistro(new Date());
    afiliacion.getAfiliacionEECCFFMM().setFechaOperacion("2023-01-01");
    afiliacion.setNombreFondo("Nombre FOndo Prueba");
    afiliacion.setNumeroContrato(codigoCentral);
    return afiliacion;
  }
}
