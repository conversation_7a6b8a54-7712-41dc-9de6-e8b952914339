package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.Parametro;
import pe.com.bbva.gifole.domain.TcTarifario;

@Component
public class TcBeneficioProductoMapper implements RowMapper<TcTarifario> {

  @Override
  public TcTarifario mapRow(ResultSet rs, int i) throws SQLException {

    TcTarifario tcTarifario = new TcTarifario();

    Parametro tcMarcaTarjeta = new Parametro();
    tcMarcaTarjeta.setNombre(rs.getString("MARCA"));

    tcTarifario.setBin(rs.getString("BIN"));
    tcTarifario.setFuncion_tagueo(rs.getString("DESCRIPCION"));

    tcTarifario.setTcMarcaTarjeta(tcMarcaTarjeta);

    return tcTarifario;
  }
}
