package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_CLIENTE")
public class VehCliente implements Serializable {

  @Id
  @Column(name = "TIPO_DOCUMENTO", length = 1)
  private String tipoDocumento;

  @Id
  @Column(name = "DOCUMENTO", length = 20)
  private String documento;

  @Column(name = "CODIGO_CENTRAL", length = 8)
  private String codigoCentral;

  @Column(name = "CODIGO_CAPA", length = 4)
  private String codigoCapa;
}
