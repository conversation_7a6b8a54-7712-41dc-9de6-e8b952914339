package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.TCAdicional;
import pe.com.bbva.gifole.domain.TCAdicionalDetEstado;
import pe.com.bbva.gifole.domain.TCAdicionalDetalle;
import pe.com.bbva.gifole.domain.TCEstado;
import pe.com.bbva.gifole.domain.TCMotivo;

@Component
public class AdicionalMapper implements RowMapper<TCAdicionalDetEstado> {

  @Override
  public TCAdicionalDetEstado mapRow(ResultSet rs, int i) throws SQLException {
    TCAdicionalDetEstado adicional = new TCAdicionalDetEstado();
    adicional.setTcAdicionalDetalle(new TCAdicionalDetalle());
    adicional.getTcAdicionalDetalle().setTcAdicional(new TCAdicional());
    adicional.getTcAdicionalDetalle().setEstadoActual(new TCEstado());
    adicional.setTcMotivo(new TCMotivo());
    adicional
        .getTcAdicionalDetalle()
        .getTcAdicional()
        .setCodigoCentral(rs.getString("CODIGO_CENTRAL"));
    adicional.getTcAdicionalDetalle().getTcAdicional().setNombre(rs.getString("NOMBRE"));
    adicional
        .getTcAdicionalDetalle()
        .getTcAdicional()
        .setTarjetaTitular(rs.getString("TARJETA_TITULAR"));
    adicional
        .getTcAdicionalDetalle()
        .getTcAdicional()
        .setNombreTarjeta(rs.getString("NOMBRE_TARJETA"));
    adicional.getTcAdicionalDetalle().getTcAdicional().setDivisa(rs.getString("DIVISA"));
    adicional.getTcAdicionalDetalle().getTcAdicional().setCorreo(rs.getString("CORREO"));
    adicional.getTcAdicionalDetalle().getTcAdicional().setDireccionAdi(rs.getString("DIRECCION"));
    adicional.getTcAdicionalDetalle().setDireccionEntrega(rs.getString("DIRECCION_ENTREGA"));
    adicional.getTcAdicionalDetalle().setOficinaRecojo(rs.getString("OFICINA_RECOJO"));
    adicional
        .getTcAdicionalDetalle()
        .getTcAdicional()
        .setFechaRegistro(rs.getTimestamp("FECHA_REGISTRO"));
    adicional.getTcAdicionalDetalle().setId(rs.getLong("id"));
    adicional.getTcAdicionalDetalle().setNombres(rs.getString("adicional"));
    adicional.getTcAdicionalDetalle().setTipoDocumento(rs.getString("TIPO_DOCUMENTO"));
    adicional.getTcAdicionalDetalle().setDocumento(rs.getString("DOCUMENTO"));
    adicional.getTcAdicionalDetalle().setLimiteCredito(rs.getInt("LIMITE_CREDITO"));
    adicional.getTcAdicionalDetalle().setVinculo(rs.getString("VINCULO"));
    adicional.getTcAdicionalDetalle().setOcupacion(rs.getString("OCUPACION"));
    adicional.getTcAdicionalDetalle().setCodCliente(rs.getString("COD_CLIENTE"));
    adicional.getTcAdicionalDetalle().setNroContrato(rs.getString("NRO_CONTRATO"));
    adicional.getTcAdicionalDetalle().setNroTarjeta(rs.getString("NRO_TARJETA"));
    adicional.getTcAdicionalDetalle().setTipoCliente(rs.getString("TIPO_CLIENTE"));
    adicional.getTcAdicionalDetalle().getEstadoActual().setNombre(rs.getString("ESTADO"));
    adicional.getTcAdicionalDetalle().setFechaModificacion(rs.getTimestamp("FECHA_MODIFICACION"));
    adicional.getTcMotivo().setNombre(rs.getString("MOTIVO"));
    adicional.setObservacion(rs.getString("OBSERVACION"));
    adicional.getTcAdicionalDetalle().setCombo(rs.getString("COMBO"));
    adicional.getTcAdicionalDetalle().getTcAdicional().setCanal(rs.getString("CANAL"));

    return adicional;
  }
}
