package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H3;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/** Ejemplo de uso del componente DataTable personalizado */
@Route("datatable-example")
public class DataTableExample extends VerticalLayout {

  public DataTableExample() {
    setSizeFull();
    setPadding(true);
    setSpacing(true);

    // Crear datos de ejemplo
    List<PersonaEjemplo> personas = createSampleData();

    // Ejemplo 1: Usando headers simples (método tradicional)
    Map<String, String> headers = new LinkedHashMap<>();
    headers.put("id", "ID");
    headers.put("nombre", "Nombre");
    headers.put("email", "Email");
    headers.put("departamento.nombre", "Departamento");
    headers.put("activo", "Estado");

    DataTable<PersonaEjemplo> dataTable1 =
        new DataTable<>("personas-table-1", headers, personas, 5);

    // Ejemplo 2: Usando configuración de columnas con anchos
    Map<String, DataTable.ColumnConfig> columns = new LinkedHashMap<>();
    columns.put("id", new DataTable.ColumnConfig("id", "ID", "80px"));
    columns.put("nombre", new DataTable.ColumnConfig("nombre", "Nombre Completo", "200px"));
    columns.put("email", new DataTable.ColumnConfig("email", "Correo Electrónico", "250px"));
    columns.put(
        "departamento.nombre",
        new DataTable.ColumnConfig("departamento.nombre", "Departamento", "150px"));
    columns.put("activo", new DataTable.ColumnConfig("activo", "Estado", "100px"));

    DataTable<PersonaEjemplo> dataTable2 =
        DataTable.withColumns("personas-table-2", columns, personas, 5);

    add(new H3("Tabla con Headers Simples"));
    add(dataTable1);
    add(new H3("Tabla con Anchos de Columna"));
    add(dataTable2);
  }

  /** Crea datos de ejemplo para la tabla */
  private List<PersonaEjemplo> createSampleData() {
    List<PersonaEjemplo> personas = new ArrayList<>();

    // Crear departamentos
    Departamento sistemas = new Departamento("Sistemas");
    Departamento rrhh = new Departamento("Recursos Humanos");
    Departamento ventas = new Departamento("Ventas");
    Departamento marketing = new Departamento("Marketing");

    // Crear personas
    personas.add(new PersonaEjemplo(1L, "Juan Pérez", "<EMAIL>", sistemas, true));
    personas.add(new PersonaEjemplo(2L, "María García", "<EMAIL>", rrhh, true));
    personas.add(new PersonaEjemplo(3L, "Carlos López", "<EMAIL>", ventas, false));
    personas.add(new PersonaEjemplo(4L, "Ana Martínez", "<EMAIL>", marketing, true));
    personas.add(
        new PersonaEjemplo(5L, "Pedro Rodríguez", "<EMAIL>", sistemas, true));
    personas.add(new PersonaEjemplo(6L, "Laura Sánchez", "<EMAIL>", rrhh, false));
    personas.add(new PersonaEjemplo(7L, "Miguel Torres", "<EMAIL>", ventas, true));
    personas.add(new PersonaEjemplo(8L, "Carmen Ruiz", "<EMAIL>", marketing, true));
    personas.add(new PersonaEjemplo(9L, "David Moreno", "<EMAIL>", sistemas, false));
    personas.add(new PersonaEjemplo(10L, "Isabel Jiménez", "<EMAIL>", rrhh, true));
    personas.add(
        new PersonaEjemplo(11L, "Francisco Herrera", "<EMAIL>", ventas, true));
    personas.add(
        new PersonaEjemplo(12L, "Pilar Navarro", "<EMAIL>", marketing, false));

    return personas;
  }

  /** Clase de ejemplo para representar una persona */
  public static class PersonaEjemplo {
    private Long id;
    private String nombre;
    private String email;
    private Departamento departamento;
    private boolean activo;

    public PersonaEjemplo(
        Long id, String nombre, String email, Departamento departamento, boolean activo) {
      this.id = id;
      this.nombre = nombre;
      this.email = email;
      this.departamento = departamento;
      this.activo = activo;
    }

    // Getters
    public Long getId() {
      return id;
    }

    public String getNombre() {
      return nombre;
    }

    public String getEmail() {
      return email;
    }

    public Departamento getDepartamento() {
      return departamento;
    }

    public boolean isActivo() {
      return activo;
    }

    public String getActivo() {
      return activo ? "Activo" : "Inactivo";
    }

    // Setters
    public void setId(Long id) {
      this.id = id;
    }

    public void setNombre(String nombre) {
      this.nombre = nombre;
    }

    public void setEmail(String email) {
      this.email = email;
    }

    public void setDepartamento(Departamento departamento) {
      this.departamento = departamento;
    }

    public void setActivo(boolean activo) {
      this.activo = activo;
    }
  }

  /** Clase de ejemplo para representar un departamento */
  public static class Departamento {
    private String nombre;

    public Departamento(String nombre) {
      this.nombre = nombre;
    }

    public String getNombre() {
      return nombre;
    }

    public void setNombre(String nombre) {
      this.nombre = nombre;
    }
  }
}
