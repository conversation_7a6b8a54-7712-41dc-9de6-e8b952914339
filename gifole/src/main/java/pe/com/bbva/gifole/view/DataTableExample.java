package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.html.H3;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import java.util.List;

/**
 * Ejemplo de uso del componente DataTable OPTIMIZADO Migrado para usar interfaces funcionales en
 * lugar de reflection
 */
@Route("datatable-example")
public class DataTableExample extends VerticalLayout {

  public DataTableExample() {
    setSizeFull();
    setPadding(true);
    setSpacing(true);

    add(new H2("🚀 DataTable Optimizado - Ejemplo de Migración"));

    // Crear datos de ejemplo
    List<PersonaEjemplo> personas = createSampleData();

    // 🚀 ENFOQUE OPTIMIZADO: Usando DataTableOptimized con Builder Pattern
    add(new H3("✅ DataTable Optimizado (Máximo Rendimiento)"));

    DataTableOptimized<PersonaEjemplo> optimizedTable =
        DataTableOptimized.<PersonaEjemplo>builder()
            .id("personas-optimized")
            .column("id", "ID", persona -> persona.getId().toString(), "80px")
            .column("nombre", "Nombre Completo", PersonaEjemplo::getNombre, "200px")
            .column("email", "Correo Electrónico", PersonaEjemplo::getEmail, "250px")
            .column(
                "departamento",
                "Departamento",
                persona -> persona.getDepartamento().getNombre(),
                "150px")
            .column(
                "estado",
                "Estado",
                persona -> persona.isActivo() ? "✅ Activo" : "❌ Inactivo",
                "100px")
            .items(personas)
            .pageSize(5)
            .build();

    add(optimizedTable);

    // 🎯 EJEMPLO AVANZADO: Con transformaciones complejas
    add(new H3("🎯 Ejemplo Avanzado con Transformaciones"));

    DataTableOptimized<PersonaEjemplo> advancedTable =
        DataTableOptimized.<PersonaEjemplo>builder()
            .id("personas-advanced")
            .column("id", "ID", persona -> String.format("#%03d", persona.getId()), "80px")
            .column("nombre", "Nombre", PersonaEjemplo::getNombre, "180px")
            .column("email", "Email", persona -> persona.getEmail().toLowerCase(), "220px")
            .column(
                "departamento",
                "Depto",
                persona -> persona.getDepartamento().getNombre().toUpperCase(),
                "120px")
            .column(
                "estado",
                "Estado",
                persona -> {
                  return persona.isActivo() ? "🟢 ACTIVO" : "🔴 INACTIVO";
                },
                "100px")
            .column(
                "info",
                "Info Completa",
                persona -> {
                  return String.format(
                      "%s (%s) - %s",
                      persona.getNombre(),
                      persona.getDepartamento().getNombre(),
                      persona.isActivo() ? "Activo" : "Inactivo");
                },
                "300px")
            .items(personas)
            .pageSize(3)
            .build();

    add(advancedTable);
  }

  /** Crea datos de ejemplo para la tabla */
  private List<PersonaEjemplo> createSampleData() {
    List<PersonaEjemplo> personas = new ArrayList<>();

    // Crear departamentos
    Departamento sistemas = new Departamento("Sistemas");
    Departamento rrhh = new Departamento("Recursos Humanos");
    Departamento ventas = new Departamento("Ventas");
    Departamento marketing = new Departamento("Marketing");

    // Crear personas
    personas.add(new PersonaEjemplo(1L, "Juan Pérez", "<EMAIL>", sistemas, true));
    personas.add(new PersonaEjemplo(2L, "María García", "<EMAIL>", rrhh, true));
    personas.add(new PersonaEjemplo(3L, "Carlos López", "<EMAIL>", ventas, false));
    personas.add(new PersonaEjemplo(4L, "Ana Martínez", "<EMAIL>", marketing, true));
    personas.add(
        new PersonaEjemplo(5L, "Pedro Rodríguez", "<EMAIL>", sistemas, true));
    personas.add(new PersonaEjemplo(6L, "Laura Sánchez", "<EMAIL>", rrhh, false));
    personas.add(new PersonaEjemplo(7L, "Miguel Torres", "<EMAIL>", ventas, true));
    personas.add(new PersonaEjemplo(8L, "Carmen Ruiz", "<EMAIL>", marketing, true));
    personas.add(new PersonaEjemplo(9L, "David Moreno", "<EMAIL>", sistemas, false));
    personas.add(new PersonaEjemplo(10L, "Isabel Jiménez", "<EMAIL>", rrhh, true));
    personas.add(
        new PersonaEjemplo(11L, "Francisco Herrera", "<EMAIL>", ventas, true));
    personas.add(
        new PersonaEjemplo(12L, "Pilar Navarro", "<EMAIL>", marketing, false));

    return personas;
  }

  /** Clase de ejemplo para representar una persona */
  public static class PersonaEjemplo {
    private Long id;
    private String nombre;
    private String email;
    private Departamento departamento;
    private boolean activo;

    public PersonaEjemplo(
        Long id, String nombre, String email, Departamento departamento, boolean activo) {
      this.id = id;
      this.nombre = nombre;
      this.email = email;
      this.departamento = departamento;
      this.activo = activo;
    }

    // Getters
    public Long getId() {
      return id;
    }

    public String getNombre() {
      return nombre;
    }

    public String getEmail() {
      return email;
    }

    public Departamento getDepartamento() {
      return departamento;
    }

    public boolean isActivo() {
      return activo;
    }

    public String getActivo() {
      return activo ? "Activo" : "Inactivo";
    }

    // Setters
    public void setId(Long id) {
      this.id = id;
    }

    public void setNombre(String nombre) {
      this.nombre = nombre;
    }

    public void setEmail(String email) {
      this.email = email;
    }

    public void setDepartamento(Departamento departamento) {
      this.departamento = departamento;
    }

    public void setActivo(boolean activo) {
      this.activo = activo;
    }
  }

  /** Clase de ejemplo para representar un departamento */
  public static class Departamento {
    private String nombre;

    public Departamento(String nombre) {
      this.nombre = nombre;
    }

    public String getNombre() {
      return nombre;
    }

    public void setNombre(String nombre) {
      this.nombre = nombre;
    }
  }
}
