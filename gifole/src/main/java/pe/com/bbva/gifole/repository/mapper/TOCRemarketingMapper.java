package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.TCTocRemarketing;

@Component
public class TOCRemarketingMapper implements RowMapper<TCTocRemarketing> {

  @Override
  public TCTocRemarketing mapRow(ResultSet rs, int i) throws SQLException {

    TCTocRemarketing tcTocRemarketing = new TCTocRemarketing();
    tcTocRemarketing.setId(rs.getLong("ID"));
    tcTocRemarketing.setCodigoCentral(rs.getString("CODIGOCENTRAL"));
    tcTocRemarketing.setTipoDocumento(rs.getString("TIPODOCUMENTO"));
    tcTocRemarketing.setNumeroDocumento(rs.getString("NUMERODOCUMENTO"));
    tcTocRemarketing.setNombreCompleto(rs.getString("NOMBRECOMPLETO"));
    tcTocRemarketing.setTelefono(rs.getString("TELEFONO"));
    tcTocRemarketing.setCorreo(rs.getString("CORREO"));
    tcTocRemarketing.setFechaRegistro(rs.getTimestamp("FECHAREGISTRO"));
    tcTocRemarketing.setNombreTarjeta(rs.getString("NOMBRETARJETA"));
    tcTocRemarketing.setTipoTarjeta(rs.getString("TIPOTARJETA"));
    tcTocRemarketing.setMontoLimite(rs.getString("LIMITETARJETA"));
    tcTocRemarketing.setTea(rs.getString("TEA"));
    tcTocRemarketing.setTcea(rs.getString("TCEA"));
    tcTocRemarketing.setTipoInformacion(rs.getString("TIPOINFORMACION"));
    tcTocRemarketing.setCanal(rs.getString("CANAL"));
    tcTocRemarketing.setFechaPago(rs.getString("FECHAPAGO"));
    tcTocRemarketing.setPasoAbandono(rs.getString("PASOABANDONO"));

    String marcaPh = StringUtils.trimToEmpty(rs.getString("MARCA_PH"));
    if (marcaPh.equalsIgnoreCase("1")) {
      marcaPh = "SI";
    } else {
      marcaPh = "NO";
    }
    tcTocRemarketing.setMarcaPh(marcaPh);

    return tcTocRemarketing;
  }
}
