package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "TC_BENEFICIOS_TARJETA")
public class TCBeneficiosTarjeta implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_TC_BENEFICIOS_TARJETA")
  @TableGenerator(
      name = "SEQ_TC_BENEFICIOS_TARJETA",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.TCBeneficiosTarjeta",
      allocationSize = 1)
  private Long id;

  @Column(name = "TARJETA", length = 30)
  private String tarjeta;

  @Column(name = "BIN", length = 6)
  private String bin;

  @Column(name = "MARCA", length = 20)
  private String marca;

  @Column(name = "PROGRAMA_LEALTAD", length = 20)
  private String programaLealtad;

  @Column(name = "TCEA", length = 10)
  private String tcea;

  @Column(name = "MEMBRESIA", length = 10)
  private String membresia;

  @Column(name = "EECC", length = 10)
  private String eecc;

  @Column(name = "SEG_DESGRAVAMEN", length = 10)
  private String segDesgravamen;

  @Column(name = "CREADOR", length = 10)
  private String creador;

  @Column(name = "FECHA_REGISTRO")
  private Date fechaRegistro;

  @Column(name = "EDITOR", length = 10)
  private String editor;

  @Column(name = "FECHA_MODIFICACION")
  private Date fechaModificacion;

  @Column(name = "ESTADO", length = 1)
  private String estado;
}
