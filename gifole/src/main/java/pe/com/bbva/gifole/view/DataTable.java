package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.Component;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.html.*;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Componente DataTable reutilizable e independiente para Vaadin 24 Crea una tabla HTML nativa con
 * paginación personalizada
 *
 * @param <T> Tipo de objeto que contendrá la tabla
 */
@CssImport("./themes/bbva/components/custom-datatable.css")
public class DataTable<T> extends VerticalLayout {

  private final String id;
  private final Map<String, String> headers;
  private final List<T> allItems;
  private final int pageSize;

  // Componentes de la tabla
  private final Div tableContainer;
  private final NativeButton previousButton;
  private final NativeButton nextButton;
  private final Span pageInfo;

  // Estado de paginación
  private int currentPage = 0;
  private int totalPages = 0;
  private List<T> currentPageItems;

  /**
   * Constructor del DataTable
   *
   * @param id ID único para el componente
   * @param headers Mapa de headers donde key=campo del objeto, value=título a mostrar
   * @param items Lista de items a mostrar
   * @param pageSize Número de items por página
   */
  public DataTable(String id, Map<String, String> headers, List<T> items, int pageSize) {
    this.id = id;
    this.headers = headers;
    this.allItems = new ArrayList<>(items);
    this.pageSize = pageSize;
    this.currentPageItems = new ArrayList<>();

    // Configurar el contenedor principal
    setId(id);
    setWidthFull();
    setHeight("auto");
    setSpacing(false);
    setPadding(false);
    setMargin(false);
    addClassName("bbva-custom-datatable");

    // Crear componentes
    this.tableContainer = new Div();
    this.previousButton = new NativeButton("Anterior");
    this.nextButton = new NativeButton("Siguiente");
    this.pageInfo = new Span();

    // Inicializar componentes
    initializeComponents();
    updatePagination();

    // Agregar componentes al layout
    add(tableContainer, createPaginationLayout());
  }

  /** Inicializa los componentes de la tabla y paginación */
  private void initializeComponents() {
    // Configurar contenedor de tabla
    tableContainer.addClassName("table-container");
    tableContainer.setWidthFull();

    // Configurar botones de paginación
    previousButton.addClassName("pagination-button");
    previousButton.addClickListener(e -> previousPage());

    nextButton.addClassName("pagination-button");
    nextButton.addClickListener(e -> nextPage());

    // Configurar información de página
    pageInfo.addClassName("page-info");
  }

  /** Crea la tabla HTML con los datos actuales */
  private void createTable() {
    tableContainer.removeAll();

    if (currentPageItems.isEmpty()) {
      Div emptyMessage = new Div();
      emptyMessage.addClassName("empty-message");
      emptyMessage.setText("No hay datos disponibles");
      tableContainer.add(emptyMessage);
      return;
    }

    // Crear tabla HTML
    NativeButton table = new NativeButton();
    table.getElement().setTag("table");
    table.addClassName("data-table");

    // Crear header
    NativeButton thead = new NativeButton();
    thead.getElement().setTag("thead");

    NativeButton headerRow = new NativeButton();
    headerRow.getElement().setTag("tr");

    for (String headerText : headers.values()) {
      NativeButton th = new NativeButton();
      th.getElement().setTag("th");
      th.getElement().setText(headerText);
      headerRow.getElement().appendChild(th.getElement());
    }

    thead.getElement().appendChild(headerRow.getElement());
    table.getElement().appendChild(thead.getElement());

    // Crear body
    NativeButton tbody = new NativeButton();
    tbody.getElement().setTag("tbody");

    for (T item : currentPageItems) {
      NativeButton row = new NativeButton();
      row.getElement().setTag("tr");

      for (String fieldName : headers.keySet()) {
        NativeButton td = new NativeButton();
        td.getElement().setTag("td");

        String value = getFieldValue(item, fieldName);
        td.getElement().setText(value);

        row.getElement().appendChild(td.getElement());
      }

      tbody.getElement().appendChild(row.getElement());
    }

    table.getElement().appendChild(tbody.getElement());
    tableContainer.add(table);
  }

  /** Obtiene el valor de un campo del objeto usando reflection */
  private String getFieldValue(T item, String fieldName) {
    try {
      // Soporta navegación anidada usando notación de punto
      return getNestedFieldValue(item, fieldName);
    } catch (Exception e) {
      return "N/A";
    }
  }

  /** Obtiene el valor de un campo anidado usando reflection */
  private String getNestedFieldValue(Object obj, String fieldPath) throws Exception {
    if (obj == null) {
      return "";
    }

    // Si no hay punto, es una propiedad simple
    if (!fieldPath.contains(".")) {
      return getSimpleFieldValue(obj, fieldPath);
    }

    // Dividir el path en partes
    String[] parts = fieldPath.split("\\.", 2);
    String currentField = parts[0];
    String remainingPath = parts[1];

    // Obtener el objeto anidado
    Object nestedObject = getSimpleFieldValue(obj, currentField);

    // Si el objeto anidado es null, retornar vacío
    if (nestedObject == null) {
      return "";
    }

    // Recursivamente obtener el valor del path restante
    return getNestedFieldValue(nestedObject, remainingPath);
  }

  /** Obtiene el valor de una propiedad simple usando reflection */
  private String getSimpleFieldValue(Object obj, String fieldName) throws Exception {
    // Intentar con getter estándar
    try {
      String getterName = "get" + capitalize(fieldName);
      Method getter = obj.getClass().getMethod(getterName);
      Object value = getter.invoke(obj);
      return value != null ? value.toString() : "";
    } catch (Exception e) {
      // Si falla, intentar con getter booleano
      try {
        String booleanGetterName = "is" + capitalize(fieldName);
        Method getter = obj.getClass().getMethod(booleanGetterName);
        Object value = getter.invoke(obj);
        return value != null ? value.toString() : "";
      } catch (Exception ex) {
        // Si falla, intentar con el nombre directo del método
        try {
          Method getter = obj.getClass().getMethod(fieldName);
          Object value = getter.invoke(obj);
          return value != null ? value.toString() : "";
        } catch (Exception exc) {
          throw new RuntimeException("No se pudo acceder al campo: " + fieldName, exc);
        }
      }
    }
  }

  /** Capitaliza la primera letra de una cadena */
  private String capitalize(String str) {
    if (str == null || str.isEmpty()) {
      return str;
    }
    return str.substring(0, 1).toUpperCase() + str.substring(1);
  }

  /** Crea el layout de paginación */
  private Component createPaginationLayout() {
    HorizontalLayout paginationLayout = new HorizontalLayout();
    paginationLayout.addClassName("pagination-layout");
    paginationLayout.setWidthFull();
    paginationLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    paginationLayout.setAlignItems(FlexComponent.Alignment.CENTER);
    paginationLayout.setPadding(true);
    paginationLayout.setSpacing(true);

    // Información de página a la izquierda
    HorizontalLayout leftSection = new HorizontalLayout();
    leftSection.add(pageInfo);
    leftSection.setAlignItems(FlexComponent.Alignment.CENTER);

    // Controles de navegación a la derecha
    HorizontalLayout rightSection = new HorizontalLayout();
    rightSection.add(previousButton, nextButton);
    rightSection.setAlignItems(FlexComponent.Alignment.CENTER);
    rightSection.setSpacing(true);

    paginationLayout.add(leftSection, rightSection);
    return paginationLayout;
  }

  /** Navega a la página anterior */
  private void previousPage() {
    if (currentPage > 0) {
      currentPage--;
      updatePagination();
    }
  }

  /** Navega a la página siguiente */
  private void nextPage() {
    if (currentPage < totalPages - 1) {
      currentPage++;
      updatePagination();
    }
  }

  /** Actualiza la paginación y los datos mostrados */
  private void updatePagination() {
    // Calcular páginas totales
    totalPages = (int) Math.ceil((double) allItems.size() / pageSize);

    // Calcular índices de inicio y fin
    int startIndex = currentPage * pageSize;
    int endIndex = Math.min(startIndex + pageSize, allItems.size());

    // Actualizar items de la página actual
    currentPageItems.clear();
    if (startIndex < allItems.size()) {
      currentPageItems.addAll(allItems.subList(startIndex, endIndex));
    }

    // Crear la tabla con los nuevos datos
    createTable();

    // Actualizar controles de paginación
    previousButton.setEnabled(currentPage > 0);
    nextButton.setEnabled(currentPage < totalPages - 1);

    // Actualizar información de página
    if (totalPages > 0 && !allItems.isEmpty()) {
      int displayStart = startIndex + 1;
      int displayEnd = endIndex;
      pageInfo.setText(
          String.format(
              "Mostrando %d - %d de %d registros (Página %d de %d)",
              displayStart, displayEnd, allItems.size(), currentPage + 1, totalPages));
    } else {
      pageInfo.setText("No hay registros para mostrar");
    }
  }

  // Métodos públicos para interactuar con el componente

  /** Actualiza los datos de la tabla */
  public void setItems(List<T> newItems) {
    allItems.clear();
    allItems.addAll(newItems);
    currentPage = 0;
    updatePagination();
  }

  /** Obtiene la página actual */
  public int getCurrentPage() {
    return currentPage;
  }

  /** Obtiene el número total de páginas */
  public int getTotalPages() {
    return totalPages;
  }

  /** Navega a una página específica */
  public void goToPage(int page) {
    if (page >= 0 && page < totalPages) {
      currentPage = page;
      updatePagination();
    }
  }

  /** Refresca los datos de la tabla */
  public void refresh() {
    updatePagination();
  }

  /** Obtiene todos los items */
  public List<T> getAllItems() {
    return new ArrayList<>(allItems);
  }

  /** Obtiene los items de la página actual */
  public List<T> getCurrentPageItems() {
    return new ArrayList<>(currentPageItems);
  }
}
