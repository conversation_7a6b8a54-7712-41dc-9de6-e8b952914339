package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.Component;
import com.vaadin.flow.component.Html;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.html.Div;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Componente DataTable reutilizable e independiente para Vaadin 24 Crea una tabla HTML nativa con
 * paginación personalizada
 *
 * @param <T> Tipo de objeto que contendrá la tabla
 */
@CssImport("./themes/bbva/components/datatable-actions.css")
public class DataTable<T> extends VerticalLayout {

  private transient Map<String, ColumnConfig> columns;
  private final transient List<T> allItems;
  private final int pageSize;

  // Caché de métodos para mejorar rendimiento
  private transient Map<String, Method> methodCache = new HashMap<>();

  // Componentes de la tabla
  private final Div tableContainer;
  private final Button previousButton;
  private final Button nextButton;
  private final Span pageInfo;

  // Estado de paginación
  private int currentPage = 0;
  private int totalPages = 0;
  private transient List<T> currentPageItems;

  /**
   * Constructor del DataTable (compatibilidad hacia atrás)
   *
   * @param id ID único para el componente
   * @param headers Mapa de headers donde key=campo del objeto, value=título a mostrar
   * @param items Lista de items a mostrar
   * @param pageSize Número de items por página
   */
  public DataTable(String id, Map<String, String> headers, List<T> items, int pageSize) {
    this.columns = convertToColumnConfigs(headers);
    this.allItems = new ArrayList<>(items);
    this.pageSize = pageSize;
    this.currentPageItems = new ArrayList<>();

    // Configurar el contenedor principal
    setId(id);
    setWidthFull();
    setHeight("auto");
    setSpacing(false);
    setPadding(false);
    setMargin(false);
    addClassName("bbva-custom-datatable");

    // Crear componentes
    this.tableContainer = new Div();
    this.previousButton = new Button("Anterior");
    this.nextButton = new Button("Siguiente");
    this.pageInfo = new Span();

    // Inicializar componentes
    initializeComponents();
    updatePagination();

    // Agregar componentes al layout
    add(tableContainer, createPaginationLayout());
  }

  /**
   * Crea un DataTable con configuración de columnas incluyendo anchos
   *
   * @param id ID único para el componente
   * @param columns Mapa de configuraciones de columna donde key=campo del objeto,
   *     value=configuración
   * @param items Lista de items a mostrar
   * @param pageSize Número de items por página
   */
  public static <T> DataTable<T> withColumns(
      String id, Map<String, ColumnConfig> columns, List<T> items, int pageSize) {
    DataTable<T> dataTable = new DataTable<>(id, new LinkedHashMap<>(), items, pageSize);
    dataTable.columns.clear();
    dataTable.columns.putAll(columns);
    dataTable.updatePagination(); // Refrescar con las nuevas columnas
    return dataTable;
  }

  /** Convierte un mapa de headers simple a configuraciones de columna */
  private static Map<String, ColumnConfig> convertToColumnConfigs(Map<String, String> headers) {
    Map<String, ColumnConfig> configs = new LinkedHashMap<>();
    for (Map.Entry<String, String> entry : headers.entrySet()) {
      configs.put(entry.getKey(), new ColumnConfig(entry.getKey(), entry.getValue()));
    }
    return configs;
  }

  /** Inicializa los componentes de la tabla y paginación */
  private void initializeComponents() {
    // Configurar contenedor de tabla
    tableContainer.addClassName("table-container");
    tableContainer.setWidthFull();

    // Configurar botones de paginación
    previousButton.addClassName("pagination-button");
    previousButton.addClickListener(e -> previousPage());

    nextButton.addClassName("pagination-button");
    nextButton.addClickListener(e -> nextPage());

    // Configurar información de página
    pageInfo.addClassName("page-info");
  }

  /** Crea la tabla HTML con los datos actuales */
  private void createTable() {
    tableContainer.removeAll();

    if (currentPageItems.isEmpty()) {
      Div emptyMessage = new Div();
      emptyMessage.addClassName("empty-message");
      emptyMessage.setText("No hay datos disponibles");
      tableContainer.add(emptyMessage);
      return;
    }

    // Crear tabla HTML usando Html component
    StringBuilder tableHtml = new StringBuilder();
    tableHtml.append("<table class=\"data-table\">");

    // Crear header
    tableHtml.append("<thead><tr>");
    for (ColumnConfig column : columns.values()) {
      tableHtml.append("<th");
      if (column.hasWidth()) {
        tableHtml.append(" style=\"width: ").append(escapeHtml(column.getWidth())).append("\"");
      }
      tableHtml.append(">").append(escapeHtml(column.getTitle())).append("</th>");
    }
    tableHtml.append("</tr></thead>");

    // Crear body
    tableHtml.append("<tbody>");
    for (T item : currentPageItems) {
      tableHtml.append("<tr>");
      for (ColumnConfig column : columns.values()) {
        tableHtml.append("<td>");

        // Verificar si es una columna de acciones
        if (isActionColumn(column.getFieldName())) {
          tableHtml.append(generateActionIcons(column.getFieldName(), item));
        } else {
          String value = getFieldValue(item, column.getFieldName());
          tableHtml.append(escapeHtml(value));
        }

        tableHtml.append("</td>");
      }
      tableHtml.append("</tr>");
    }
    tableHtml.append("</tbody>");
    tableHtml.append("</table>");

    // Crear el componente Html con la tabla
    Html tableComponent = new Html(tableHtml.toString());
    tableContainer.add(tableComponent);
  }

  /** Escapa caracteres HTML para prevenir XSS */
  private String escapeHtml(String text) {
    if (text == null) {
      return "";
    }
    return text.replace("&", "&amp;")
        .replace("<", "&lt;")
        .replace(">", "&gt;")
        .replace("\"", "&quot;")
        .replace("'", "&#x27;");
  }

  /** Obtiene el valor de un campo del objeto usando reflection con caché */
  private String getFieldValue(T item, String fieldName) {
    try {
      // Soporta navegación anidada usando notación de punto
      return getNestedFieldValue(item, fieldName);
    } catch (ReflectionException e) {
      return "N/A";
    }
  }

  /** Obtiene el valor de un campo anidado usando reflection con caché */
  private String getNestedFieldValue(Object obj, String fieldPath) throws ReflectionException {
    if (obj == null) {
      return "";
    }

    // Si no hay punto, es una propiedad simple
    if (!fieldPath.contains(".")) {
      return getSimpleFieldValueCached(obj, fieldPath);
    }

    // Dividir el path en partes
    String[] parts = fieldPath.split("\\.", 2);
    String currentField = parts[0];
    String remainingPath = parts[1];

    // Obtener el objeto anidado
    Object nestedObject = getSimpleFieldValueObject(obj, currentField);

    // Si el objeto anidado es null, retornar vacío
    if (nestedObject == null) {
      return "";
    }

    // Recursivamente obtener el valor del path restante
    return getNestedFieldValue(nestedObject, remainingPath);
  }

  /** Obtiene el valor de una propiedad simple usando reflection con caché */
  private String getSimpleFieldValueCached(Object obj, String fieldName)
      throws ReflectionException {
    Object value = getSimpleFieldValueObject(obj, fieldName);
    return value != null ? value.toString() : "";
  }

  /** Obtiene el objeto de una propiedad simple usando reflection con caché */
  private Object getSimpleFieldValueObject(Object obj, String fieldName)
      throws ReflectionException {
    String cacheKey = obj.getClass().getName() + "." + fieldName;

    // Buscar en caché primero
    Method method = methodCache.get(cacheKey);

    if (method == null) {
      // No está en caché, buscar el método
      method = findGetterMethod(obj.getClass(), fieldName);
      if (method != null) {
        methodCache.put(cacheKey, method);
      } else {
        throw new ReflectionException("No se pudo encontrar getter para: " + fieldName, null);
      }
    }

    try {
      return method.invoke(obj);
    } catch (Exception e) {
      throw new ReflectionException("Error al invocar método: " + method.getName(), e);
    }
  }

  /** Busca el método getter apropiado para un campo */
  private Method findGetterMethod(Class<?> clazz, String fieldName) {
    // Intentar con getter estándar
    try {
      String getterName = "get" + capitalize(fieldName);
      return clazz.getMethod(getterName);
    } catch (NoSuchMethodException e) {
      // Intentar con getter booleano
      try {
        String booleanGetterName = "is" + capitalize(fieldName);
        return clazz.getMethod(booleanGetterName);
      } catch (NoSuchMethodException ex) {
        // Intentar con el nombre directo del método
        try {
          return clazz.getMethod(fieldName);
        } catch (NoSuchMethodException exc) {
          return null;
        }
      }
    }
  }

  /** Excepción personalizada para errores de reflection */
  private static class ReflectionException extends Exception {
    public ReflectionException(String message, Throwable cause) {
      super(message, cause);
    }
  }

  /** Configuración de columna */
  public static class ColumnConfig {
    private final String fieldName;
    private final String title;
    private final String width;

    public ColumnConfig(String fieldName, String title) {
      this(fieldName, title, null);
    }

    public ColumnConfig(String fieldName, String title, String width) {
      this.fieldName = fieldName;
      this.title = title;
      this.width = width;
    }

    public String getFieldName() {
      return fieldName;
    }

    public String getTitle() {
      return title;
    }

    public String getWidth() {
      return width;
    }

    public boolean hasWidth() {
      return width != null && !width.trim().isEmpty();
    }
  }

  /** Capitaliza la primera letra de una cadena */
  private String capitalize(String str) {
    if (str == null || str.isEmpty()) {
      return str;
    }
    return str.substring(0, 1).toUpperCase() + str.substring(1);
  }

  /** Verifica si una columna es de acciones */
  private boolean isActionColumn(String fieldName) {
    return "action-edit".equals(fieldName)
        || "action-delete".equals(fieldName)
        || "actions-edit-delete".equals(fieldName);
  }

  /** Genera los iconos de acciones según el tipo */
  private String generateActionIcons(String actionType, T item) {
    StringBuilder iconsHtml = new StringBuilder();
    iconsHtml.append("<div class=\"action-buttons\">");

    switch (actionType) {
      case "action-edit":
        iconsHtml.append(generateEditIcon(item));
        break;
      case "action-delete":
        iconsHtml.append(generateDeleteIcon(item));
        break;
      case "actions-edit-delete":
        iconsHtml.append(generateEditIcon(item));
        iconsHtml.append(" ");
        iconsHtml.append(generateDeleteIcon(item));
        break;
      default:
        // No hacer nada para tipos desconocidos
        break;
    }

    iconsHtml.append("</div>");
    return iconsHtml.toString();
  }

  /** Genera el icono de editar */
  private String generateEditIcon(T item) {
    return String.format(
        "<button class=\"action-btn edit-btn\" onclick=\"editItem('%s')\" title=\"Editar\">"
            + "<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">"
            + "<path d=\"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"></path>"
            + "<path d=\"m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z\"></path>"
            + "</svg>"
            + "</button>",
        getItemId(item));
  }

  /** Genera el icono de eliminar */
  private String generateDeleteIcon(T item) {
    return String.format(
        "<button class=\"action-btn delete-btn\" onclick=\"deleteItem('%s')\" title=\"Eliminar\">"
            + "<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">"
            + "<polyline points=\"3,6 5,6 21,6\"></polyline>"
            + "<path d=\"m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2\"></path>"
            + "<line x1=\"10\" y1=\"11\" x2=\"10\" y2=\"17\"></line>"
            + "<line x1=\"14\" y1=\"11\" x2=\"14\" y2=\"17\"></line>"
            + "</svg>"
            + "</button>",
        getItemId(item));
  }

  /** Obtiene el ID del item para las acciones */
  private String getItemId(T item) {
    try {
      // Buscar método getId()
      Method getIdMethod = item.getClass().getMethod("getId");
      Object id = getIdMethod.invoke(item);
      return id != null ? id.toString() : item.hashCode() + "";
    } catch (Exception e) {
      // Si no tiene getId(), usar hashCode
      return item.hashCode() + "";
    }
  }

  /** Crea el layout de paginación */
  private Component createPaginationLayout() {
    HorizontalLayout paginationLayout = new HorizontalLayout();
    paginationLayout.addClassName("pagination-layout");
    paginationLayout.setWidthFull();
    paginationLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    paginationLayout.setAlignItems(FlexComponent.Alignment.CENTER);
    paginationLayout.setPadding(true);
    paginationLayout.setSpacing(true);

    // Información de página a la izquierda
    HorizontalLayout leftSection = new HorizontalLayout();
    leftSection.add(pageInfo);
    leftSection.setAlignItems(FlexComponent.Alignment.CENTER);

    // Controles de navegación a la derecha
    HorizontalLayout rightSection = new HorizontalLayout();
    rightSection.add(previousButton, nextButton);
    rightSection.setAlignItems(FlexComponent.Alignment.CENTER);
    rightSection.setSpacing(true);

    paginationLayout.add(leftSection, rightSection);
    return paginationLayout;
  }

  /** Navega a la página anterior */
  private void previousPage() {
    if (currentPage > 0) {
      currentPage--;
      updatePagination();
    }
  }

  /** Navega a la página siguiente */
  private void nextPage() {
    if (currentPage < totalPages - 1) {
      currentPage++;
      updatePagination();
    }
  }

  /** Actualiza la paginación y los datos mostrados */
  private void updatePagination() {
    // Calcular páginas totales
    totalPages = (int) Math.ceil((double) allItems.size() / pageSize);

    // Calcular índices de inicio y fin
    int startIndex = currentPage * pageSize;
    int endIndex = Math.min(startIndex + pageSize, allItems.size());

    // Actualizar items de la página actual
    currentPageItems.clear();
    if (startIndex < allItems.size()) {
      currentPageItems.addAll(allItems.subList(startIndex, endIndex));
    }

    // Crear la tabla con los nuevos datos
    createTable();

    // Actualizar controles de paginación
    previousButton.setEnabled(currentPage > 0);
    nextButton.setEnabled(currentPage < totalPages - 1);

    // Actualizar información de página
    if (totalPages > 0 && !allItems.isEmpty()) {
      int displayStart = startIndex + 1;
      int displayEnd = endIndex;
      pageInfo.setText(
          String.format(
              "Mostrando %d - %d de %d registros (Página %d de %d)",
              displayStart, displayEnd, allItems.size(), currentPage + 1, totalPages));
    } else {
      pageInfo.setText("No hay registros para mostrar");
    }
  }

  // Métodos públicos para interactuar con el componente

  /** Actualiza los datos de la tabla */
  public void setItems(List<T> newItems) {
    allItems.clear();
    allItems.addAll(newItems);
    currentPage = 0;
    updatePagination();
  }

  /** Obtiene la página actual */
  public int getCurrentPage() {
    return currentPage;
  }

  /** Obtiene el número total de páginas */
  public int getTotalPages() {
    return totalPages;
  }

  /** Navega a una página específica */
  public void goToPage(int page) {
    if (page >= 0 && page < totalPages) {
      currentPage = page;
      updatePagination();
    }
  }

  /** Refresca los datos de la tabla */
  public void refresh() {
    updatePagination();
  }

  /** Obtiene todos los items */
  public List<T> getAllItems() {
    return new ArrayList<>(allItems);
  }

  /** Obtiene los items de la página actual */
  public List<T> getCurrentPageItems() {
    return new ArrayList<>(currentPageItems);
  }
}
