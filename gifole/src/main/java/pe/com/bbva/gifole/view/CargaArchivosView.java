package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;

@PageTitle("Carga de Archivos")
@Route(value = "carga-archivos", layout = MainLayout.class)
public class CargaArchivosView extends VerticalLayout {

  // Filtros (sin Upload ni botón Cargar)
  private final ComboBox<String> tipoArchivoCombo = new ComboBox<>("Tipo de Archivo");

  public CargaArchivosView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Carga de Archivos");
    title.addClassName("bbva-grid-title");

    // Panel de filtros
    HorizontalLayout filtersPanel = createFiltersPanel();

    mainLayout.add(title, filtersPanel);
    add(mainLayout);
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.END);

    tipoArchivoCombo.addClassName("bbva-input-floating");
    tipoArchivoCombo.setWidth("200px");
    tipoArchivoCombo.setItems("AUTOMAS", "OTROS"); // Valores de ejemplo

    filtersPanel.add(tipoArchivoCombo);
    return filtersPanel;
  }
}
