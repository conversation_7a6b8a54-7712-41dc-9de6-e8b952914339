package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.upload.Upload;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import pe.com.bbva.gifole.common.enums.TipoArchivoEnum;
import pe.com.bbva.gifole.common.enums.VehCotizadorConstant;
import pe.com.bbva.gifole.domain.VehCotizadorCargaHistorial;

@PageTitle("Carga de Archivos")
@Route(value = "carga-archivos", layout = MainLayout.class)
public class CargaArchivosView extends VerticalLayout {

  // Constantes
  private static final String SIN_PROCESAR = "Sin Procesar";
  private static final String PROCESADO = "Procesado";
  private static final String NO_TERMINADO = "No Terminado";

  private final Grid<VehCotizadorCargaHistorial> grid =
      new Grid<>(VehCotizadorCargaHistorial.class, false);
  private final List<VehCotizadorCargaHistorial> archivosList = new ArrayList<>();

  // Componentes de carga
  private final ComboBox<TipoArchivoEnum> tipoArchivoCombo = new ComboBox<>();
  private final Upload upload = new Upload();
  private final Button cargarButton = new Button("Cargar");
  private final Span eliminarPickupLabel = new Span("Eliminar los Pickup 4x4");

  private InputStream fileStream;
  private String fileName;

  public CargaArchivosView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    // Layout principal
    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Card para carga de archivos
    VerticalLayout uploadCard = createUploadCard();

    // Card para el Grid
    VerticalLayout gridCard = createGridCard();

    // Footer con opción eliminar pickup
    HorizontalLayout footerLayout = createFooterLayout();

    mainLayout.add(uploadCard, gridCard, footerLayout);
    mainLayout.setFlexGrow(0, uploadCard);
    mainLayout.setFlexGrow(1, gridCard);
    mainLayout.setFlexGrow(0, footerLayout);

    add(mainLayout);

    // Cargar datos de ejemplo
    loadSampleData();
  }

  private VerticalLayout createUploadCard() {
    VerticalLayout uploadCard = new VerticalLayout();
    uploadCard.setWidthFull();
    uploadCard.addClassName("bbva-card");
    uploadCard.addClassName("upload-card");

    H2 uploadTitle = new H2("Carga de Archivos");
    uploadTitle.addClassName("bbva-grid-title");

    // Layout horizontal para los controles de carga
    HorizontalLayout uploadLayout = new HorizontalLayout();
    uploadLayout.setWidthFull();
    uploadLayout.setSpacing(true);
    uploadLayout.setAlignItems(FlexComponent.Alignment.END);

    // Configurar ComboBox de tipo de archivo
    tipoArchivoCombo.setLabel("Tipo de Archivo");
    tipoArchivoCombo.setPlaceholder("Seleccione tipo de archivo");
    tipoArchivoCombo.setWidth("200px");
    tipoArchivoCombo.setItems(TipoArchivoEnum.values());
    tipoArchivoCombo.setItemLabelGenerator(TipoArchivoEnum::getNombre);
    tipoArchivoCombo.setValue(TipoArchivoEnum.AUTOMAS); // Valor por defecto según la imagen

    // Configurar Upload
    upload.setMaxFiles(1);
    upload.setDropLabel(new Span("Seleccionar archivo"));
    upload.setUploadButton(new Button("Ningún archivo seleccionado"));
    upload.getUploadButton().getStyle().set("background", "white");
    upload.getUploadButton().getStyle().set("border", "1px solid #ccc");
    upload.getUploadButton().getStyle().set("color", "#666");
    upload.getUploadButton().getStyle().set("cursor", "pointer");
    upload.setWidth("600px");

    // Usar la API moderna de Vaadin 24 - setUploadHandler
    upload.setUploadHandler(
        uploadEvent -> {
          this.fileName = uploadEvent.getFileName();
          // El archivo fue cargado exitosamente
          getUI()
              .ifPresent(
                  ui ->
                      ui.access(
                          () -> {
                            Notification.show(
                                "Archivo " + this.fileName + " listo para ser procesado.");
                          }));
        });

    // Configurar botón cargar
    cargarButton.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    cargarButton.addClickListener(e -> procesarCarga());

    uploadLayout.add(tipoArchivoCombo, upload, cargarButton);
    uploadCard.add(uploadTitle, uploadLayout);

    return uploadCard;
  }

  private VerticalLayout createGridCard() {
    VerticalLayout gridCard = new VerticalLayout();
    gridCard.setSizeFull();
    gridCard.addClassName("bbva-grid-card");

    configureGrid();
    gridCard.add(grid);

    return gridCard;
  }

  private HorizontalLayout createFooterLayout() {
    HorizontalLayout footerLayout = new HorizontalLayout();
    footerLayout.setWidthFull();
    footerLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.START);
    footerLayout.addClassName("footer-layout");

    eliminarPickupLabel.addClassName("eliminar-pickup-label");
    eliminarPickupLabel.getStyle().set("color", "#666");
    eliminarPickupLabel.getStyle().set("font-size", "14px");

    footerLayout.add(eliminarPickupLabel);
    return footerLayout;
  }

  private void configureGrid() {
    grid.setSizeFull();
    grid.addClassName("carga-grid");

    // Configurar columnas con anchos automáticos
    grid.addColumn(VehCotizadorCargaHistorial::getTipoCarga)
        .setHeader("Tipo de Carga")
        .setSortable(true);

    grid.addColumn(VehCotizadorCargaHistorial::getNombre).setHeader("Nombre").setSortable(true);

    grid.addColumn(historial -> formatDate(historial.getCreacion()))
        .setHeader("Fecha de Carga")
        .setSortable(true);

    grid.addColumn(this::getEstadoDescripcion).setHeader("Estado").setSortable(true);

    grid.addColumn(item -> "").setHeader("Acciones");

    // Configurar selección
    grid.setSelectionMode(Grid.SelectionMode.SINGLE);
  }

  private String formatDate(Date date) {
    if (date == null) return "";
    SimpleDateFormat sdf = new SimpleDateFormat("dd MMM. yyyy HH:mm:ss");
    return sdf.format(date);
  }

  private String getEstadoDescripcion(VehCotizadorCargaHistorial historial) {
    String estado = historial.getEstado();
    if (VehCotizadorConstant.CODIGO_ESTADO_SIN_PROCESO.equals(estado)) {
      return VehCotizadorConstant.DESCRIPCION_ESTADO_SIN_PROCESO;
    } else if (VehCotizadorConstant.CODIGO_ESTADO_PROCESADO.equals(estado)) {
      return VehCotizadorConstant.DESCRIPCION_ESTADO_PROCESADO;
    } else if (VehCotizadorConstant.CODIGO_ESTADO_ERROR.equals(estado)) {
      return VehCotizadorConstant.DESCRIPCION_ESTADO_ERROR;
    }
    return estado;
  }

  private void procesarCarga() {
    if (tipoArchivoCombo.getValue() == null) {
      Notification.show("Debe seleccionar un tipo de archivo")
          .addThemeVariants(NotificationVariant.LUMO_ERROR);
      return;
    }

    if (fileName == null || fileName.isEmpty()) {
      Notification.show("Debe seleccionar un archivo")
          .addThemeVariants(NotificationVariant.LUMO_ERROR);
      return;
    }

    // Simular carga de archivo
    VehCotizadorCargaHistorial nuevoArchivo = new VehCotizadorCargaHistorial();
    nuevoArchivo.setTipoCarga(tipoArchivoCombo.getValue().getNombre());
    nuevoArchivo.setNombre(fileName);
    nuevoArchivo.setCreacion(new Date());
    nuevoArchivo.setEstado(VehCotizadorConstant.CODIGO_ESTADO_SIN_PROCESO);

    archivosList.add(0, nuevoArchivo); // Agregar al inicio
    grid.getDataProvider().refreshAll();

    Notification.show("Archivo cargado exitosamente")
        .addThemeVariants(NotificationVariant.LUMO_SUCCESS);

    // Limpiar formulario
    upload.clearFileList();
    fileName = null;
  }

  private void loadSampleData() {
    // Grid vacío inicialmente como en la imagen
    grid.setItems(archivosList);
  }

  @SuppressWarnings("deprecation")
  private Date createDate(int year, int month, int day, int hour, int minute, int second) {
    return new Date(year - 1900, month - 1, day, hour, minute, second);
  }
}
