package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.SuscripcionProgramadaFFMM;

@Component
public class SuscripcionProgramadaFFMMMapper implements RowMapper<SuscripcionProgramadaFFMM> {

  @Override
  public SuscripcionProgramadaFFMM mapRow(ResultSet rs, int i) throws SQLException {
    SuscripcionProgramadaFFMM suscripcionProgramadaFFMM = new SuscripcionProgramadaFFMM();
    suscripcionProgramadaFFMM.setId(rs.getLong("ID"));
    suscripcionProgramadaFFMM.setNombre(rs.getString("NOMBRE"));
    suscripcionProgramadaFFMM.setCodigoCentral(rs.getString("CODIGO_CENTRAL"));
    suscripcionProgramadaFFMM.setCorreo(rs.getString("CORREO"));
    suscripcionProgramadaFFMM.setNombreFondo(rs.getString("NOMBRE_FONDO"));
    suscripcionProgramadaFFMM.setNumeroFondo(rs.getString("NUMERO_FONDO"));
    suscripcionProgramadaFFMM.setNombreCuenta(rs.getString("NOMBRE_CUENTA"));
    suscripcionProgramadaFFMM.setNumeroCuenta(rs.getString("NUMERO_CUENTA"));
    suscripcionProgramadaFFMM.setPeriodo(rs.getString("PERIODO"));
    suscripcionProgramadaFFMM.setAporteProgramado(rs.getBigDecimal("APORTE_PROGRAMADO"));
    suscripcionProgramadaFFMM.setDivisa(rs.getString("DIVISA"));
    suscripcionProgramadaFFMM.setProcesado(rs.getString("PROCESADO"));
    suscripcionProgramadaFFMM.setDiaCargo(rs.getString("DIACARGO"));
    suscripcionProgramadaFFMM.setFechaRegistro(rs.getTimestamp("FECHA_REGISTRO"));
    return suscripcionProgramadaFFMM;
  }
}
