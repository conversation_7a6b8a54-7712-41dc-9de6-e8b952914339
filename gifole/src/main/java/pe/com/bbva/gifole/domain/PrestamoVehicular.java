package pe.com.bbva.gifole.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class PrestamoVehicular implements Serializable {

  private static final long serialVersionUID = 1L;
  private Long id;
  private String codigoCentral;
  private String tipoDocumento;
  private String numDocumento;
  private String nombres;
  private String paterno;
  private String materno;
  private String correo;
  private String telefono;
  private String importeTotal;
  private String divisaTotal;
  private String importeCuota;
  private String divisaCuota;
  private String plazo;
  private String frecuencia;
  private String tea;
  private String tcea;
  private String tipoVehiculo;
  private String canal;
  private Date fechaRegistro;
  private Date fechaModificacion;
  private Date fechaIniPrestamo;
  private Date fechaFinPrestamo;
  private String fechaIniCreacion;
  private String fechaFinCreacion;
  private String fechaDeRegistro;
}
