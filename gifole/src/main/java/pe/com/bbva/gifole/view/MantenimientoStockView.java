package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;

@PageTitle("Mantenimiento de Stock")
@Route(value = "mantenimiento-stock", layout = MainLayout.class)
public class MantenimientoStockView extends VerticalLayout {

  // Filtros
  private final ComboBox<String> estadoFilter = new ComboBox<>("Estado");
  private final TextField premioFilter = new TextField("Premio");

  public MantenimientoStockView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Filtros de Mantenimiento de Stock");
    title.addClassName("bbva-page-title");

    // Panel de filtros
    HorizontalLayout filtersPanel = createFiltersPanel();

    mainLayout.add(title, filtersPanel);
    add(mainLayout);
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.END);
    filtersPanel.setSpacing(false);

    estadoFilter.addClassName("bbva-input-floating");
    estadoFilter.setWidth("90px");
    estadoFilter.setItems("Todos", "Activo", "Inactivo");
    estadoFilter.setValue("Todos");

    premioFilter.addClassName("bbva-input-floating");
    premioFilter.setWidth("120px");
    premioFilter.setPlaceholder("Buscar premio...");

    filtersPanel.add(estadoFilter, premioFilter);
    return filtersPanel;
  }
}
