package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.shared.Tooltip;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import pe.com.bbva.gifole.common.bean.StockBean;

// COMPONENTE HECHO POR GEMINI
@PageTitle("Mantenimiento de Stock")
@Route(value = "mantenimiento-stock", layout = MainLayout.class)
public class MantenimientoStockView extends VerticalLayout {

  private static final String ACTIVO = "A";
  private static final String INACTIVO = "I";

  private final Grid<StockBean> grid = new Grid<>(StockBean.class, false);
  private final List<StockBean> stockList = new ArrayList<>();
  private final List<StockBean> allStock = new ArrayList<>();

  private int currentPage = 0;
  private final int pageSize = 10;
  private int totalPages = 0;

  private Button previousButton;
  private Button nextButton;
  private Span pageInfo;

  private ComboBox<String> estadoFilter;
  private TextField premioFilter;

  private final ComboBox<String> cmbTipoMoneda = new ComboBox<>();
  private final TextField txtPremio = new TextField();
  private final TextField txtNroStock = new TextField();
  private final TextField txtStockInicial = new TextField();
  private final TextField txtDetallePremio = new TextField();
  private final TextField txtCondicionPremio = new TextField();
  private final ComboBox<String> cmbEstado = new ComboBox<>();

  private final Button btnGuardar = new Button("Guardar");
  private final Button btnEliminar = new Button("Eliminar");
  private final Button btnNuevo = new Button("Nuevo Stock", VaadinIcon.PLUS.create());
  private final Button btnCerrarDrawer = new Button(VaadinIcon.CLOSE.create());

  private VerticalLayout drawerPanel;
  private boolean drawerVisible = false;
  private StockBean stockEnEdicion = null;

  public MantenimientoStockView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);
    getStyle().set("position", "relative");

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    HorizontalLayout filtersPanel = createFiltersPanel();
    HorizontalLayout headerLayout = createHeaderLayout();
    VerticalLayout gridCard = createGridCard();
    gridCard.setSizeFull();

    mainLayout.add(filtersPanel, headerLayout, gridCard);
    mainLayout.setFlexGrow(1, gridCard);

    drawerPanel = createDrawerPanel();

    add(mainLayout, drawerPanel);

    loadData();
    updateGrid();
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(FlexComponent.Alignment.END);
    filtersPanel.setSpacing(false);

    estadoFilter = new ComboBox<>("Estado");
    estadoFilter.addClassName("bbva-input-floating");
    estadoFilter.setWidth("90px");
    estadoFilter.setItems("Todos", "Activo", "Inactivo");
    estadoFilter.setValue("Todos");
    estadoFilter.addValueChangeListener(e -> applyFilters());

    premioFilter = new TextField("Premio");
    premioFilter.addClassName("bbva-input-floating");
    premioFilter.setWidth("120px");
    premioFilter.setPlaceholder("Buscar premio...");
    premioFilter.addValueChangeListener(e -> applyFilters());

    filtersPanel.add(estadoFilter, premioFilter);

    return filtersPanel;
  }

  private HorizontalLayout createHeaderLayout() {
    H2 title = new H2("Lista de Stock");
    title.addClassName("bbva-grid-title");
    title.getStyle().set("margin", "0");

    btnNuevo.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnNuevo.addClassName("bbva-filters-button");
    btnNuevo.addClickListener(e -> toggleDrawer());
    Tooltip.forComponent(btnNuevo).withText("Crear nuevo stock");

    HorizontalLayout header = new HorizontalLayout(title, btnNuevo);
    header.setWidthFull();
    header.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    header.setAlignItems(FlexComponent.Alignment.CENTER);
    header.addClassName("bbva-header-layout");

    return header;
  }

  private VerticalLayout createDrawerPanel() {
    VerticalLayout drawer = new VerticalLayout();
    drawer.addClassName("drawer-panel");
    drawer.setWidth("500px");
    drawer.setHeight("100%");
    drawer.setPadding(true);
    drawer.setSpacing(true);

    drawer
        .getStyle()
        .set("position", "fixed")
        .set("top", "0")
        .set("right", "-500px")
        .set("background", "var(--bbva-white)")
        .set("box-shadow", "-4px 0 20px rgba(0, 0, 0, 0.15)")
        .set("z-index", "1000")
        .set("transition", "right 0.3s ease-in-out")
        .set("overflow-y", "auto");

    HorizontalLayout drawerHeader = new HorizontalLayout();
    drawerHeader.setWidthFull();
    drawerHeader.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    drawerHeader.setAlignItems(FlexComponent.Alignment.CENTER);

    H2 drawerTitle = new H2("Formulario de Stock");
    drawerTitle.addClassName("bbva-page-title");
    drawerTitle.getStyle().set("margin", "0");

    btnCerrarDrawer.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
    btnCerrarDrawer.addClickListener(e -> toggleDrawer());
    Tooltip.forComponent(btnCerrarDrawer).withText("Cerrar formulario");

    drawerHeader.add(drawerTitle, btnCerrarDrawer);

    VerticalLayout formContent = createFormContent();

    drawer.add(drawerHeader, formContent);
    drawer.setFlexGrow(1, formContent);

    return drawer;
  }

  private VerticalLayout createFormContent() {
    VerticalLayout formContent = new VerticalLayout();
    formContent.setSpacing(true);
    formContent.setPadding(false);

    cmbTipoMoneda.setLabel("Tipo de Moneda");
    cmbTipoMoneda.addClassName("bbva-form-field");
    cmbTipoMoneda.setItems("Soles", "Dólares");

    txtPremio.setLabel("Premio");
    txtPremio.addClassName("bbva-form-field");

    txtNroStock.setLabel("Número de Stock");
    txtNroStock.addClassName("bbva-form-field");

    txtStockInicial.setLabel("Stock Inicial");
    txtStockInicial.addClassName("bbva-form-field");

    txtDetallePremio.setLabel("Detalle del Premio");
    txtDetallePremio.addClassName("bbva-form-field");

    txtCondicionPremio.setLabel("Condición del Premio");
    txtCondicionPremio.addClassName("bbva-form-field");

    cmbEstado.setLabel("Estado");
    cmbEstado.addClassName("bbva-form-field");
    cmbEstado.setItems(ACTIVO, INACTIVO);
    cmbEstado.setValue(ACTIVO);

    btnGuardar.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnGuardar.addClassName("bbva-button");
    btnGuardar.addClickListener(e -> guardarStock());

    btnEliminar.addThemeVariants(ButtonVariant.LUMO_ERROR);
    btnEliminar.addClassName("bbva-button");
    btnEliminar.addClickListener(e -> eliminarStock());
    btnEliminar.setVisible(false);

    HorizontalLayout buttonLayout = new HorizontalLayout(btnGuardar, btnEliminar);
    buttonLayout.setSpacing(true);
    buttonLayout.setWidthFull();

    formContent.add(
        cmbTipoMoneda,
        txtPremio,
        txtNroStock,
        txtStockInicial,
        txtDetallePremio,
        txtCondicionPremio,
        cmbEstado,
        buttonLayout);

    return formContent;
  }

  private void toggleDrawer() {
    drawerVisible = !drawerVisible;
    if (drawerVisible) {
      drawerPanel.getStyle().set("right", "0");
      btnNuevo.setText("Cerrar");
      btnNuevo.setIcon(VaadinIcon.CLOSE.create());
    } else {
      drawerPanel.getStyle().set("right", "-500px");
      btnNuevo.setText("Nuevo Stock");
      btnNuevo.setIcon(VaadinIcon.PLUS.create());
      limpiarFormulario();
    }
  }

  private VerticalLayout createGridCard() {
    VerticalLayout gridCard = new VerticalLayout();
    gridCard.setSizeFull();
    gridCard.addClassName("bbva-grid-card");
    gridCard.setSpacing(true);
    gridCard.setPadding(true);

    grid.setSizeFull();
    grid.addClassName("bbva-grid");
    grid.addThemeVariants(GridVariant.LUMO_ROW_STRIPES, GridVariant.LUMO_WRAP_CELL_CONTENT);

    grid.addColumn(StockBean::getTipoMoneda)
        .setHeader("Tipo Moneda")
        .setSortable(true)
        .setAutoWidth(true);
    grid.addColumn(StockBean::getPremio).setHeader("Premio").setSortable(true).setAutoWidth(true);
    grid.addColumn(StockBean::getNroStock)
        .setHeader("Nro Stock")
        .setSortable(true)
        .setAutoWidth(true);
    grid.addColumn(StockBean::getStockInicial)
        .setHeader("Stock Inicial")
        .setSortable(true)
        .setAutoWidth(true);
    grid.addColumn(StockBean::getDetallePremio)
        .setHeader("Detalle Premio")
        .setSortable(true)
        .setAutoWidth(true);
    grid.addColumn(StockBean::getCondicionPremio)
        .setHeader("Condición Premio")
        .setSortable(true)
        .setAutoWidth(true);
    Grid.Column<StockBean> estadoColumn =
        grid.addColumn(stock -> ACTIVO.equals(stock.getEstado()) ? "Activo" : "Inactivo")
            .setHeader("Estado")
            .setSortable(true)
            .setAutoWidth(true);

    grid.addComponentColumn(this::createActionsColumn)
        .setHeader("Acciones")
        .setSortable(false)
        .setAutoWidth(true)
        .setFlexGrow(0);

    estadoColumn.setClassNameGenerator(
        stock -> ACTIVO.equals(stock.getEstado()) ? "bbva-status-active" : "bbva-status-inactive");

    HorizontalLayout paginationControls = createPagination();

    gridCard.add(grid, paginationControls);
    gridCard.setFlexGrow(1, grid);

    return gridCard;
  }

  private HorizontalLayout createActionsColumn(StockBean stock) {
    Button editButton = new Button(new Icon(VaadinIcon.EDIT));
    editButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY, ButtonVariant.LUMO_SMALL);
    editButton.addClassName("bbva-action-button");
    editButton.addClickListener(e -> editStock(stock));
    Tooltip.forComponent(editButton).withText("Editar stock");

    Button deleteButton = new Button(new Icon(VaadinIcon.TRASH));
    deleteButton.addThemeVariants(
        ButtonVariant.LUMO_TERTIARY, ButtonVariant.LUMO_ERROR, ButtonVariant.LUMO_SMALL);
    deleteButton.addClassName("bbva-action-button");
    deleteButton.addClickListener(e -> deleteStock(stock));
    Tooltip.forComponent(deleteButton).withText("Eliminar stock");

    HorizontalLayout actions = new HorizontalLayout(editButton, deleteButton);
    actions.setSpacing(false);
    actions.setJustifyContentMode(FlexComponent.JustifyContentMode.CENTER);
    actions.addClassName("bbva-actions-layout");
    return actions;
  }

  private HorizontalLayout createPagination() {
    HorizontalLayout paginationLayout = new HorizontalLayout();
    paginationLayout.addClassName("bbva-pagination");
    paginationLayout.setWidthFull();
    paginationLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    paginationLayout.setAlignItems(FlexComponent.Alignment.CENTER);

    pageInfo = new Span();
    pageInfo.addClassName("bbva-pagination-info");

    HorizontalLayout controls = new HorizontalLayout();
    controls.addClassName("bbva-pagination-controls");
    controls.setSpacing(true);

    previousButton = new Button("Anterior", VaadinIcon.ANGLE_LEFT.create());
    previousButton.addClassName("bbva-pagination-button");
    previousButton.addClickListener(e -> previousPage());

    nextButton = new Button("Siguiente", VaadinIcon.ANGLE_RIGHT.create());
    nextButton.addClassName("bbva-pagination-button");
    nextButton.addClickListener(e -> nextPage());

    controls.add(previousButton, nextButton);
    paginationLayout.add(pageInfo, controls);

    return paginationLayout;
  }

  private void loadData() {
    allStock.clear();
    StockBean stock1 = new StockBean();
    stock1.setId(1L);
    stock1.setTipoMoneda("Soles");
    stock1.setPremio("Premio 1");
    stock1.setNroStock(100);
    stock1.setStockInicial(100);
    stock1.setDetallePremio("Detalle 1");
    stock1.setCondicionPremio("Condicion 1");
    stock1.setEstado(ACTIVO);
    stock1.setFechaInicio(new Date());
    stock1.setFechaFin(new Date());

    StockBean stock2 = new StockBean();
    stock2.setId(2L);
    stock2.setTipoMoneda("Dólares");
    stock2.setPremio("Premio 2");
    stock2.setNroStock(50);
    stock2.setStockInicial(50);
    stock2.setDetallePremio("Detalle 2");
    stock2.setCondicionPremio("Condicion 2");
    stock2.setEstado(INACTIVO);
    stock2.setFechaInicio(new Date());
    stock2.setFechaFin(new Date());

    allStock.add(stock1);
    allStock.add(stock2);
    applyFilters();
  }

  private void applyFilters() {
    List<StockBean> filteredStock =
        allStock.stream()
            .filter(
                stock -> {
                  if (!"Todos".equals(estadoFilter.getValue())) {
                    String expectedState =
                        "Activo".equals(estadoFilter.getValue()) ? ACTIVO : INACTIVO;
                    if (stock.getEstado() == null || !expectedState.equals(stock.getEstado())) {
                      return false;
                    }
                  }

                  if (premioFilter.getValue() != null && !premioFilter.getValue().isEmpty()) {
                    if (stock.getPremio() == null
                        || !stock
                            .getPremio()
                            .toLowerCase()
                            .contains(premioFilter.getValue().toLowerCase())) {
                      return false;
                    }
                  }

                  return true;
                })
            .collect(Collectors.toList());

    stockList.clear();
    stockList.addAll(filteredStock);
    currentPage = 0;
    updateGrid();
  }

  private void updateGrid() {
    totalPages = (int) Math.ceil((double) stockList.size() / pageSize);

    int start = currentPage * pageSize;
    int end = Math.min(start + pageSize, stockList.size());

    List<StockBean> pageData = stockList.subList(start, end);
    grid.setItems(pageData);

    updatePaginationControls();
  }

  private void updatePaginationControls() {
    previousButton.setEnabled(currentPage > 0);
    nextButton.setEnabled(currentPage < totalPages - 1);

    if (totalPages > 0) {
      pageInfo.setText(
          String.format(
              "Página %d de %d (%d registros)", currentPage + 1, totalPages, stockList.size()));
    } else {
      pageInfo.setText("No hay registros");
    }
  }

  private void previousPage() {
    if (currentPage > 0) {
      currentPage--;
      updateGrid();
    }
  }

  private void nextPage() {
    if (currentPage < totalPages - 1) {
      currentPage++;
      updateGrid();
    }
  }

  private void guardarStock() {
    if (validarCampos()) {
      if (stockEnEdicion == null) {
        StockBean nuevoStock = new StockBean();
        mapearFormularioABean(nuevoStock);
        nuevoStock.setId((long) (allStock.size() + 1));
        allStock.add(nuevoStock);
        Notification.show("Stock creado exitosamente")
            .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
      } else {
        mapearFormularioABean(stockEnEdicion);
        Notification.show("Stock actualizado exitosamente")
            .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
      }
      applyFilters();
      toggleDrawer();
    }
  }

  private void eliminarStock() {
    if (stockEnEdicion != null) {
      allStock.remove(stockEnEdicion);
      applyFilters();
      toggleDrawer();
      Notification.show("Stock eliminado exitosamente")
          .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
    }
  }

  private void editStock(StockBean stock) {
    stockEnEdicion = stock;
    cargarStockEnFormulario(stock);
    if (!drawerVisible) {
      toggleDrawer();
    }
    btnEliminar.setVisible(true);
    btnGuardar.setText("Actualizar");
  }

  private void deleteStock(StockBean stock) {
    stockEnEdicion = stock;
    eliminarStock();
  }

  private boolean validarCampos() {
    if (cmbTipoMoneda.getValue() == null || cmbTipoMoneda.getValue().trim().isEmpty()) {
      Notification.show("El tipo de moneda es requerido")
          .addThemeVariants(NotificationVariant.LUMO_ERROR);
      return false;
    }
    if (txtPremio.getValue() == null || txtPremio.getValue().trim().isEmpty()) {
      Notification.show("El premio es requerido").addThemeVariants(NotificationVariant.LUMO_ERROR);
      return false;
    }
    return true;
  }

  private void mapearFormularioABean(StockBean stock) {
    stock.setTipoMoneda(cmbTipoMoneda.getValue());
    stock.setPremio(txtPremio.getValue());
    stock.setNroStock(Integer.parseInt(txtNroStock.getValue()));
    stock.setStockInicial(Integer.parseInt(txtStockInicial.getValue()));
    stock.setDetallePremio(txtDetallePremio.getValue());
    stock.setCondicionPremio(txtCondicionPremio.getValue());
    stock.setEstado(cmbEstado.getValue());
  }

  private void cargarStockEnFormulario(StockBean stock) {
    cmbTipoMoneda.setValue(stock.getTipoMoneda());
    txtPremio.setValue(stock.getPremio());
    txtNroStock.setValue(String.valueOf(stock.getNroStock()));
    txtStockInicial.setValue(String.valueOf(stock.getStockInicial()));
    txtDetallePremio.setValue(stock.getDetallePremio());
    txtCondicionPremio.setValue(stock.getCondicionPremio());
    cmbEstado.setValue(stock.getEstado());
  }

  private void limpiarFormulario() {
    stockEnEdicion = null;
    cmbTipoMoneda.clear();
    txtPremio.clear();
    txtNroStock.clear();
    txtStockInicial.clear();
    txtDetallePremio.clear();
    txtCondicionPremio.clear();
    cmbEstado.setValue(ACTIVO);
    btnEliminar.setVisible(false);
    btnGuardar.setText("Guardar");
  }
}
