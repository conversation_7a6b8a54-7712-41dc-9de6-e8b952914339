package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import pe.com.bbva.gifole.common.enums.TipoDivisaMonto;

public class DivisaMontoBean implements Serializable {

  /** Divisa */
  private static final long serialVersionUID = -8264769460087582356L;

  private String codigo;
  private TipoDivisaMonto tipoDivisaMonto;

  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public TipoDivisaMonto getTipoDivisaMonto() {
    return tipoDivisaMonto;
  }

  public void setTipoDivisaMonto(TipoDivisaMonto tipoDivisaMonto) {
    this.tipoDivisaMonto = tipoDivisaMonto;
  }
}
