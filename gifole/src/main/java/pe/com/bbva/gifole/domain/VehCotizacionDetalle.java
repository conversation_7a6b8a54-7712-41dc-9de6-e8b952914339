package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Entity
@Table(name = "VEH_COTIZACION_DETALLE")
public class VehCotizacionDetalle implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SeqGenV")
  @SequenceGenerator(
      name = "SeqGenV",
      sequenceName = "SQ_VEH_COTIZACION_DETALLE",
      allocationSize = 1)
  @Column(name = "ID")
  private Long id;

  // bidirectional many-to-one association to VehCotizacion
  @ManyToOne
  @JoinColumn(name = "COTIZACION")
  private VehCotizacion cotizacion;

  // bidirectional many-to-one association to VehPlan
  @ManyToOne
  @JoinColumn(name = "PLAN")
  private VehPlan plan;

  @Column(name = "CUOTA_MENSUAL", length = 15, precision = 2)
  private BigDecimal cuotaMensual;

  @Column(name = "CUOTA_ANUAL", length = 15, precision = 2)
  private BigDecimal cuotaAnual;

  @Column(name = "DIVISA", length = 3)
  private String divisa;

  @Transient private String detalleMensual;

  @Transient private String divisaSimbolo;

  @Transient private String valor;

  @Transient private String cuota;
}
