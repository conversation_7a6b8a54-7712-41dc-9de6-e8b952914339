package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.SisItem;

@Component
public class Sis<PERSON>temMapper implements RowMapper<SisItem> {

  @Override
  public SisItem mapRow(ResultSet rs, int i) throws SQLException {
    SisItem sisItemBean = new SisItem();
    sisItemBean.setId(rs.getLong("ID"));
    sisItemBean.setDescripcion(rs.getString("DESCRIPCION"));
    sisItemBean.setEstado(rs.getString("ESTADO"));
    return sisItemBean;
  }
}
