package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.common.bean.TCReferidoSinTablonBean;
import pe.com.bbva.gifole.util.UtilCalendario;

@SuppressWarnings("rawtypes")
@Component
public class TCReferidoSinTablonMapper implements RowMapper {

  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    TCReferidoSinTablonBean param = new TCReferidoSinTablonBean();

    String clasificacion = "NO";

    if (rs.getString("CLASIFICACIONREFERIDOR") != null) {
      if (rs.getString("CLASIFICACIONREFERIDOR").equals("EMPLOYEE")) {
        clasificacion = "SI";
      }
    }

    param.setNombreCompletoReferidor(
        rs.getString("NOMBREREFERIDOR")
            + " "
            + rs.getString("APELLIDOPATERNOREFERIDOR")
            + " "
            + StringUtils.trimToEmpty(rs.getString("APELLIDOMATERNOREFERIDOR")));
    param.setNumeroDocumentoReferidor(rs.getString("NRODOCUMENTOREFERIDOR"));
    param.setMarcaEmpleadoReferidor(clasificacion);
    param.setNumeroDocumentoReferido(rs.getString("NRODOCUMENTOREFERIDO"));
    param.setCodigoCentralReferido(rs.getString("CODIGOCENTRALREFERIDO"));
    param.setCelularReferido(rs.getString("CELULARREFERIDO"));
    param.setNombreCompletoReferido(
        rs.getString("NOMBREREFERIDO")
            + " "
            + rs.getString("APELLIDOPATERNOREFERIDO")
            + " "
            + StringUtils.trimToEmpty(rs.getString("APELLIDOMATERNOREFERIDO")));
    param.setNbrTarjetaReferido(rs.getString("NOMBRETARJETAREFERIDO"));
    param.setTipoTarjetaReferido(rs.getString("TIPOTARJETAREFERIDO"));
    param.setLineaReferido(rs.getString("LINEAREFERIDO"));
    param.setTasaReferido(rs.getString("TASAREFERIDO"));
    param.setCorreoReferido(rs.getString("CORREOREFERIDO"));
    param.setFechaLeadGenerado(UtilCalendario.obtenerCadenaFechaActualPeru());
    param.setId(rs.getLong("IDREFERIDO"));

    return param;
  }
}
