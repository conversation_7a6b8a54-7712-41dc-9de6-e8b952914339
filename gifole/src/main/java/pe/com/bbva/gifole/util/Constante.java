package pe.com.bbva.gifole.util;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class Constante {

  public static final Logger loggerCargaSeguros = LogManager.getLogger("cargaSeguros");
  public static final Logger loggerFondoMutuo = LogManager.getLogger("fondoMutuo");
  public static final Logger loggerSeguroProtTarjeta = LogManager.getLogger("seguroProtTarjeta");
  public static final Logger loggerTarjeta = LogManager.getLogger("tarjeta");
  public static final Logger loggerCuenta = LogManager.getLogger("cuenta");
  public static final Logger loggerError = LogManager.getLogger("errorGifole");
  public static final Logger loggerIncentivacionJob = LogManager.getLogger("incentivacionjob");
  public static final Logger loggerAperturaFondo = LogManager.getLogger("aperturafondo");
  public static final Logger loggerDepositoPlazoFijo = LogManager.getLogger("depositoPlazoFijo");
  public static final Logger loggerCancelacionProducto =
      LogManager.getLogger("cancelacionProductos");
  public static final Logger loggerCambioModalidadEECC =
      LogManager.getLogger("cambioModalidadEECC");
  public static final Logger loggerSemaforoDigital = LogManager.getLogger("semaforoDigital");
  public static final Logger loggerCargaCuponRaspaGana =
      LogManager.getLogger("cargaCuponRaspaGana");
  public static final Logger loggerRaspaYGana = LogManager.getLogger("raspaYGana");
  public static final Logger loggerBannerCrossSell = LogManager.getLogger("bannerCrossSell");
  public static final Logger loggerTocRemarketing = LogManager.getLogger("tocRemarketing");
  public static final Logger loggerPrestamoVehicular = LogManager.getLogger("prestamoVehicular");
  public static final Logger loggerCargarArchivosParametria =
      LogManager.getLogger("cargarArchivosParametria");
  public static final Logger loggerCuentasAfp = LogManager.getLogger("cuentasAfp");
  public static final Logger loggerCargarArchivosCotizador =
      LogManager.getLogger("cargarArchivosCotizador");
  public static final Logger loggerSeguroVehicularRoyal =
      LogManager.getLogger("seguroVehicularRoyal");
  public static final Logger loggerSeguroHogarRoyal = LogManager.getLogger("seguroHogarRoyal");
  public static final Logger loggerVentaSeguroRoyal = LogManager.getLogger("ventaSeguroRoyal");
  public static final Logger loggerReferidosMP = LogManager.getLogger("referidosMP");
  public static final Logger loggerTarjetaGarantizada = LogManager.getLogger("tarjetaGarantizada");
  public static final Logger loggerSeguroNegocioATuMedidaRoyal =
      LogManager.getLogger("seguroNegocioATuMedidaRoyal");
  public static final Logger loggerTarjetaUpgrade = LogManager.getLogger("tarjetaUpgrade");
  public static final Logger loggerBeneficiosUpgrade = LogManager.getLogger("beneficiosUpgrade");
  public static final Logger loggerSeguroVidaRoyal = LogManager.getLogger("seguroVidaRoyal");
  public static final Logger loggerProductoSeguro = LogManager.getLogger("productoSeguro");
  public static final Logger loggerSeguroRoyal = LogManager.getLogger("SeguroRoyal");
  public static final Logger loggerMercurio = LogManager.getLogger("mercurio");
  public static final Logger loggerSeguroVehicularRoyalOpenPay =
      LogManager.getLogger("seguroVehicularRoyalOpenPay");
  public static final Logger loggerGiam = LogManager.getLogger("giam");

  public abstract static class SESION {
    public static final String USUARIO = "SESION_USUARIO";
    public static final String ITEM = "SESION_ITEM";
    public static final String PLIN_LEADS_IN_REVIEW = "PLIN_LEADS_IN_REVIEW";
  }

  public abstract static class DESARROLLO {
    public static final String MODO = "0";
  }

  public abstract static class ADICIONAL {
    public static final String ESTADO_REGISTRADO_ONE_CLICK = "0";
    public static final String ESTADO_REGISTRADO = "1";
    public static final String ESTADO_RECHAZADO = "2";
    public static final String ESTADO_PROCESADO = "3";
    public static final String ESTADO_APROBADO_AUTOMATICO = "9";
    public static final String ESTADO_RECHAZADO_NO_ENVIO_CORREO = "4";
    public static final String ESTADO_PROCESADO_NO_ENVIO_CORREO = "5";

    public static final String OTRO_MOTIVO = "OTROS";
    public static final String MOTIVO_EXTRANJERO_SIN_BD =
        "CLIENTE EXTRANJERO SIN BASE DE DATOS O BASE INCOMPLETA";
    public static final String MOTIVO_OFERTA_GIFOLE_MAYOR_INDICADO_MOI =
        "OFERTA EN GIFOLE ES MAYOR A LO INDICADO EN MOI";
    public static final String CLIENTE_NUEVO = "N";
    public static final String CLIENTE_ANTIGUO = "A";

    public static final String TIPO_MOTIVO_RECHAZO_TA = "TA";
    public static final String TIPO_MOTIVO_RECHAZO_TC = "TC";
  }

  public abstract static class CANAL {
    public static final String BANCA_POR_INTERNET_UX = "BXIUX";
    public static final String NOMBRE_BANCA_POR_INTERNET = "BANCA POR INTERNET";
    public static final String ZONA_PUBLICA = "ZONA_PUB";
    public static final String BANCA_MOVIL = "BMMOVIL";
    public static final String NOMBRE_BANCA_MOVIL = "BANCA MOVIL";
    public static final String PIC = "PIC";
    public static final String IVR = "IVR";
  }

  public abstract class PARAMETRO {

    public static final String RPTA_NO_CONE_WS = "Error!. No se pudo conectar al servicio.";
    public static final String COD_RPTA_EXITO = "1";
    public static final String COD_RPTA_NO_EXITO = "0";
    public static final String COD_RPTA_EXCEPTION = "-1";
  }

  public abstract class MENSAJE {

    public static final String MSJ_RPTA_EXITO_CONSULTAR_APERTURA_CUENTA =
        "Se encontro el numero de cuenta consultada.";
    public static final String MSJ_RPTA_NO_EXITO_CONSULTAR_APERTURA_CUENTA =
        "No se encontro el numero de cuenta consultada.";
    public static final String MSJ_RPTA_EXCEPTION = "Ha ocurrido un error en: ";
    public static final String MSJ_RPTA_NULL = "Error!. No exise el objeto.";
    public static final String MSJ_RPTA_VACIO = "Error!. Datos ingresados vacios.";
    public static final String MSJ_RPTA_LISTA_VACIA = "La lista obtenida se encuentra vacia.";

    public static final String MSJ_OK = "OK";
    public static final String MSJ_ERROR = "ERROR";
    public static final String MSJ_NO_DATA = "NO SE ENCONTRARON DATOS";
    public static final String MSJ_ERROR_RUTA_GENERACION = "ERROR EN GENERACION DE RUTA";
    public static final String MSJ_ERROR_LIMPIAR_ARCHIVO = "ERROR AL LIMPIAR ARCHIVO";
    public static final String MSJ_ERROR_GENERACION_ARCHIVO = "ERROR EN GENERACION DE ARCHIVO";
    public static final String MSJ_FALTAN_REGISTROS_ARCHIVO =
        "NO SE HAN CONSIDERADO TODOS LOS REGISTROS EN EL ARCHIVO";
    public static final String MSJ_NO_PROCESO = "YA EXISTE EL ARCHIVO";
    public static final String MSJ_ERROR_ACTUALIZACION_FLAG_PROCESO =
        "ERROR EN ACTUALIZAR FLAG DE PROCESOS";
    public static final String MSJ_ERROR_NO_ACTUALIZA = "ERROR - NO ACTUALIZA";
    public static final String MSJ_ERROR_NO_COPIA_ARCHIVOS = "ERROR - NO COPIA ARCHIVOS";
    public static final String MSJ_HA_OCURRIDO_ERROR = "Ha ocurrido un error ";
    public static final String MSJ_TERMINO_CON_EXITO = "Se he culminado con exito ";
    public static final String MSJ_CONFIRMAR_REGISTRO = "Confirmar Registro?";
    public static final String MSJ_CONFIRMAR_ACTUALIZACION = "Confirmar Actualización?";
    public static final String MSJ_CONFIRMAR_ELIMINAR = "Confirmar Eliminar?";
  }

  public abstract class ESTADO_SVR {
    public static final String REGISTRADO = "REGISTRADO";
    public static final String FORMALIZADO = "FORMALIZADO";
  }

  public abstract class CANCELACION_PRODUCTOS {
    public static final String OTRO_MOTIVO = "OTROS";
  }

  public class PatFtpArchivosPld {
    private PatFtpArchivosPld() {}

    public static final String FTP_CERTICOM = "FTP_CERTICOM";
    public static final String FTP_COMER = "FTP_COMER";
    public static final String FTP_CONSUMER = "FTP_CONSUMER";

    public static final String FTP_SERVER = "FTP_SERVER";
    public static final String FTP_PORT = "FTP_PORT";
    public static final String FTP_USR = "FTP_USR";
    public static final String FTP_PAS = "FTP_PWD";
    public static final String FTP_OUT = "FTP_OUT";

    public static final String RUTA_ARCHIVO = "RUTA_ARCHIVO";
    public static final String TIPO = "PAT_ARCHIVO_RMRKTNG";
  }

  public abstract class DATOS_GENERALES {

    private DATOS_GENERALES() {}

    public static final String EMPTY = "";

    public static final String UI_WIDTH_100_POR_CIENTO = "100.0%";

    public static final String UI_WIDTH_30_PX = "30px";
    public static final String UI_WIDTH_100_PX = "100px";
    public static final String UI_WIDTH_112_PX = "112px";
    public static final String UI_WIDTH_115_PX = "115px";
    public static final String UI_WIDTH_130_PX = "130px";
    public static final String UI_WIDTH_140_PX = "140px";
    public static final String UI_WIDTH_150_PX = "150px";
    public static final String UI_WIDTH_168_PX = "168px";
    public static final String UI_WIDTH_200_PX = "200px";
    public static final String UI_WIDTH_210_PX = "210px";
    public static final String UI_WIDTH_250_PX = "250px";
    public static final String UI_WIDTH_300_PX = "300px";
    public static final String UI_WIDTH_400_PX = "400px";
    public static final String UI_WIDTH_500_PX = "500px";
    public static final String UI_WIDTH_670_PX = "670px";
    public static final String UI_WIDTH_700_PX = "700px";
    public static final String UI_WIDTH_710_PX = "710px";
    public static final String UI_WIDTH_750_PX = "750px";
    public static final String UI_WIDTH_800_PX = "800px";
    public static final String UI_WIDTH_1300_PX = "1300px";

    public static final String UI_WIDTH_1_PX = "-1px";
    public static final String UI_HEIGHT_1_PX = "-1px";
    public static final String UI_HEIGHT_100_PX = "100px";
    public static final String UI_HEIGHT_200_PX = "200px";

    public static final String UI_LABEL_CODIGO_CENTRAL = "Código Central";
    public static final String UI_LABEL_BUSCAR = "Buscar";
    public static final String UI_LABEL_CREAR = "Crear";
    public static final String UI_LABEL_ACTUALIZAR = "Actualizar";
    public static final String UI_LABEL_ELIMINAR = "Eliminar";
    public static final String UI_LABEL_EXPORTAR_EXCEL = "Exportar Excel";
    public static final String UI_LABEL_EXPORTAR_CSV = "Exportar CSV";

    public static final int UI_COLUMN_DATATABLE_WIDTH_60 = 60;
    public static final int UI_COLUMN_DATATABLE_WIDTH_70 = 70;
    public static final int UI_COLUMN_DATATABLE_WIDTH_80 = 80;
    public static final int UI_COLUMN_DATATABLE_WIDTH_90 = 90;
    public static final int UI_COLUMN_DATATABLE_WIDTH_100 = 100;
    public static final int UI_COLUMN_DATATABLE_WIDTH_110 = 110;
    public static final int UI_COLUMN_DATATABLE_WIDTH_120 = 120;
    public static final int UI_COLUMN_DATATABLE_WIDTH_130 = 130;
    public static final int UI_COLUMN_DATATABLE_WIDTH_145 = 145;
    public static final int UI_COLUMN_DATATABLE_WIDTH_150 = 150;
    public static final int UI_COLUMN_DATATABLE_WIDTH_152 = 152;
    public static final int UI_COLUMN_DATATABLE_WIDTH_160 = 160;
    public static final int UI_COLUMN_DATATABLE_WIDTH_170 = 170;
    public static final int UI_COLUMN_DATATABLE_WIDTH_180 = 180;
    public static final int UI_COLUMN_DATATABLE_WIDTH_200 = 200;
    public static final int UI_COLUMN_DATATABLE_WIDTH_250 = 250;
    public static final int UI_COLUMN_DATATABLE_WIDTH_270 = 270;
    public static final int UI_COLUMN_DATATABLE_WIDTH_300 = 300;
    public static final int UI_COLUMN_DATATABLE_WIDTH_420 = 420;

    public static final String EXTENSION_FORMATO_XLSX = ".xlsx";
    public static final String EXTENSION_FORMATO_CSV = ".csv";

    public static final String FILTRO_FECHA_HORA_DESDE = " 00:00:00";
    public static final String FILTRO_FECHA_HORA_HASTA = " 23:59:59";

    public static final String JSON_CAMPO_SECURITY = "security";
    public static final String JSON_CAMPO_REQUEST_BODY = "requestBody";
    public static final String JSON_CAMPO_LISTA_RESPUESTA = "listaRespuesta";

    public static final String TAMANIO_TABLA_100P = "100%";
    public static final String TAMANIO_TABLA_100DP = "100.0%";

    public static final String CODIGO_VALOR_0 = "0";
    public static final String CODIGO_VALOR_1 = "1";

    public static final String PATTERN_MONTO_PRESTAMO = "^[0-9]+([.][0-9]+)?$";
    public static final String FORMATO_MONTO_PRESTAMO = "###,###,###.###";

    public static final String JSON_CAMPO_CAMPO_FECHA_CORTE = "ultima_reevaluacion";

    public static final String PLUS = "add";
    public static final String MINUS = "minus";

    public static final String ZERO = "0";
    public static final String UNO = "1";

    public static final String CODIGO_ESTADO_SOLICITUD = "codigoEstadoSolicitud";
    public static final String CODIGO_ACCION_CREAR = "crear";
    public static final String CODIGO_ACCION_ACTUALIZAR = "actualizar";
    public static final String CODIGO_ACCION_ELIMINAR = "eliminar";
    public static final String CODIGO_ESTADO_ACTIVO = "A";
    public static final String CODIGO_ESTADO_INACTIVO = "I";

    /* CODIGOS PARA MANEJO DE CADENAS */
    public static final String COD_CADENA_CORTADA = "...(*)";
    public static final String PUNTO = ".";
    public static final String DOS_PUNTOS = ":";
    public static final String SEPARADOR = "-";
    public static final String SUMA = "+";
    public static final String DIVISOR = "/";
    public static final String PORCENTAJE = "%";
    public static final String EXPRESION_OR = "||";
    public static final String EXPRESION_AND = "&&";
    public static final String UNDERLINE = "_";
    public static final String PIPELINE = "|";
    public static final String SALTO_LINEA = "\r\n";
    public static final String SALTO_LINEA_PARRAFO = "\n";
    public static final String IMAGEN_NO_DISPONIBLE = "no-disponible.png";
    public static final String MONEDA_SOLES = "S/.";
    public static final String COMA = ",";
    public static final String IGUAL = "=";
    public static final String PUNTO_Y_COMA = ";";
    public static final String REGEX_PIPELINE = "\\|";
    public static final String FORMATO_2_DIGITOS = "%02d";
    public static final String CONCATENACION_COMAS = ", ";
    public static final String CORCHETE_INICIO = "[";
    public static final String CORCHETE_FIN = "]";
    public static final String TEXTO_PUNTOS = ": ";
  }

  public abstract class SEGURO_VEHICULAR_ROYAL {

    private SEGURO_VEHICULAR_ROYAL() {}

    public static final String NOMBRE_OBJETO_JAVA = "ConsultaSeguroVehicularUI.java";

    public static final String CAMPO_ID = "id";
    public static final String CAMPO_STEP = "step";
    public static final String CAMPO_FECHA_REGISTRO = "fechaRegistro";
    public static final String CAMPO_NOMBRE = "nombre";
    public static final String CAMPO_APELLIDO = "apellido";
    public static final String CAMPO_TIPO_DOCUMENTO = "tipoDocumento";
    public static final String CAMPO_DOCUMENTO = "documento";
    public static final String CAMPO_DEPARTAMENTO = "departamento";
    public static final String CAMPO_CORREO = "correo";
    public static final String CAMPO_TELEFONO = "telefono";
    public static final String CAMPO_PLACA = "placa";
    public static final String CAMPO_ANHO_FABRICACION = "anhoFabricacion";
    public static final String CAMPO_OBJETO_COTIZACION = "objetoCotizacion";
    public static final String CAMPO_INFORMACION_BANCO = "informacionBanco";
    public static final String CAMPO_CODIGO_OFICINA = "codigoOficina";
    public static final String CAMPO_CODIGO_USUARIO = "codigoUsuario";
    public static final String CAMPO_HORARIO_CONTACTO = "horarioContacto";
    public static final String CAMPO_CANAL = "canal";
    public static final String CAMPO_FECHA_REGISTRO_DESDE = "fechaRegistroDesde";
    public static final String CAMPO_FECHA_REGISTRO_HASTA = "fechaRegistroHasta";
    public static final String CAMPO_INDICADOR_SI_ES_CLIENTE = "indicadorSiEsCliente";
    public static final String CAMPO_MARCA = "marca";
    public static final String CAMPO_MODELO = "modelo";
    public static final String CAMPO_VALOR_COMERCIAL = "valorComercial";
    public static final String CAMPO_INDICADOR_CONVERSION_MOTOR =
        "indicadorConversionMotorVehiculo";
    public static final String CAMPO_PLAN_NOMBRE = "planNombre";
    public static final String CAMPO_PLAN_CUOTAS_MONEDA = "planCuotasMoneda";
    public static final String CAMPO_PLAN_CUOTAS_MONTO = "planCuotasMonto";
    public static final String CAMPO_PRIMA_MONTO = "primaMonto";
    public static final String CAMPO_PRIMA_MONTO_MONEDA = "primaMontoMoneda";
    public static final String CAMPO_PRIMA_FRECUENCIA = "primaFrecuencia";
    public static final String CAMPO_URL_ORIGEN = "urlOrigen";

    public static final String COLUMNA_FECHA_REGISTRO = "FECHA REGISTRO";
    public static final String COLUMNA_STEP = "PASO COTIZACIÓN";
    public static final String COLUMNA_NOMBRE = "NOMBRE";
    public static final String COLUMNA_APELLIDO = "APELLIDO";
    public static final String COLUMNA_TIPO_DOCUMENTO = "TIPO DOCUMENTO";
    public static final String COLUMNA_DOCUMENTO = "NÚMERO DOCUMENTO";
    public static final String COLUMNA_DEPARTAMENTO = "DEPARTAMENTO";
    public static final String COLUMNA_CORREO = "CORREO";
    public static final String COLUMNA_TELEFONO = "TELÉFONO";
    public static final String COLUMNA_PLACA = "NÚMERO PLACA";
    public static final String COLUMNA_ANHO_FABRICACION = "AÑO FABRICACIÓN";
    public static final String COLUMNA_OBJETO_COTIZACION = "NRO COTIZACIÓN";
    public static final String COLUMNA_INFORMACION_BANCO = "INFORMACIÓN BANCO";
    public static final String COLUMNA_CODIGO_OFICINA = "CÓDIGO OFICINA";
    public static final String COLUMNA_CODIGO_USUARIO = "CÓDIGO USUARIO";
    public static final String COLUMNA_HORARIO_CONTACTO = "HORARIO CONTACTO";
    public static final String COLUMNA_CANAL = "CANAL";
    public static final String COLUMNA_INDICADOR_SI_ES_CLIENTE = "ES CLIENTE";
    public static final String COLUMNA_MARCA = "MARCA";
    public static final String COLUMNA_MODELO = "MODELO";
    public static final String COLUMNA_VALOR_COMERCIAL = "VALOR COMERCIAL";
    public static final String COLUMNA_INDICADOR_CONVERSION_MOTOR = "MOTOR CONVERTIDO";
    public static final String COLUMNA_PLAN_NOMBRE = "NOMBRE DEL PLAN";
    public static final String COLUMNA_PLAN_CUOTAS_MONEDA = "MONEDA DE CUOTAS";
    public static final String COLUMNA_PLAN_CUOTAS_MONTO = "MONTO DE CUOTA";
    public static final String COLUMNA_PRIMA_MONTO = "MONTO DE PRIMA";
    public static final String COLUMNA_PRIMA_MONTO_MONEDA = "MONEDA DE PRIMA";
    public static final String COLUMNA_PRIMA_FRECUENCIA = "FRECUENCIA DE PAGOS";
    public static final String COLUMNA_URL_ORIGEN_COTIZACION = "URL ORIGEN";
    public static final String CANAL_ZONA_PUBLICA = "CZ";

    public static final String SECCION_ARCHIVO_EXPORTACION = "seguro vehicular royal";
    public static final String NOMBRE_ARCHIVO_EXPORTACION = "SEGURO_VEHICULAR_ROYAL_";

    public static final String PRIMER_STEP = "QUOTATION_SIMULATION_STEP";
    public static final String SEGUNDO_STEP = "QUOTATION_STEP";
    public static final String TERCER_STEP = "QUOTATION_STEP_SAVED";
    public static final String CUARTO_STEP = "QUOTATION_CARD_SAVED";
    public static final String PRIMER_STEP_VISIBLE = "PASO 1 DATOS PERSONALES Y VEHICULO";
    public static final String SEGUNDO_STEP_VISIBLE = "PASO 2 SIMULACION DE PLANES";
    public static final String TERCER_STEP_VISIBLE = "PASO 3 COTIZACION FINAL GUARDADA";
    public static final String CUARTO_STEP_VISIBLE = "PASO 4 TARJETA GUARDADA";
  }

  public abstract class VENTA_SEGURO_ROYAL {

    private VENTA_SEGURO_ROYAL() {}

    public static final String NOMBRE_OBJETO_JAVA = "ConsultaVentaSeguroRoyalUI.java";

    public static final String CAMPO_ID = "id";
    public static final String CAMPO_FECHA_REGISTRO_COTIZACION = "fechaRegistroCotizacion";
    public static final String CAMPO_FECHA_REGISTRO_VENTA = "fechaRegistroVenta";
    public static final String CAMPO_NOMBRE = "nombre";
    public static final String CAMPO_APELLIDO = "apellido";
    public static final String CAMPO_DOCUMENTO = "documento";
    public static final String CAMPO_CORREO = "correo";
    public static final String CAMPO_TELEFONO = "telefono";
    public static final String CAMPO_PRODUCTO_ID = "productoId";
    public static final String CAMPO_PRODUCTO = "productoName";
    public static final String CAMPO_FECHA_REGISTRO_DESDE = "fechaRegistroDesde";
    public static final String CAMPO_FECHA_REGISTRO_HASTA = "fechaRegistroHasta";
    public static final String CAMPO_PLAN_CUOTAS_MONEDA = "planCuotasMoneda";
    public static final String CAMPO_PLAN_CUOTAS_PERIODO = "planCuotasPeriodo";
    public static final String CAMPO_PLAN_CUOTAS_MONTO = "planCuotasMonto";
    public static final String CAMPO_PLAN_NUMERO_COUOTAS_TOTAL = "planNumeroCuotasTotal";
    public static final String CAMPO_COTIZACION_ID = "cotizacionId";
    public static final String CAMPO_PRIMA_MONTO = "primaMonto";
    public static final String CAMPO_PRIMA_MONTO_MONEDA = "primaMontoMoneda";
    public static final String CAMPO_CANAL = "canal";
    public static final String CAMPO_SUBCANAL = "subChannel";
    public static final String CAMPO_CONTRATO = "contratoId";
    public static final String CAMPO_POLIZA = "polizaId";
    public static final String CAMPO_FECHA_VIG_INI = "fechaVigIni";
    public static final String CAMPO_FECHA_VIG_FIN = "fechaVigFin";
    public static final String CAMPO_METODO_PAGO = "metodoPago";
    public static final String CAMPO_NUMERO_TARJETA_CUENTA = "nroCuentaOTarjeta";
    public static final String CAMPO_INFORMACION_BANCO = "informacionBanco";
    public static final String CAMPO_CODIGO_OFICINA = "codigoOficina";
    public static final String CAMPO_SUMA_ASEGURADA = "sumaAsegurada";
    public static final String CAMPO_SUMA_ASEGURADA_MONEDA = "sumaAseguradaMoneda";
    public static final String CAMPO_CUPON = "couponCode";

    public static final String COLUMNA_FECHA_REGISTRO_COT = "FECHA COTIZACIÓN";
    public static final String COLUMNA_FECHA_REGISTRO_VEN = "FECHA VENTA";
    public static final String COLUMNA_NOMBRE = "NOMBRE";
    public static final String COLUMNA_APELLIDO = "APELLIDO";
    public static final String COLUMNA_DOCUMENTO = "NÚMERO DOCUMENTO";
    public static final String COLUMNA_CORREO = "CORREO";
    public static final String COLUMNA_TELEFONO = "TELÉFONO";
    public static final String COLUMNA_PRODUCTO_ID = "ID PRODUCTO";
    public static final String COLUMNA_PRODUCTO = "PRODUCTO";
    public static final String COLUMNA_FECHA_REGISTRO_DESDE = "FECHA INICIO";
    public static final String COLUMNA_FECHA_REGISTRO_HASTA = "FECHA FIN";
    public static final String COLUMNA_PLAN_CUOTAS_MONEDA = "MONEDA DE CUOTAS";
    public static final String COLUMNA_PLAN_CUOTAS_PERIODO = "PERIODO DE CUOTAS";
    public static final String COLUMNA_PLAN_CUOTAS_MONTO = "MONTO DE CUOTA";
    public static final String COLUMNA_PLAN_NUMERO_COUOTAS_TOTAL = "NÚMERO DE CUOTAS";
    public static final String COLUMNA_COTIZACION_ID = "ID COTIZACIÓN";
    public static final String COLUMNA_PRIMA_MONTO = "MONTO DE PRIMA";
    public static final String COLUMNA_PRIMA_MONTO_MONEDA = "MONEDA DE PRIMA";
    public static final String COLUMNA_CANAL = "CANAL";
    public static final String COLUMNA_SUBCANAL = "SUBCANAL";
    public static final String COLUMNA_FECHA_VIG_INI = "FECHA VIG. INICIO";
    public static final String COLUMNA_FECHA_VIG_FIN = "FECHA VIG. FIN";
    public static final String COLUMNA_CONTRATO = "CONTRATO";
    public static final String COLUMNA_POLIZA = "PÓLIZA";
    public static final String COLUMNA_METODO_PAGO = "MÉTODO DE PAGO";
    public static final String COLUMNA_NUMERO_CUOTAS = "NÚMERO DE CUOTAS";
    public static final String COLUMNA_NUMERO_TARJETA_CUENTA = "NRO. TARJETA/CUENTA";
    public static final String COLUMNA_INFORMACION_BANCO = "CÓDIGO BANCO";
    public static final String COLUMNA_CODIGO_OFICINA = "CÓDIGO OFICINA";
    public static final String COLUMNA_SUMA_ASEGURADA_MONEDA = "MONEDA SUMA ASEGURADA";
    public static final String COLUMNA_SUMA_ASEGURADA = "SUMA ASEGURADA";
    public static final String COLUMNA_CUPON = "CÓDIGO CUPÓN";

    public static final String SECCION_ARCHIVO_EXPORTACION = "venta seguro royal";
    public static final String NOMBRE_ARCHIVO_EXPORTACION = "VENTA_SEGURO_ROYAL_";
  }

  public abstract static class LEAD_UI_CONSTANTS {

    public static final String CODE_FIELD = "codigo";
    public static final String REGISTER_DATE_FIELD = "fechaHoraRegistro";
    public static final String REGISTER_DATE_UPDATE_FIELD = "fechaHoraActualizacion";
    public static final String RUC_FIELD = "ruc";
    public static final String BUSINESS_NAME_FIELD = "razonSocial";
    public static final String EMAIL_FIELD = "correo";
    public static final String STATE_FIELD = "estado";
    public static final String DETAIL_FIELD = "detalle";
    public static final String EDIT_FIELD = "editar";
    public static final String OBJECT_FIELD = "objeto";
  }

  public abstract class SEGURO_HOGAR_ROYAL {

    private SEGURO_HOGAR_ROYAL() {}

    public static final String NOMBRE_OBJETO_JAVA = "ConsultaSeguroHogarUI.java";
    public static final String SECCION_ARCHIVO_EXPORTACION = "seguro hogar royal";
    public static final String NOMBRE_ARCHIVO_EXPORTACION = "SEGURO_HOGAR_ROYAL_";

    public static final String CAMPO_ID = "id";
    public static final String CAMPO_FECHA_REGISTRO = "fechaRegistro";
    public static final String CAMPO_NOMBRE = "nombre";
    public static final String CAMPO_APELLIDO = "apellido";
    public static final String CAMPO_TIPO_DOCUMENTO = "tipoDocumento";
    public static final String CAMPO_DOCUMENTO = "documento";
    public static final String CAMPO_CORREO = "correo";
    public static final String CAMPO_TELEFONO = "telefono";
    public static final String CAMPO_TIPO_PROPIEDAD = "tipoPropiedad";
    public static final String CAMPO_PROPIEDAD = "propiedad";
    public static final String CAMPO_DIRECCION = "direccion";
    public static final String CAMPO_ZONA = "zona";
    public static final String CAMPO_DISTRITO = "distrito";
    public static final String CAMPO_DEPARTAMENTO = "departamento";
    public static final String CAMPO_PROVINCIA = "provincia";
    public static final String CAMPO_MONEDA = "moneda";
    public static final String CAMPO_MONTO = "monto";
    public static final String CAMPO_TIPO_USO = "tipoUso";
    public static final String CAMPO_USO = "uso";
    public static final String CAMPO_TIPO_MATERIAL = "tipoMaterialConstruido";
    public static final String CAMPO_MATERIAL = "materialConstruido";
    public static final String CAMPO_ANTIGUEDAD = "antiguedad";
    public static final String CAMPO_PISOS = "numeroPisos";
    public static final String CAMPO_FECHA_REGISTRO_DESDE = "fechaRegistroDesde";
    public static final String CAMPO_FECHA_REGISTRO_HASTA = "fechaRegistroHasta";
    public static final String CAMPO_OBJETO_COTIZACION = "objetoCotizacion";
    public static final String CAMPO_INFORMACION_BANCO = "informacionBanco";
    public static final String CAMPO_CODIGO_OFICINA = "codigoOficina";
    public static final String CAMPO_CODIGO_USUARIO = "codigoUsuario";
    public static final String CAMPO_HORARIO_CONTACTO = "horarioContacto";
    public static final String CAMPO_CANAL = "canal";
    public static final String CAMPO_INDICADOR_SI_ES_CLIENTE = "indicadorSiEsCliente";
    public static final String CAMPO_POSEEDOR = "poseedor";
    public static final String CAMPO_POSEEDOR_TIPO = "poseedorTipo";
    public static final String CAMPO_PLAN_CUOTAS_MONEDA = "planCuotasMoneda";
    public static final String CAMPO_PLAN_CUOTAS_MONTO = "planCuotasMonto";
    public static final String CAMPO_PRIMA_MONTO = "primaMonto";
    public static final String CAMPO_PRIMA_MONTO_MONEDA = "primaMontoMoneda";

    public static final String COLUMNA_FECHA_REGISTRO = "FECHA REGISTRO";
    public static final String COLUMNA_NOMBRE = "NOMBRE";
    public static final String COLUMNA_APELLIDO = "APELLIDO";
    public static final String COLUMNA_TIPO_DOCUMENTO = "TIPO DOCUMENTO";
    public static final String COLUMNA_DOCUMENTO = "NÚMERO DOCUMENTO";
    public static final String COLUMNA_CORREO = "CORREO";
    public static final String COLUMNA_TELEFONO = "TELÉFONO";
    public static final String COLUMNA_OBJETO_COTIZACION = "NRO COTIZACIÓN";

    public static final String COLUMNA_TIPO_PROPIEDAD = "TIPO PROPIEDAD";
    public static final String COLUMNA_PROPIEDAD = "PROPIEDAD";
    public static final String COLUMNA_DIRECCION = "DIRECCIÓN";
    public static final String COLUMNA_ZONA = "ZONA";
    public static final String COLUMNA_DISTRITO = "DISTRITO";
    public static final String COLUMNA_DEPARTAMENTO = "DEPARTAMENTO";
    public static final String COLUMNA_PROVINCIA = "PROVINCIA";
    public static final String COLUMNA_MONEDA = "MONEDA";
    public static final String COLUMNA_MONTO = "VALOR COMERCIAL";
    public static final String COLUMNA_TIPO_USO = "TIPO DE USO";
    public static final String COLUMNA_USO = "UTILIZACIÓN";
    public static final String COLUMNA_TIPO_MATERIAL = "TIPO MATERIAL";
    public static final String COLUMNA_MATERIAL = "MATERIAL";
    public static final String COLUMNA_ANTIGUEDAD = "ANTIGUEDAD (AÑOS)";
    public static final String COLUMNA_PISOS = "NRO. PISOS";

    public static final String COLUMNA_INFORMACION_BANCO = "INFORMACIÓN BANCO";
    public static final String COLUMNA_CODIGO_OFICINA = "CÓDIGO OFICINA";
    public static final String COLUMNA_CODIGO_USUARIO = "CÓDIGO USUARIO";
    public static final String COLUMNA_HORARIO_CONTACTO = "HORARIO CONTACTO";
    public static final String COLUMNA_CANAL = "CANAL";
    public static final String COLUMNA_INDICADOR_SI_ES_CLIENTE = "ES CLIENTE";
    public static final String COLUMNA_POSEEDOR = "POSEEDOR";
    public static final String COLUMNA_POSEEDOR_TIPO = "TIPO POSEEDOR";
    public static final String COLUMNA_PLAN_CUOTAS_MONEDA = "MONEDA DE CUOTAS";
    public static final String COLUMNA_PLAN_CUOTAS_MONTO = "MONTO DE CUOTA";
    public static final String COLUMNA_PRIMA_MONTO = "MONTO DE PRIMA";
    public static final String COLUMNA_PRIMA_MONTO_MONEDA = "MONEDA DE PRIMA";
  }

  public abstract class TARJETA_GARANTIZADA {

    private TARJETA_GARANTIZADA() {}

    public static final String NOMBRE_OBJETO_JAVA = "ConsultaTarjetaGarantizadaUI.java";
    public static final String NOMBRE_REPORTE = "Consultar Tarjeta Garantizada";

    public static final String CAMPO_ID = "id";
    public static final String CAMPO_CODIGO_CENTRAL = "codigoCentral";
    public static final String CAMPO_TIPO_DOCUMENTO = "tipoDocumento";
    public static final String CAMPO_NRO_DOCUMENTO = "nroDocumento";
    public static final String CAMPO_NOMBRES = "nombres";
    public static final String CAMPO_APELLIDO_PATERNO = "apellidoPaterno";
    public static final String CAMPO_APELLIDO_MATERNO = "apellidoMaterno";
    public static final String CAMPO_NOMBRE_COMPLETO = "nombreCompleto";
    public static final String CAMPO_FECHA_REGISTRO = "fechaRegistro";
    public static final String CAMPO_NRO_TARJETA = "nroTarjeta";
    public static final String CAMPO_REGISTRO_GESTOR = "registroGestor";
    public static final String CAMPO_TELEFONO = "telefono";
    public static final String CAMPO_CORREO = "correo";
    public static final String CAMPO_ESTADO = "estado";
    public static final String CAMPO_CODIGO_OFICINA = "codigoOficina";
    public static final String CAMPO_DESCRIPCION_OFICINA = "descripcionOficina";
    public static final String CAMPO_OFICINA_RECOJO = "oficinaRecojo";
    public static final String CAMPO_LINEA_CREDITO = "lineaCredito";
    public static final String CAMPO_TIPO_TARJETA = "tipoTarjeta";
    public static final String CAMPO_FECHA_ALTA = "fechaAlta";
    public static final String CAMPO_CUENTA_RESPALDO = "cuentaRespaldo";
    public static final String CAMPO_CANAL = "canal";
    public static final String CAMPO_FECHA_REGISTRO_DESDE = "fechaRegistroDesde";
    public static final String CAMPO_FECHA_REGISTRO_HASTA = "fechaRegistroHasta";
    public static final String CAMPO_ORDENAMIENTO_CANAL = "ordenamientoCanal";
    public static final String CAMPO_FECHA_INICIO_CAMPANIA = "fechaInicioCampania";
    public static final String CAMPO_FECHA_FIN_CAMPANIA = "fechaFinCampania";

    public static final String COLUMNA_CODIGO_CENTRAL = "CODIGO CENTRAL";
    public static final String COLUMNA_TIPO_DOCUMENTO = "TIPO DOCUMENTO";
    public static final String COLUMNA_NRO_DOCUMENTO = "NUMERO DOCUMENTO";
    public static final String COLUMNA_NOMBRE_COMPLETO = "NOMBRE COMPLETO";
    public static final String COLUMNA_FECHA_REGISTRO = "FECHA REGISTRO";
    public static final String COLUMNA_NRO_TARJETA = "TARJETA";
    public static final String COLUMNA_REGISTRO_GESTOR = "REGISTRO GESTOR";
    public static final String COLUMNA_TELEFONO = "TELEFONO";
    public static final String COLUMNA_CORREO = "CORREO";
    public static final String COLUMNA_ESTADO = "ESTADO";
    public static final String COLUMNA_CODIGO_OFICINA = "CODIGO OFICINA";
    public static final String COLUMNA_DESCRIPCION_OFICINA = "DESCRIPCION OFICINA";
    public static final String COLUMNA_OFICINA_RECOJO = "OFICINA RECOJO";
    public static final String COLUMNA_LINEA_CREDITO = "LINEA DE CREDITO";
    public static final String COLUMNA_TIPO_TARJETA = "TIPO DE TARJETA";
    public static final String COLUMNA_FECHA_ALTA = "FECHA ALTA";
    public static final String COLUMNA_CUENTA_RESPALDO = "CUENTA RESPALDO";
    public static final String COLUMNA_CANAL = "CANAL";

    public static final String SECCION_ARCHIVO_EXPORTACION = "tarjeta garantizada";
    public static final String NOMBRE_ARCHIVO_EXPORTACION = "TARJETA_GARANTIZADA_";
  }

  public abstract class CANCELACION_PRESTAMO_COMERCIAL {

    private CANCELACION_PRESTAMO_COMERCIAL() {}

    public static final String NOMBRE_OBJETO_JAVA = "ConsultaPrestamoComercialUI.java";
    public static final String NOMBRE_REPORTE = "Consulta Cancelación de Préstamo Comercial";

    public static final String CAMPO_CODIGO_CENTRAL = "codigoCentral";
    public static final String CAMPO_NOMBRE_CLIENTE = "nombresApellidos";
    public static final String CAMPO_TIPO_DOCUMENTO = "tipoDocumento";
    public static final String CAMPO_NUMERO_DOCUMENTO = "numeroDocumento";
    public static final String CAMPO_FECHA_REGISTRO = "fechaHoraRegistro";
    public static final String CAMPO_SUB_PRODUCTO = "subProducto";
    public static final String CAMPO_NUMERO_CONTRATO = "nroContrato";
    public static final String CAMPO_CUENTA_CARGO = "ctaCargoAsociada";
    public static final String CAMPO_CORREO = "correo";
    public static final String CAMPO_CELULAR = "celularCliente";
    public static final String CAMPO_ESTADO_SOLICITUD = "estado";
    public static final String CAMPO_MOTIVO_RECHAZO = "motivoRechazo";
    public static final String CAMPO_DETALLE_OTROS = "otroMotivoRechazo";
    public static final String CAMPO_DEUDA_TOTAL_PRESTAMO = "montoPrestamo";
    public static final String CAMPO_FECHA_MODIFICACION = "fechaHoraModificacion";
    public static final String CAMPO_REGISTRO_EXTERNO = "registroModificacion";
    public static final String CAMPO_CANAL = "canal";
    public static final String CAMPO_OBJETO = "objeto";
    public static final String CAMPO_FECHA_REGISTRO_DESDE = "fechaRegistroDesde";
    public static final String CAMPO_FECHA_REGISTRO_HASTA = "fechaRegistroHasta";

    public static final String COLUMNA_CODIGO_CENTRAL = "Código Central";
    public static final String COLUMNA_NOMBRE_CLIENTE = "Nombre Cliente";
    public static final String COLUMNA_TIPO_DOCUMENTO = "Tipo Documento";
    public static final String COLUMNA_NUMERO_DOCUMENTO = "Número de Documento";
    public static final String COLUMNA_FECHA_REGISTRO = "Fecha Registro";
    public static final String COLUMNA_SUB_PRODUCTO = "Sub Producto";
    public static final String COLUMNA_NUMERO_CONTRATO = "Número de contrato";
    public static final String COLUMNA_CUENTA_CARGO = "Cuenta de cargo";
    public static final String COLUMNA_CORREO = "Correo";
    public static final String COLUMNA_CELULAR = "Celular";
    public static final String COLUMNA_ESTADO_SOLICITUD = "Estado Solicitud";
    public static final String COLUMNA_MOTIVO_RECHAZO = "Motivo de Rechazo";
    public static final String COLUMNA_DETALLE_OTROS = "Detalle Otros";
    public static final String COLUMNA_DEUDA_TOTAL_PRESTAMO = "Deuda Total Prestamo";
    public static final String COLUMNA_FECHA_MODIFICACION = "Fecha modificación";
    public static final String COLUMNA_REGISTRO_EXTERNO = "Registro externo";
    public static final String COLUMNA_CANAL = "Canal";

    public static final String SECCION_ARCHIVO_EXPORTACION = "Cancelación de Préstamo Comercial";
    public static final String NOMBRE_ARCHIVO_EXPORTACION = "CANCELACION_PRESTAMO_COMERCIAL_";
  }

  public abstract class CANCELACION_PRESTAMO_JURIDICO {

    private CANCELACION_PRESTAMO_JURIDICO() {}

    public static final String NOMBRE_OBJETO_JAVA = "ConsultaCancelacionPrestamoJuridicoUI.java";
    public static final String NOMBRE_REPORTE = "Consulta Cancelación de Préstamo Jurídico";

    public static final String CAMPO_CODIGO_CENTRAL = "codigoCentral";
    public static final String CAMPO_NOMBRE_EMPRESA = "nombreEmpresa";
    public static final String CAMPO_CODIGO_EMPRESA = "codigoEmpresa";
    public static final String CAMPO_CODIGO_USUARIO = "codigoUsuario";
    public static final String CAMPO_TIPO_DOCUMENTO = "tipoDocumento";
    public static final String CAMPO_NUMERO_DOCUMENTO = "numeroDocumento";
    public static final String CAMPO_FECHA_REGISTRO = "fechaHoraRegistro";
    public static final String CAMPO_SUB_PRODUCTO = "subProducto";
    public static final String CAMPO_NUMERO_CONTRATO = "nroContrato";
    public static final String CAMPO_CUENTA_CARGO = "ctaCargoAsociada";
    public static final String CAMPO_CORREO = "correo";
    public static final String CAMPO_CELULAR = "celularCliente";
    public static final String CAMPO_ESTADO_SOLICITUD = "estado";
    public static final String CAMPO_MOTIVO_RECHAZO = "motivoRechazo";
    public static final String CAMPO_DETALLE_OTROS = "otroMotivoRechazo";
    public static final String CAMPO_MONEDA = "moneda";
    public static final String CAMPO_DEUDA_TOTAL_PRESTAMO = "montoPrestamo";
    public static final String CAMPO_FECHA_MODIFICACION = "fechaHoraModificacion";
    public static final String CAMPO_REGISTRO_EXTERNO = "registroModificacion";
    public static final String CAMPO_CANAL = "canal";
    public static final String CAMPO_DETALLE = "detalle";
    public static final String CAMPO_OBJETO = "objeto";
    public static final String CAMPO_FECHA_REGISTRO_DESDE = "fechaHoraRegistro_1";
    public static final String CAMPO_FECHA_REGISTRO_HASTA = "fechaHoraRegistro_2";

    public static final String COLUMNA_CODIGO_CENTRAL = "Código Central";
    public static final String COLUMNA_NOMBRE_EMPRESA = "Nombre Empresa";
    public static final String COLUMNA_CODIGO_EMPRESA = "Cod Empresa / Cod referencia";
    public static final String COLUMNA_CODIGO_USUARIO = "Cod Usuario";
    public static final String COLUMNA_TIPO_DOCUMENTO = "Tipo Documento";
    public static final String COLUMNA_NUMERO_DOCUMENTO = "Número de Documento";
    public static final String COLUMNA_FECHA_REGISTRO = "Fecha Registro";
    public static final String COLUMNA_SUB_PRODUCTO = "Sub Producto";
    public static final String COLUMNA_NUMERO_CONTRATO = "Número de contrato";
    public static final String COLUMNA_CUENTA_CARGO = "Cuenta de Cargo";
    public static final String COLUMNA_CORREO = "Correo usuario";
    public static final String COLUMNA_CELULAR = "Celular";
    public static final String COLUMNA_ESTADO_SOLICITUD = "Estado Solicitud";
    public static final String COLUMNA_MOTIVO_RECHAZO = "Motivo Rechazo";
    public static final String COLUMNA_DETALLE_OTROS = "Detalle Otros";
    public static final String COLUMNA_MONEDA = "Moneda";
    public static final String COLUMNA_DEUDA_TOTAL_PRESTAMO = "Deuda Total Prestamo";
    public static final String COLUMNA_FECHA_MODIFICACION = "Fecha Modificación";
    public static final String COLUMNA_REGISTRO_EXTERNO = "Registro Externo";
    public static final String COLUMNA_CANAL = "Canal";
    public static final String COLUMNA_DETALLE = "Detalle";

    public static final String SECCION_ARCHIVO_EXPORTACION = "Cancelación de Préstamo Jurídico";
    public static final String NOMBRE_ARCHIVO_EXPORTACION = "CANCELACION_PRESTAMO_JURIDICO_";
    public static final String NOMBRE_DETALLE_REPORTE = "Detalle de Préstamo Jurídico";
    public static final String NOMBRE_BANDEJA_REPORTE = "Bandeja de Préstamo Jurídico";

    public static final String DICTAMEN_MENSAJE_RECHAZAR = "Rechazar | Cancelación de Préstamo";
    public static final String DICTAMEN_ATENCION = "Atención";
    public static final String DICTAMEN_ESTADO_SOLICITUD = "Estado Solicitud:";
    public static final String DICTAMEN_LABEL = "Label";
    public static final String DICTAMEN_DATOS_TITULAR = "Datos del titular";
    public static final String DICTAMEN_EMPRESA = "Empresa:";
    public static final String DICTAMEN_CODIGO_CENTRAL = "Código Central:";
    public static final String DICTAMEN_TIPO_DOCUMENTO = "Tipo de Documento:";
    public static final String DICTAMEN_NUMERO_DOCUMENTO = "Número de Documento:";
    public static final String DICTAMEN_CORREO = "Correo:";
    public static final String DICTAMEN_DATOS_PRESTAMO = "Datos del Préstamo";
    public static final String DICTAMEN_NRO_CONTRATO = "Nro Contrato:";
    public static final String DICTAMEN_SUB_PRODUCTO = "Sub Producto:";
    public static final String DICTAMEN_CUENTA_CARGO = "Cuenta de Cargo:";
    public static final String DICTAMEN_CELULAR = "Celular:";
    public static final String DICTAMEN_BOTON_PROCESAR = "Procesar";
    public static final String DICTAMEN_BOTON_RECHAZAR = "Rechazar";

    public static final String DICTAMEN_DENEGADO_MSJ_SELECCIONE = "--SELECCIONE--";
    public static final String DICTAMEN_DENEGADO_MSJ_ESC_MOTIVO_RECHAZO =
        "Escriba el motivo de rechazo.";
    public static final String DICTAMEN_DENEGADO_MSJ_ESC_MOTIVO_PRESTAMO =
        "Escriba el monto del prestamo.";
    public static final String DICTAMEN_DENEGADO_MSJ_ESC_CARACTER_VALIDO =
        "Escriba solo caracteres válidos.";
    public static final String DICTAMEN_DENEGADO_CAMPO_DESCRIPCION = "descripcion";
    public static final String DICTAMEN_DENEGADO_CAMPO_TIPO_DIVISA_MONTO = "tipoDivisaMonto";
    public static final String DICTAMEN_DENEGADO_MSJ_MOTIVO_RECHAZO = "Motivo de Rechazo:";
    public static final String DICTAMEN_DENEGADO_MSJ_ESPECIFIQUE =
        "Especifique cualquier otro motivo de rechazo";
    public static final String DICTAMEN_DENEGADO_MSJ_SELECCIONE_MONEDA = "Seleccione moneda:";
    public static final String DICTAMEN_DENEGADO_MSJ_INGRESE =
        "Ingrese la deuda total del prestamo:";
    public static final String DICTAMEN_DENEGADO_BOTON_ACEPTAR = "Aceptar";
  }

  public abstract class CANCELACION_LINEA_CREDITO {

    private CANCELACION_LINEA_CREDITO() {}

    public static final String NOMBRE_OBJETO_JAVA =
        "ConsultaCancelacionCancelacionLineaCreditoUI.java";
    public static final String NOMBRE_REPORTE = "Consulta Cancelación de Línea de Crédito";

    public static final String CAMPO_CODIGO_CENTRAL = "codigoCentral";
    public static final String CAMPO_NOMBRE_CLIENTE = "nombresApellidos";
    public static final String CAMPO_TIPO_DOCUMENTO = "tipoDocumento";
    public static final String CAMPO_NUMERO_DOCUMENTO = "numeroDocumento";
    public static final String CAMPO_FECHA_REGISTRO = "fechaHoraRegistro";
    public static final String CAMPO_SUB_PRODUCTO = "subProducto";
    public static final String CAMPO_NUMERO_CONTRATO = "nroContrato";
    public static final String CAMPO_CUENTA_CARGO = "ctaCargoAsociada";
    public static final String CAMPO_CORREO = "correo";
    public static final String CAMPO_CELULAR = "celularCliente";
    public static final String CAMPO_ESTADO_SOLICITUD = "estado";
    public static final String CAMPO_MOTIVO_RECHAZO = "motivoRechazo";
    public static final String CAMPO_DETALLE_OTROS = "otroMotivoRechazo";
    public static final String CAMPO_MONEDA = "moneda";
    public static final String CAMPO_DEUDA_TOTAL_PRESTAMO = "montoPrestamo";
    public static final String CAMPO_FECHA_MODIFICACION = "fechaHoraModificacion";
    public static final String CAMPO_REGISTRO_EXTERNO = "registroModificacion";
    public static final String CAMPO_CANAL = "canal";
    public static final String CAMPO_DETALLE = "detalle";
    public static final String CAMPO_OBJETO = "objeto";
    public static final String CAMPO_FECHA_REGISTRO_DESDE = "fechaHoraRegistro_1";
    public static final String CAMPO_FECHA_REGISTRO_HASTA = "fechaHoraRegistro_2";

    public static final String COLUMNA_CODIGO_CENTRAL = "Código Central";
    public static final String COLUMNA_NOMBRE_CLIENTE = "Nombre Cliente";
    public static final String COLUMNA_TIPO_DOCUMENTO = "Tipo Documento";
    public static final String COLUMNA_NUMERO_DOCUMENTO = "Número de Documento";
    public static final String COLUMNA_FECHA_REGISTRO = "Fecha Registro";
    public static final String COLUMNA_SUB_PRODUCTO = "Sub Producto";
    public static final String COLUMNA_NUMERO_CONTRATO = "Número de contrato";
    public static final String COLUMNA_CUENTA_CARGO = "Cuenta de Cargo";
    public static final String COLUMNA_CORREO = "Correo";
    public static final String COLUMNA_CELULAR = "Celular";
    public static final String COLUMNA_ESTADO_SOLICITUD = "Estado Solicitud";
    public static final String COLUMNA_MOTIVO_RECHAZO = "Motivo Rechazo";
    public static final String COLUMNA_DETALLE_OTROS = "Detalle Otros";
    public static final String COLUMNA_MONEDA = "Moneda";
    public static final String COLUMNA_DEUDA_TOTAL_PRESTAMO = "Importe Total de Línea";
    public static final String COLUMNA_FECHA_MODIFICACION = "Fecha Modificación";
    public static final String COLUMNA_REGISTRO_EXTERNO = "Registro Externo";
    public static final String COLUMNA_CANAL = "Canal";
    public static final String COLUMNA_DETALLE = "Detalle";

    public static final String SECCION_ARCHIVO_EXPORTACION = "Cancelación de Línea de Crédito";
    public static final String NOMBRE_ARCHIVO_EXPORTACION = "CANCELACION_LINEA_CREDITO_";
    public static final String NOMBRE_DETALLE_REPORTE = "Detalle de Línea de Crédito";
    public static final String NOMBRE_BANDEJA_REPORTE = "Bandeja de Línea de Crédito";

    public static final String DICTAMEN_MENSAJE_RECHAZAR =
        "Rechazar | Cancelación de Línea de Crédito";
    public static final String DICTAMEN_ATENCION = "Atención";
    public static final String DICTAMEN_ESTADO_SOLICITUD = "Estado Solicitud:";
    public static final String DICTAMEN_LABEL = "Label";
    public static final String DICTAMEN_DATOS_TITULAR = "Datos del titular";
    public static final String DICTAMEN_EMPRESA = "Empresa:";
    public static final String DICTAMEN_CODIGO_CENTRAL = "Código Central:";
    public static final String DICTAMEN_TIPO_DOCUMENTO = "Tipo de Documento:";
    public static final String DICTAMEN_NUMERO_DOCUMENTO = "Número de Documento:";
    public static final String DICTAMEN_CORREO = "Correo:";
    public static final String DICTAMEN_DATOS_PRESTAMO = "Datos del Préstamo";
    public static final String DICTAMEN_NRO_CONTRATO = "Nro Contrato:";
    public static final String DICTAMEN_SUB_PRODUCTO = "Sub Producto:";
    public static final String DICTAMEN_CUENTA_CARGO = "Cuenta de Cargo:";
    public static final String DICTAMEN_CELULAR = "Celular:";
    public static final String DICTAMEN_BOTON_PROCESAR = "Procesar";
    public static final String DICTAMEN_BOTON_RECHAZAR = "Rechazar";

    public static final String DICTAMEN_DENEGADO_MSJ_SELECCIONE = "--SELECCIONE--";
    public static final String DICTAMEN_DENEGADO_MSJ_ESC_MOTIVO_RECHAZO =
        "Escriba el motivo de rechazo.";
    public static final String DICTAMEN_DENEGADO_MSJ_ESC_MOTIVO_PRESTAMO =
        "Escriba el monto del prestamo.";
    public static final String DICTAMEN_DENEGADO_MSJ_ESC_CARACTER_VALIDO =
        "Escriba solo caracteres válidos.";
    public static final String DICTAMEN_DENEGADO_CAMPO_DESCRIPCION = "descripcion";
    public static final String DICTAMEN_DENEGADO_CAMPO_TIPO_DIVISA_MONTO = "tipoDivisaMonto";
    public static final String DICTAMEN_DENEGADO_MSJ_MOTIVO_RECHAZO = "Motivo de Rechazo:";
    public static final String DICTAMEN_DENEGADO_MSJ_ESPECIFIQUE =
        "Especifique cualquier otro motivo de rechazo";
    public static final String DICTAMEN_DENEGADO_MSJ_SELECCIONE_MONEDA = "Seleccione moneda:";
    public static final String DICTAMEN_DENEGADO_MSJ_INGRESE =
        "Ingrese la deuda total del prestamo:";
    public static final String DICTAMEN_DENEGADO_BOTON_ACEPTAR = "Aceptar";
  }

  public abstract class CANCELACION_TARJETA {
    private CANCELACION_TARJETA() {}

    public static final String CAMPO_TIPO_GARANTIA = "tipoGarantia";
    public static final String COLUMNA_TIPO_GARANTIA = "Tipo de Garantía";
  }

  public abstract class SEGURO_NEGOCIO_A_TU_MEDIDA_ROYAL {

    private SEGURO_NEGOCIO_A_TU_MEDIDA_ROYAL() {}

    public static final String NOMBRE_OBJETO_JAVA = "ConsultaSeguroNegocioATuMedidaRoyalUI.java";
    public static final String SECCION_ARCHIVO_EXPORTACION = "seguro negocio a tu medida royal";
    public static final String NOMBRE_ARCHIVO_EXPORTACION = "SEGURO_NEGOCIO_A_TU_MEDIDA_ROYAL_";

    public static final String CAMPO_ID = "id";
    public static final String CAMPO_FECHA_REGISTRO = "fechaRegistro";
    public static final String CAMPO_NOMBRE = "nombre";
    public static final String CAMPO_APELLIDO = "apellido";
    public static final String CAMPO_TIPO_DOCUMENTO = "tipoDocumento";
    public static final String CAMPO_DOCUMENTO = "documento";
    public static final String CAMPO_CORREO = "correo";
    public static final String CAMPO_TELEFONO = "telefono";
    public static final String CAMPO_TIPO_PROPIEDAD = "tipoPropiedad";
    public static final String CAMPO_PROPIEDAD = "propiedad";
    public static final String CAMPO_DIRECCION = "direccion";
    public static final String CAMPO_ZONA = "zona";
    public static final String CAMPO_DISTRITO = "distrito";
    public static final String CAMPO_DEPARTAMENTO = "departamento";
    public static final String CAMPO_PROVINCIA = "provincia";
    public static final String CAMPO_MONEDA = "moneda";
    public static final String CAMPO_MONTO = "monto";
    public static final String CAMPO_TIPO_USO = "tipoUso";
    public static final String CAMPO_USO = "uso";
    public static final String CAMPO_TIPO_MATERIAL = "tipoMaterialConstruido";
    public static final String CAMPO_MATERIAL = "materialConstruido";
    public static final String CAMPO_ANTIGUEDAD = "antiguedad";
    public static final String CAMPO_PISOS = "numeroPisos";
    public static final String CAMPO_FECHA_REGISTRO_DESDE = "fechaRegistroDesde";
    public static final String CAMPO_FECHA_REGISTRO_HASTA = "fechaRegistroHasta";
    public static final String CAMPO_OBJETO_COTIZACION = "objetoCotizacion";
    public static final String CAMPO_INFORMACION_BANCO = "informacionBanco";
    public static final String CAMPO_CODIGO_OFICINA = "codigoOficina";
    public static final String CAMPO_CODIGO_USUARIO = "codigoUsuario";
    public static final String CAMPO_HORARIO_CONTACTO = "horarioContacto";
    public static final String CAMPO_CANAL = "canal";
    public static final String CAMPO_INDICADOR_SI_ES_CLIENTE = "indicadorSiEsCliente";
    public static final String CAMPO_POSEEDOR = "poseedor";
    public static final String CAMPO_POSEEDOR_TIPO = "poseedorTipo";
    public static final String CAMPO_PLAN_CUOTAS_MONEDA = "planCuotasMoneda";
    public static final String CAMPO_PLAN_CUOTAS_MONTO = "planCuotasMonto";
    public static final String CAMPO_PRIMA_MONTO = "primaMonto";
    public static final String CAMPO_PRIMA_MONTO_MONEDA = "primaMontoMoneda";

    public static final String COLUMNA_FECHA_REGISTRO = "FECHA REGISTRO";
    public static final String COLUMNA_NOMBRE = "NOMBRE";
    public static final String COLUMNA_APELLIDO = "APELLIDO";
    public static final String COLUMNA_TIPO_DOCUMENTO = "TIPO DOCUMENTO";
    public static final String COLUMNA_DOCUMENTO = "NÚMERO DOCUMENTO";
    public static final String COLUMNA_CORREO = "CORREO";
    public static final String COLUMNA_TELEFONO = "TELÉFONO";
    public static final String COLUMNA_OBJETO_COTIZACION = "NRO COTIZACIÓN";

    public static final String COLUMNA_TIPO_PROPIEDAD = "TIPO PROPIEDAD";
    public static final String COLUMNA_PROPIEDAD = "PROPIEDAD";
    public static final String COLUMNA_DIRECCION = "DIRECCIÓN";
    public static final String COLUMNA_ZONA = "ZONA";
    public static final String COLUMNA_DISTRITO = "DISTRITO";
    public static final String COLUMNA_DEPARTAMENTO = "DEPARTAMENTO";
    public static final String COLUMNA_PROVINCIA = "PROVINCIA";
    public static final String COLUMNA_MONEDA = "MONEDA";
    public static final String COLUMNA_MONTO = "VALOR COMERCIAL";
    public static final String COLUMNA_TIPO_USO = "TIPO DE USO";
    public static final String COLUMNA_USO = "UTILIZACIÓN";
    public static final String COLUMNA_TIPO_MATERIAL = "TIPO MATERIAL";
    public static final String COLUMNA_MATERIAL = "MATERIAL";
    public static final String COLUMNA_ANTIGUEDAD = "ANTIGUEDAD (AÑOS)";
    public static final String COLUMNA_PISOS = "NRO. PISOS";

    public static final String COLUMNA_INFORMACION_BANCO = "INFORMACIÓN BANCO";
    public static final String COLUMNA_CODIGO_OFICINA = "CÓDIGO OFICINA";
    public static final String COLUMNA_CODIGO_USUARIO = "CÓDIGO USUARIO";
    public static final String COLUMNA_HORARIO_CONTACTO = "HORARIO CONTACTO";
    public static final String COLUMNA_CANAL = "CANAL";
    public static final String COLUMNA_INDICADOR_SI_ES_CLIENTE = "ES CLIENTE";
    public static final String COLUMNA_POSEEDOR = "POSEEDOR";
    public static final String COLUMNA_POSEEDOR_TIPO = "TIPO POSEEDOR";
    public static final String COLUMNA_PLAN_CUOTAS_MONEDA = "MONEDA DE CUOTAS";
    public static final String COLUMNA_PLAN_CUOTAS_MONTO = "MONTO DE CUOTA";
    public static final String COLUMNA_PRIMA_MONTO = "MONTO DE PRIMA";
    public static final String COLUMNA_PRIMA_MONTO_MONEDA = "MONEDA DE PRIMA";
  }

  public abstract class BENEFICIOS_UPGRADE {

    private BENEFICIOS_UPGRADE() {}

    public static final String NOMBRE_OBJETO_BENEFICIOS = "TcUpgradeBeneficiosMantenimientoUI.java";
    public static final String NOMBRE_OBJETO_DESCRIPCIONES =
        "TcUpgradeDescripcionesMantenimientoUI.java";
    public static final String NOMBRE_REPORTE_BENEFICIOS = "Mantenimiento de Upgrade (Beneficios)";
    public static final String NOMBRE_REPORTE_DESCRIPCIONES =
        "Mantenimiento de Upgrade (Descripciones)";

    public static final String CAMPO_ID = "id";
    public static final String CAMPO_BIN = "bin";
    public static final String CAMPO_CODIGO = "codigo";
    public static final String CAMPO_DESCRIPCION = "descripcion";
    public static final String CAMPO_DESCRIPCION_DETALLADA = "descripcionDetallada";
    public static final String CAMPO_ESTADO = "estado";
    public static final String CAMPO_CREADOR = "creador";
    public static final String CAMPO_FECHA_CREACION = "fechaCreacion";
    public static final String CAMPO_EDITOR = "editor";
    public static final String CAMPO_FECHA_MODIFICACION = "fechaModificacion";
    public static final String CAMPO_INDICADOR_BENEFICIARIO = "indicadorBeneficiario";
    public static final String OBJETO_BENEFICIO_UPGRADE = "beneficioUpgrade";

    public static final String COLUMNA_BIN = "BIN";
    public static final String COLUMNA_CODIGO = "Código";
    public static final String COLUMNA_DESCRIPCION = "Descripción";
    public static final String COLUMNA_DESCRIPCION_GENERAL = "Descripción general";
    public static final String COLUMNA_DESCRIPCION_DETALLADA = "Descripción detallada";
    public static final String COLUMNA_ESTADO = "Estado";
    public static final String COLUMNA_CREADOR = "Creador";
    public static final String COLUMNA_FECHA_CREACION = "Fecha creación";
    public static final String COLUMNA_EDITOR = "Editor";
    public static final String COLUMNA_FECHA_MODIFICACION = "Fecha modificación";

    public static final String NOMBRE_ARCHIVO_BENEFICIOS_EXPORTACION =
        "PPPAD_D02_UpgradeBeneficios";
    public static final String NOMBRE_ARCHIVO_DESCRIPCIONES_EXPORTACION =
        "PPPAD_D02_UpgradeDescripciones";

    public static final String MSJ_INFORMATIVO_CODIGO_BENEFICIARIO =
        "Recuerda que el código del beneficio para upgrade debe iniciar con el prefijo 'U'.";
    public static final String MSJ_VALICACION_BIN = "Seleccione bin.";
    public static final String MSJ_VALICACION_CODIGO_BENEFICIARIO = "Ingrese el código beneficio";
    public static final String MSJ_VALICACION_CODIGO_4_CARACTERES =
        "El código del beneficio debe ser de 4 caracteres";
    public static final String MSJ_VALICACION_CODIGO_PREFIJO_U =
        "El código del beneficio debe iniciar con el prefijo 'U'";
    public static final String MSJ_VALICACION_CODIGO_RELACION_BIN =
        "El código del beneficio ingresado ya está asociado al BIN seleccionado";
    public static final String MSJ_VALICACION_DESCRIPCION = "Ingrese la descripción.";
    public static final String MSJ_VALICACION_ESTADO = "Seleccione estado.";
    public static final String MSJ_VALICACION_DESCRIPCION_DETALLADA =
        "Ingrese la descripción detallada.";

    public static final String CODIGO_ACCION_UPGRADE_BENEFICIO = "MANTENIMIENTO_UPGRADE_BENEFICIO";
    public static final String CODIGO_ACCION_UPGRADE_DESCRIPCION =
        "MANTENIMIENTO_UPGRADE_DESCRIPCION";
    public static final String CODIGO_VALIDACION_U = "U";

    public static final String PATRON_FILTRO_A = "A";
    public static final String PATRON_FILTRO_AC = "AC";
    public static final String PATRON_FILTRO_ACT = "ACT";
    public static final String PATRON_FILTRO_ACTI = "ACTI";
    public static final String PATRON_FILTRO_ACTIV = "ACTIV";
    public static final String PATRON_FILTRO_ACTIVO = "ACTIVO";
    public static final String PATRON_FILTRO_I = "I";
    public static final String PATRON_FILTRO_IN = "IN";
  }

  public abstract class TARJETA_UPGRADE {

    private TARJETA_UPGRADE() {}

    public static final String NOMBRE_OBJETO_JAVA = "ConsultaTarjetaUpgradeUI.java";
    public static final String NOMBRE_REPORTE = "Consultar Tarjeta Upgrade";

    public static final String CAMPO_ID = "id";
    public static final String CAMPO_CODIGO_CENTRAL = "codigoCentral";
    public static final String CAMPO_TIPO_DOCUMENTO = "tipoDocumento";
    public static final String CAMPO_NUMERO_DOCUMENTO = "numeroDocumento";
    public static final String CAMPO_NOMBRE_COMPLETO = "nombreCompleto";
    public static final String CAMPO_TELEFONO = "telefono";
    public static final String CAMPO_CORREO = "correo";
    public static final String CAMPO_NOMBRE_TARJETA_ORIGEN = "nombreTarjetaOrigen";
    public static final String CAMPO_TIPO_TARJETA_ORIGEN = "tipoTarjetaOrigen";
    public static final String CAMPO_NOMBRE_NUEVA_TARJETA = "nombreNuevaTarjeta";
    public static final String CAMPO_TIPO_NUEVA_TARJETA = "tipoNuevaTarjeta";
    public static final String CAMPO_MONEDA = "moneda";
    public static final String CAMPO_LINEA_CREDITO = "lineaCredito";
    public static final String CAMPO_CANAL = "canal";
    public static final String CAMPO_ESTADO = "estado";
    public static final String CAMPO_CREADOR = "creador";
    public static final String CAMPO_FECHA_CREACION = "fechaCreacion";
    public static final String CAMPO_EDITOR = "editor";
    public static final String CAMPO_FECHA_MODIFICACION = "fechaModificacion";
    public static final String CAMPO_FECHA_REGISTRO_DESDE = "fechaRegistroDesde";
    public static final String CAMPO_FECHA_REGISTRO_HASTA = "fechaRegistroHasta";

    public static final String COLUMNA_CODIGO_CENTRAL = "CODIGO CENTRAL";
    public static final String COLUMNA_TIPO_DOCUMENTO = "TIPO DOCUMENTO";
    public static final String COLUMNA_NUMERO_DOCUMENTO = "NUMERO DOCUMENTO";
    public static final String COLUMNA_NOMBRE_COMPLETO = "NOMBRE COMPLETO";
    public static final String COLUMNA_TELEFONO = "TELEFONO";
    public static final String COLUMNA_CORREO = "CORREO";
    public static final String COLUMNA_NOMBRE_TARJETA_ORIGEN = "NOMBRE TARJETA ORIGEN";
    public static final String COLUMNA_TIPO_TARJETA_ORIGEN = "TIPO TARJETA ORIGEN";
    public static final String COLUMNA_NOMBRE_NUEVA_TARJETA = "NOMBRE NUEVA TARJETA";
    public static final String COLUMNA_TIPO_NUEVA_TARJETA = "TIPO NUEVA TARJETA";
    public static final String COLUMNA_MONEDA = "MONEDA";
    public static final String COLUMNA_LINEA_CREDITO = "LINEA DE CREDITO";
    public static final String COLUMNA_CANAL = "CANAL";
    public static final String COLUMNA_ESTADO = "ESTADO";
    public static final String COLUMNA_CREADOR = "CREADOR";
    public static final String COLUMNA_FECHA_CREACION = "FECHA CREACION";
    public static final String COLUMNA_EDITOR = "EDITOR";
    public static final String COLUMNA_FECHA_MODIFICACION = "FECHA MODIFICACION";

    public static final String SECCION_ARCHIVO_EXPORTACION = "tarjeta upgrade";
    public static final String NOMBRE_ARCHIVO_EXPORTACION = "TARJETA_UPGRADE_";
  }

  public abstract class SEGURO_VIDA_ROYAL {

    private SEGURO_VIDA_ROYAL() {}

    public static final String NOMBRE_OBJETO_JAVA = "ConsultaSeguroVidaUI.java";
    public static final String SECCION_ARCHIVO_EXPORTACION = "seguro vida royal";
    public static final String NOMBRE_ARCHIVO_EXPORTACION = "SEGURO_VIDA_ROYAL_";

    public static final String CAMPO_ID = "id";
    public static final String CAMPO_FECHA_REGISTRO = "fechaRegistro";
    public static final String CAMPO_NOMBRE = "nombre";
    public static final String CAMPO_APELLIDO = "apellido";
    public static final String CAMPO_TIPO_DOCUMENTO = "tipoDocumento";
    public static final String CAMPO_DOCUMENTO = "documento";
    public static final String CAMPO_CORREO = "correo";
    public static final String CAMPO_TELEFONO = "telefono";
    public static final String CAMPO_CODIGO_OFICINA = "codigoOficina";
    public static final String CAMPO_CODIGO_USUARIO = "codigoUsuario";
    public static final String CAMPO_HORARIO_CONTACTO = "horarioContacto";
    public static final String CAMPO_CANAL = "canal";
    public static final String CAMPO_FECHA_REGISTRO_DESDE = "fechaRegistroDesde";
    public static final String CAMPO_FECHA_REGISTRO_HASTA = "fechaRegistroHasta";
    public static final String CAMPO_INDICADOR_SI_ES_CLIENTE = "indicadorSiEsCliente";
    public static final String CAMPO_INDICADOR_PROCESADO = "indicadorProcesado";
    public static final String CAMPO_NUMERO_CONTRATO = "nroContrato";

    public static final String COLUMNA_FECHA_REGISTRO = "FECHA REGISTRO";
    public static final String COLUMNA_NOMBRE = "NOMBRE";
    public static final String COLUMNA_APELLIDO = "APELLIDO";
    public static final String COLUMNA_TIPO_DOCUMENTO = "TIPO DOCUMENTO";
    public static final String COLUMNA_DOCUMENTO = "NÚMERO DOCUMENTO";
    public static final String COLUMNA_CORREO = "CORREO";
    public static final String COLUMNA_TELEFONO = "TELÉFONO";
    public static final String COLUMNA_CODIGO_OFICINA = "CÓDIGO OFICINA";
    public static final String COLUMNA_CODIGO_USUARIO = "CÓDIGO USUARIO";
    public static final String COLUMNA_HORARIO_CONTACTO = "HORARIO CONTACTO";
    public static final String COLUMNA_CANAL = "CANAL";
    public static final String COLUMNA_INDICADOR_SI_ES_CLIENTE = "ES CLIENTE";
    public static final String COLUMNA_INDICADOR_PROCESADO = "PROCESADO";
    public static final String COLUMNA_NUMERO_CONTRATO = "NÚMERO CONTRATO";
  }

  public abstract class PRODUCTO_SEGURO_ROYAL {

    private PRODUCTO_SEGURO_ROYAL() {}

    public static final String CAMPO_ID = "id";
    public static final String CAMPO_COD_PRODUCTO = "codigoProducto";
    public static final String CAMPO_NOMBRE_PRODUCTO = "nombreProducto";
    public static final String CAMPO_IND_ROYAL = "indRoyal";

    public static final String CAMPO_ESTADO = "estado";
    public static final String SELECT_TIPO = "Tipo Producto";
  }

  public abstract class SEGURO_ROYAL {

    private SEGURO_ROYAL() {}

    public static final String TITULO = "Cotización Seguro Royal";
    public static final String NOMBRE_OBJETO_JAVA = "ConsultaSeguroDesempleoUI.java";
    public static final String SECCION_ARCHIVO_EXPORTACION = "seguro royal";
    public static final String NOMBRE_ARCHIVO_EXPORTACION = "SEGURO_ROYAl_";

    public static final String CAMPO_FECHA_REGISTRO_DESDE = "fechaRegistroDesde";
    public static final String CAMPO_FECHA_REGISTRO_HASTA = "fechaRegistroHasta";

    public static final String CAMPO_ID = "id";
    public static final String CAMPO_FECHA_REGISTRO = "fechaRegistro";
    public static final String CAMPO_NOMBRE_PRODUCTO = "nombreProducto";
    public static final String CAMPO_NOMBRE = "nombre";
    public static final String CAMPO_APELLIDO = "apellido";
    public static final String CAMPO_TIPO_DOCUMENTO = "tipoDocumento";
    public static final String CAMPO_NRO_DOCUMENTO = "nroDocumento";
    public static final String CAMPO_CORREO = "correo";
    public static final String CAMPO_TELEFONO = "telefono";
    public static final String CAMPO_ES_CLIENTE = "esCliente";
    public static final String CAMPO_MONEDA = "moneda";
    public static final String CAMPO_VALOR_COMERCIAL = "valorComercial";
    public static final String CAMPO_MONEDA_CUOTAS = "monedaCuotas";
    public static final String CAMPO_MONTO_CUOTAS = "montoCuotas";
    public static final String CAMPO_MONEDA_PRIMA = "monedaPrima";
    public static final String CAMPO_MONTO_PRIMA = "montoPrima";
    public static final String CAMPO_NRO_COTIZACION = "nroContizacion";
    public static final String CAMPO_INFORMACION_BANCO = "informacionBanco";
    public static final String CAMPO_CODIGO_OFICINA = "codigoOficina";
    public static final String CAMPO_CODIGO_USUARIO = "codigoUsuario";
    public static final String CAMPO_CANAL = "canal";
    public static final String CAMPO_ID_PRODUCTO = "idProducto";

    public static final String COLUMNA_FECHA_REGISTRO = "FECHA REGISTRO";
    public static final String COLUMNA_PRODUCTO = "PRODUCTO";
    public static final String COLUMNA_NOMBRE = "NOMBRE";
    public static final String COLUMNA_APELLIDO = "APELLIDO";
    public static final String COLUMNA_TIPO_DOCUMENTO = "TIPO DOCUMENTO";
    public static final String COLUMNA_NRO_DOCUMENTO = "NÚMERO DOCUMENTO";
    public static final String COLUMNA_CORREO = "CORREO";
    public static final String COLUMNA_TELEFONO = "TELÉFONO";
    public static final String COLUMNA_ES_CLIENTE = "ES CLIENTE";
    public static final String COLUMNA_MONEDA = "MONEDA";
    public static final String COLUMNA_VALOR_COMERCIAL = "VALOR COMERCIAL";
    public static final String COLUMNA_MONEDA_CUOTAS = "MONEDA DE CUOTAS";
    public static final String COLUMNA_MONTO_CUOTAS = "MONTO DE CUOTAS";
    public static final String COLUMNA_MONEDA_PRIMA = "MONEDA DE PRIMA";
    public static final String COLUMNA_MONTO_PRIMA = "MONTO DE PRIMA";
    public static final String COLUMNA_NRO_COTIZACION = "NRO COTIZACION";
    public static final String COLUMNA_INFORMACION_BANCO = "INFORMACION DE BANCO";
    public static final String COLUMNA_CODIGO_OFICINA = "CODIGO DE OFICINA";
    public static final String COLUMNA_CODIGO_USUARIO = "CODIGO_USUARIO";
    public static final String COLUMNA_CANAL = "CANAL";
  }

  public abstract class MERCURIO {

    private MERCURIO() {}

    public static final String NOMBRE_OBJETO_JAVA = "ConsultaLeadMercurioUI.java";
    public static final String NOMBRE_REPORTE = "Reporte Lead Adquirencia AOP";

    public static final String CAMPO_ID = "id";
    public static final String CAMPO_CODIGO_CENTRAL = "codigoCentral";
    public static final String CAMPO_TIPO_DOCUMENTO = "tipoDocumento";
    public static final String CAMPO_NUMERO_DOCUMENTO = "numeroDocumento";
    public static final String CAMPO_NOMBRE_COMPLETO = "nombreCompleto";
    public static final String CAMPO_TELEFONO = "telefono";
    public static final String CAMPO_CORREO = "correo";
    public static final String CAMPO_ESTADO = "estado";
    public static final String CAMPO_FECHA_REGISTRO_DESDE = "fechaRegistroDesde";
    public static final String CAMPO_CANAL = "canal";
    public static final String CAMPO_PASO_ABANDONO = "pasoAbandono";
    public static final String CAMPO_PRODUCTO = "producto";
    public static final String CAMPO_FECHA_REGISTRO_HASTA = "fechaRegistroHasta";
    public static final String CAMPO_FECHA_REGISTRO = "fechaRegistro";

    public static final String COLUMNA_CODIGO_CENTRAL = "CODIGO CENTRAL";
    public static final String COLUMNA_TIPO_DOCUMENTO = "TIPO DOCUMENTO";
    public static final String COLUMNA_NUMERO_DOCUMENTO = "NUMERO DOCUMENTO";
    public static final String COLUMNA_NOMBRE_COMPLETO = "NOMBRE COMPLETO";
    public static final String COLUMNA_TELEFONO = "TELEFONO";
    public static final String COLUMNA_CORREO = "CORREO";
    public static final String COLUMNA_ESTADO = "ESTADO";
    public static final String COLUMNA_FECHA_REGISTRO = "FECHA REGISTRO";
    public static final String COLUMNA_CANAL = "CANAL";
    public static final String COLUMNA_PASO_ABANDONO = "PASO ABANDONO";
    public static final String COLUMNA_PRODUCTO = "PRODUCTO";

    public static final String SECCION_ARCHIVO_EXPORTACION = "mercurio";
    public static final String NOMBRE_ARCHIVO_EXPORTACION = "LEAD_ADQUIRENCIA_AOP_";

    public static final String ESTADO_PROCESADO = "Procesado";
    public static final String ESTADO_PENDIENTE = "Pendiente";
  }

  public abstract class LEAD_SEGURO_VEHICULAR_OPENPAY {

    private LEAD_SEGURO_VEHICULAR_OPENPAY() {}

    public static final String NOMBRE_OBJETO_JAVA = "ConsultarLeadSeguroVehicularOpenPayUI.java";
    public static final String SECCION_ARCHIVO_EXPORTACION =
        "Leads del seguro vehicular royal zona publica openpay";
    public static final String NOMBRE_ARCHIVO_EXPORTACION = "LEADS_SEGURO_VEHICULAR_ZP_OPENPAY";

    public static final String CAMPO_ID = "id";
    public static final String CAMPO_DETALLE = "solicitudDetalle";
    public static final String CAMPO_NUMERO_COTIZACION = "cotizacionCodigo";
    public static final String CAMPO_STEP_PROCESS = "cotizacionStep";
    public static final String CAMPO_CAMPO_FECHA_REGISTRO_SOLICITUD = "solicitudFechaRegistro";
    public static final String CAMPO_SOLICITUD_HORARIO_CONTACTO = "horarioContacto";
    public static final String CAMPO_NUMERO_DOCUMENTO = "documentoNumero";
    public static final String CAMPO_TIPO_DOCUMENTO = "documentoTipo";
    public static final String CAMPO_NOMBRE = "nombre";
    public static final String CAMPO_APELLIDO = "apellido";
    public static final String CAMPO_CORREO = "correo";
    public static final String CAMPO_TELEFONO = "telefono";
    public static final String CAMPO_PLACA_VEHICULO = "vehiculoPlaca";
    public static final String CAMPO_MARCA_VEHICULO = "vehiculoMarca";
    public static final String CAMPO_MODELO_VEHICULO = "vehiculoModelo";
    public static final String CAMPO_FECHA_FABRICACION_VEHICULO = "vehiculoFechafabricacion";
    public static final String CAMPO_VALOR_MONETARIO_VEHICULO = "vehiculoValorMonetario";
    public static final String CAMPO_MONEDA_VALOR_MONETARIO_VEHICULO =
        "vehiculoMonedaValorMonetario";
    public static final String CAMPO_CONVERSION_MOTOR_VEHICULO = "vehiculoConversionMotor";
    public static final String CAMPO_TIPO_CIRCULACION_VEHICULO = "vehiculoTipoCirculacion";
    public static final String CAMPO_MONTO_PRIMA = "primaMonto";
    public static final String CAMPO_MONEDA_MONTO_PRIMA = "primaMontoMoneda";
    public static final String CAMPO_MONTO_TOTAL_PRIMA = "primaTotalMonto";
    public static final String CAMPO_MONEDA_MONTO_TOTAL_PRIMA = "primaTotalMontoMoneda";
    public static final String CAMPO_FRECUENCIA_PAGO_PRIMA = "primaPeriodo";
    public static final String CAMPO_PLAN_NAME = "planName";
    public static final String CAMPO_INFORMACION_BANCO = "informacionBanco";
    public static final String CAMPO_CODIGO_OFICINA = "codigoOficina";
    public static final String CAMPO_CODIGO_USUARIO = "codigoUsuario";
    public static final String CAMPO_CANAL = "canal";
    public static final String CAMPO_FECHA_REGISTRO_DESDE = "fechaRegistroDesde";
    public static final String CAMPO_FECHA_REGISTRO_HASTA = "fechaRegistroHasta";

    public static final String COLUMNA_DETALLE = "DETALLE";
    public static final String COLUMNA_NUMERO_COTIZACION = "CÓDIGO DE COTIZACIÓN";
    public static final String COLUMNA_STEP_PROCESS = "PASO DE COTIZACIÓN";
    public static final String COLUMNA_FECHA_REGISTRO_SOLICITUD = "FECHA REGISTRO DE SOLICITUD";
    public static final String COLUMNA_SOLICITUD_HORARIO_CONTACTO = "HORARIO DE CONTACTO";
    public static final String COLUMNA_NUMERO_DOCUMENTO = "NÚMERO DOCUMENTO";
    public static final String COLUMNA_TIPO_DOCUMENTO = "TIPO DOCUMENTO";
    public static final String COLUMNA_NOMBRE = "NOMBRE";
    public static final String COLUMNA_APELLIDO = "APELLIDO";
    public static final String COLUMNA_CORREO = "CORREO";
    public static final String COLUMNA_TELEFONO = "TELÉFONO";
    public static final String COLUMNA_PLACA_VEHICULO = "PLACA";
    public static final String COLUMNA_MARCA_VEHICULO = "MARCA";
    public static final String COLUMNA_MODELO_VEHICULO = "MODELO";
    public static final String COLUMNA_FECHA_FABRICACION_VEHICULO = "FECHA DE FABRICACIÓN";
    public static final String COLUMNA_VALOR_MONETARIO_VEHICULO = "VALOR VEHICULAR";
    public static final String COLUMNA_MONEDA_VALOR_MONETARIO_VEHICULO = "MONEDA VALOR VEHICULAR";
    public static final String COLUMNA_CONVERSION_MOTOR_VEHICULO = "SE REALIZO CONVERSION DE MOTOR";
    public static final String COLUMNA_TIPO_CIRCULACION_VEHICULO = "CIRCULACION VEHICULAR";
    public static final String COLUMNA_MONTO_PRIMA = "VALOR DE PRIMA";
    public static final String COLUMNA_MONEDA_MONTO_PRIMA = "MONEDA PRIMA";
    public static final String COLUMNA_MONTO_TOTAL_PRIMA = "VALOR TOTAL DE PRIMA";
    public static final String COLUMNA_MONEDA_MONTO_TOTAL_PRIMA = "MONEDA TOTAL PRIMA";
    public static final String COLUMNA_FRECUENCIA_PAGO_PRIMA = "FRECUENCIA DE PAGO";
    public static final String COLUMNA_PLAN_NAME = "PLAN ASEGURADO";
    public static final String COLUMNA_INFORMACION_BANCO = "BANCO";
    public static final String COLUMNA_CODIGO_OFICINA = "CÓDIGO OFICINA";
    public static final String COLUMNA_CODIGO_USUARIO = "CÓDIGO USUARIO";
    public static final String COLUMNA_CANAL = "CANAL";
  }

  public abstract class PERFIL {
    public static final String ADMINISTRADOR = "ADMINISTRADOR";
    public static final String ADMI_YMP2_REL_SYS = "ADMI_YMP2_REL_SYS";
    public static final String BACK_OFFICE_TC = "BACK_OFFICE_TC";
    public static final String ADMIN_TARJETAS = "ADMIN_TARJETAS";
  }
}
