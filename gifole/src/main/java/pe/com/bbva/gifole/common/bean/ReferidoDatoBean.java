package pe.com.bbva.gifole.common.bean;

import java.util.Date;

public class ReferidoDatoBean {

  // Referidor
  private Long id;
  private String nombre;
  private String apepat;
  private String apemat;
  private String codigoCentral;
  private String numeroDocumento;
  private String marcaEmpleado;
  private String cantidadPremio;

  // Referido
  private Long idReferido;
  private String numeroDocumentoRef;
  private String codigoCentralRef;
  private String celularRef;
  private String nombreRef;
  private String apepatRef;
  private String apematRef;
  private String tipoTarjeta;
  private String nombreTarjeta;
  private String linea;
  private String tasa;
  private String correo;
  private Date fechaLead;
  private String nombreCompletoRef;
  private String estado;
  private int cantidadPremioRef;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public String getApepat() {
    return apepat;
  }

  public void setApepat(String apepat) {
    this.apepat = apepat;
  }

  public String getApemat() {
    return apemat;
  }

  public void setApemat(String apemat) {
    this.apemat = apemat;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getNumeroDocumento() {
    return numeroDocumento;
  }

  public void setNumeroDocumento(String numeroDocumento) {
    this.numeroDocumento = numeroDocumento;
  }

  public String getMarcaEmpleado() {
    return marcaEmpleado;
  }

  public void setMarcaEmpleado(String marcaEmpleado) {
    this.marcaEmpleado = marcaEmpleado;
  }

  public Long getIdReferido() {
    return idReferido;
  }

  public void setIdReferido(Long idReferido) {
    this.idReferido = idReferido;
  }

  public String getNumeroDocumentoRef() {
    return numeroDocumentoRef;
  }

  public void setNumeroDocumentoRef(String numeroDocumentoRef) {
    this.numeroDocumentoRef = numeroDocumentoRef;
  }

  public String getCodigoCentralRef() {
    return codigoCentralRef;
  }

  public void setCodigoCentralRef(String codigoCentralRef) {
    this.codigoCentralRef = codigoCentralRef;
  }

  public String getCelularRef() {
    return celularRef;
  }

  public void setCelularRef(String celularRef) {
    this.celularRef = celularRef;
  }

  public String getNombreRef() {
    return nombreRef;
  }

  public void setNombreRef(String nombreRef) {
    this.nombreRef = nombreRef;
  }

  public String getApepatRef() {
    return apepatRef;
  }

  public void setApepatRef(String apepatRef) {
    this.apepatRef = apepatRef;
  }

  public String getApematRef() {
    return apematRef;
  }

  public void setApematRef(String apematRef) {
    this.apematRef = apematRef;
  }

  public String getTipoTarjeta() {
    return tipoTarjeta;
  }

  public void setTipoTarjeta(String tipoTarjeta) {
    this.tipoTarjeta = tipoTarjeta;
  }

  public String getNombreTarjeta() {
    return nombreTarjeta;
  }

  public void setNombreTarjeta(String nombreTarjeta) {
    this.nombreTarjeta = nombreTarjeta;
  }

  public String getLinea() {
    return linea;
  }

  public void setLinea(String linea) {
    this.linea = linea;
  }

  public String getTasa() {
    return tasa;
  }

  public void setTasa(String tasa) {
    this.tasa = tasa;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public Date getFechaLead() {
    return fechaLead;
  }

  public void setFechaLead(Date fechaLead) {
    this.fechaLead = fechaLead;
  }

  public String getNombreCompletoRef() {
    return nombreCompletoRef;
  }

  public void setNombreCompletoRef(String nombreCompletoRef) {
    this.nombreCompletoRef = nombreCompletoRef;
  }

  public String getCantidadPremio() {
    return cantidadPremio;
  }

  public void setCantidadPremio(String cantidadPremio) {
    this.cantidadPremio = cantidadPremio;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public int getCantidadPremioRef() {
    return cantidadPremioRef;
  }

  public void setCantidadPremioRef(int cantidadPremioRef) {
    this.cantidadPremioRef = cantidadPremioRef;
  }
}
