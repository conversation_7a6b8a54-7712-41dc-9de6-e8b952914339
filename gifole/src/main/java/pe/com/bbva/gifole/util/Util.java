package pe.com.bbva.gifole.util;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Clase utilitaria con métodos de uso general
 *
 * <AUTHOR>
 * @version 1.0
 */
public class Util {

  /** Formato de fecha DD-MM-YYYY */
  private static final String FORMATO_FECHA_DD_MM_YYYY = "dd-MM-yyyy";

  /**
   * Convierte una fecha a formato String DD-MM-YYYY
   *
   * @param fecha La fecha a convertir
   * @return String con formato DD-MM-YYYY, o cadena vacía si la fecha es null
   */
  public static String convertirFechaAString(Date fecha) {
    if (fecha == null) {
      return "";
    }

    try {
      SimpleDateFormat formatter = new SimpleDateFormat(FORMATO_FECHA_DD_MM_YYYY);
      return formatter.format(fecha);
    } catch (Exception e) {
      return "";
    }
  }

  /**
   * Convierte una fecha a formato String con patrón personalizado
   *
   * @param fecha La fecha a convertir
   * @param patron El patrón de formato (ej: "dd/MM/yyyy", "yyyy-MM-dd", etc.)
   * @return String con el formato especificado, o cadena vacía si hay error
   */
  public static String convertirFechaAString(Date fecha, String patron) {
    if (fecha == null || patron == null || patron.trim().isEmpty()) {
      return "";
    }

    try {
      SimpleDateFormat formatter = new SimpleDateFormat(patron);
      return formatter.format(fecha);
    } catch (Exception e) {
      return "";
    }
  }

  /**
   * Convierte una fecha a formato String DD-MM-YYYY HH:mm:ss
   *
   * @param fecha La fecha a convertir
   * @return String con formato DD-MM-YYYY HH:mm:ss, o cadena vacía si la fecha es null
   */
  public static String convertirFechaHoraAString(Date fecha) {
    if (fecha == null) {
      return "";
    }

    try {
      SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
      return formatter.format(fecha);
    } catch (Exception e) {
      return "";
    }
  }
}
