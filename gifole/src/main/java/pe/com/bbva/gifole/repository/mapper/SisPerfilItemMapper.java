package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.SisItem;
import pe.com.bbva.gifole.domain.SisPerfil;
import pe.com.bbva.gifole.domain.SisPerfilItem;

@Component
public class SisPerfilItemMapper implements RowMapper<SisPerfilItem> {

  @Override
  public SisPerfilItem mapRow(ResultSet rs, int i) throws SQLException {

    SisPerfilItem sisPerfilItem = new SisPerfilItem();
    sisPerfilItem.setId(rs.getLong("ID"));

    SisPerfil sisPerfil = new SisPerfil();
    sisPerfil.setId(rs.getLong("PERFIL"));
    sisPerfilItem.setSisPerfil(sisPerfil);

    SisItem sisItem = new SisItem();
    sisItem.setId(rs.getLong("ITEM"));
    sisPerfilItem.setSisItem(sisItem);

    sisPerfilItem.setEstado(rs.getString("ESTADO"));

    return sisPerfilItem;
  }
}
