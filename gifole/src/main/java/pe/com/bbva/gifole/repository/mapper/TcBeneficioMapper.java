package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.BeneficioTcCu;

@Component
public class TcBeneficioMapper implements RowMapper<BeneficioTcCu> {

  @Override
  public BeneficioTcCu mapRow(ResultSet rs, int i) throws SQLException {

    BeneficioTcCu beneficioTcCu = new BeneficioTcCu();

    beneficioTcCu.setBin(rs.getString("BIN"));
    beneficioTcCu.setEstado(rs.getString("ESTADO"));
    beneficioTcCu.setId(rs.getString("ID"));
    beneficioTcCu.setDescripcion(rs.getString("DESCRIPCION"));

    return beneficioTcCu;
  }
}
