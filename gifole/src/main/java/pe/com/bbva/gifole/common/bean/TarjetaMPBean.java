package pe.com.bbva.gifole.common.bean;

import java.util.Date;

public class TarjetaMPBean {

  private Long id;
  private String tipoDocumento;
  private String numeroDocumento;
  private String codigoCentral;
  private String nroContrato;
  private String nroTarjeta;
  private String fechaActivacion;
  private String fechaContratacion;
  private String estado;
  private String canal;
  private String oficinaContrato;
  private Date fechaActivaFormateada;
  private Date fechaContrataFormateada;
  private String origen;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public String getNumeroDocumento() {
    return numeroDocumento;
  }

  public void setNumeroDocumento(String numeroDocumento) {
    this.numeroDocumento = numeroDocumento;
  }

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getNroContrato() {
    return nroContrato;
  }

  public void setNroContrato(String nroContrato) {
    this.nroContrato = nroContrato;
  }

  public String getNroTarjeta() {
    return nroTarjeta;
  }

  public void setNroTarjeta(String nroTarjeta) {
    this.nroTarjeta = nroTarjeta;
  }

  public String getFechaActivacion() {
    return fechaActivacion;
  }

  public void setFechaActivacion(String fechaActivacion) {
    this.fechaActivacion = fechaActivacion;
  }

  public String getFechaContratacion() {
    return fechaContratacion;
  }

  public void setFechaContratacion(String fechaContratacion) {
    this.fechaContratacion = fechaContratacion;
  }

  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public String getOficinaContrato() {
    return oficinaContrato;
  }

  public void setOficinaContrato(String oficinaContrato) {
    this.oficinaContrato = oficinaContrato;
  }

  public Date getFechaActivaFormateada() {
    return fechaActivaFormateada;
  }

  public void setFechaActivaFormateada(Date fechaActivaFormateada) {
    this.fechaActivaFormateada = fechaActivaFormateada;
  }

  public Date getFechaContrataFormateada() {
    return fechaContrataFormateada;
  }

  public void setFechaContrataFormateada(Date fechaContrataFormateada) {
    this.fechaContrataFormateada = fechaContrataFormateada;
  }

  public String getOrigen() {
    return origen;
  }

  public void setOrigen(String origen) {
    this.origen = origen;
  }
}
