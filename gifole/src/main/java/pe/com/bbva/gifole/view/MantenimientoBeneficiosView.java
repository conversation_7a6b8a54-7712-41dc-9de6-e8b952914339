package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.domain.TcBeneficios;
import pe.com.bbva.gifole.util.Utilitario;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Mantenimiento de Beneficios")
@Route(value = "mantenimiento-beneficios", layout = MainLayout.class)
public class MantenimientoBeneficiosView extends VerticalLayout {

  public MantenimientoBeneficiosView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Mantenimiento de Beneficios");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    DataTable<TcBeneficios> dataTable =
        DataTable.<TcBeneficios>builder()
            .id("tabla-beneficios")
            .column(
                "estado",
                "Estado",
                beneficio -> "A".equals(beneficio.getEstado()) ? "Activo" : "Inactivo",
                "100px")
            .column(
                "titulo",
                "Saludo",
                beneficio -> StringUtils.trimToEmpty(beneficio.getSaludo()),
                "210px")
            .column(
                "cuerpo",
                "Texto",
                beneficio -> StringUtils.trimToEmpty(beneficio.getTexto()),
                "260px")
            .column(
                "url", "Texto 1", beneficio -> StringUtils.trimToEmpty(beneficio.getUrl()), "150px")
            .column(
                "urlmostrar",
                "Texto Acción",
                beneficio -> StringUtils.trimToEmpty(beneficio.getUrlMostrar()),
                "150px")
            .column(
                "urltracking",
                "URL Tracking",
                beneficio -> StringUtils.trimToEmpty(beneficio.getUrlTracking()),
                "150px")
            .column(
                "fecharegistro",
                "Fecha Registro",
                beneficio ->
                    Utilitario.formatearFecha(beneficio.getFechaRegistro(), "dd/MM/yyyy HH:mm"),
                "150px")
            .column(
                "fechaactivacion",
                "Fecha Activación",
                beneficio ->
                    Utilitario.formatearFecha(beneficio.getFechaActivacion(), "dd/MM/yyyy HH:mm"),
                "150px")
            // Datos
            .items(createSampleData()) // 20 registros de ejemplo
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }

  /** Crea datos de ejemplo para la tabla */
  private java.util.List<TcBeneficios> createSampleData() {
    java.util.List<TcBeneficios> beneficios = new java.util.ArrayList<>();

    // Crear 20 registros de ejemplo
    beneficios.add(
        createBeneficio(
            "A",
            "¡Bienvenido!",
            "Descubre todos los beneficios exclusivos de BBVA",
            "https://bbva.pe/beneficios",
            "Ver Beneficios",
            "https://track.bbva.pe/1"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Hola Cliente!",
            "Aprovecha descuentos especiales en restaurantes",
            "https://bbva.pe/restaurantes",
            "Ver Descuentos",
            "https://track.bbva.pe/2"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Oferta Especial!",
            "Cashback del 5% en compras online",
            "https://bbva.pe/cashback",
            "Activar Cashback",
            "https://track.bbva.pe/3"));
    beneficios.add(
        createBeneficio(
            "I",
            "Promoción Vencida",
            "Descuentos en cines ya no disponible",
            "https://bbva.pe/cines",
            "Ver Detalles",
            "https://track.bbva.pe/4"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Viaja Más!",
            "Millas dobles en vuelos internacionales",
            "https://bbva.pe/viajes",
            "Acumular Millas",
            "https://track.bbva.pe/5"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Salud Primero!",
            "Descuentos en farmacias y laboratorios",
            "https://bbva.pe/salud",
            "Ver Farmacias",
            "https://track.bbva.pe/6"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Compra Inteligente!",
            "Cuotas sin interés en electrodomésticos",
            "https://bbva.pe/electrodomesticos",
            "Ver Tiendas",
            "https://track.bbva.pe/7"));
    beneficios.add(
        createBeneficio(
            "I",
            "Mantenimiento",
            "Beneficio en actualización",
            "https://bbva.pe/mantenimiento",
            "Próximamente",
            "https://track.bbva.pe/8"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Educación!",
            "Descuentos en cursos y certificaciones",
            "https://bbva.pe/educacion",
            "Ver Cursos",
            "https://track.bbva.pe/9"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Deportes!",
            "Membresías preferenciales en gimnasios",
            "https://bbva.pe/deportes",
            "Ver Gimnasios",
            "https://track.bbva.pe/10"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Tecnología!",
            "Descuentos en gadgets y smartphones",
            "https://bbva.pe/tecnologia",
            "Ver Ofertas",
            "https://track.bbva.pe/11"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Hogar Ideal!",
            "Financiamiento especial para el hogar",
            "https://bbva.pe/hogar",
            "Solicitar",
            "https://track.bbva.pe/12"));
    beneficios.add(
        createBeneficio(
            "I",
            "Temporada Cerrada",
            "Beneficios de temporada navideña",
            "https://bbva.pe/navidad",
            "Ver Historial",
            "https://track.bbva.pe/13"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Entretenimiento!",
            "Descuentos en streaming y entretenimiento",
            "https://bbva.pe/streaming",
            "Activar",
            "https://track.bbva.pe/14"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Combustible!",
            "Descuentos en estaciones de servicio",
            "https://bbva.pe/combustible",
            "Ver Estaciones",
            "https://track.bbva.pe/15"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Mascotas!",
            "Beneficios para el cuidado de mascotas",
            "https://bbva.pe/mascotas",
            "Ver Veterinarias",
            "https://track.bbva.pe/16"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Seguros!",
            "Seguros con tarifas preferenciales",
            "https://bbva.pe/seguros",
            "Cotizar",
            "https://track.bbva.pe/17"));
    beneficios.add(
        createBeneficio(
            "I",
            "Revisión",
            "Beneficio en proceso de revisión",
            "https://bbva.pe/revision",
            "En Proceso",
            "https://track.bbva.pe/18"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Inversiones!",
            "Comisiones preferenciales en inversiones",
            "https://bbva.pe/inversiones",
            "Invertir",
            "https://track.bbva.pe/19"));
    beneficios.add(
        createBeneficio(
            "A",
            "¡Exclusivo VIP!",
            "Beneficios exclusivos para clientes VIP",
            "https://bbva.pe/vip",
            "Ser VIP",
            "https://track.bbva.pe/20"));

    return beneficios;
  }

  /** Crea un beneficio de ejemplo */
  private TcBeneficios createBeneficio(
      String estado,
      String saludo,
      String texto,
      String url,
      String urlMostrar,
      String urlTracking) {
    TcBeneficios beneficio = new TcBeneficios();
    beneficio.setEstado(estado);
    beneficio.setSaludo(saludo);
    beneficio.setTexto(texto);
    beneficio.setUrl(url);
    beneficio.setUrlMostrar(urlMostrar);
    beneficio.setUrlTracking(urlTracking);
    beneficio.setFechaRegistro(new java.util.Date());
    beneficio.setFechaActivacion(new java.util.Date());
    return beneficio;
  }
}
