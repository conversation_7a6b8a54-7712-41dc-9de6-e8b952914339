package pe.com.bbva.gifole.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

/** Entidad que representa un Usuario en el sistema GIFOLE */
@Data
@Entity
@Table(name = "USUARIO")
@Schema(description = "Entidad Usuario del sistema GIFOLE")
public class Usuario {

  @Id
  @Column(unique = true, nullable = false, precision = 16)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequence")
  @SequenceGenerator(name = "sequence", sequenceName = "SQ_USUARIO", allocationSize = 1)
  private Long id;

  @Column(name = "REGISTRO", length = 7)
  @Schema(description = "Registro unico del usuario", example = "USR0001", maxLength = 7)
  private String registro;

  @Column(name = "NOMBRE", length = 50)
  @Schema(description = "Nombre del usuario", example = "Juan", maxLength = 50)
  private String nombre;

  @Column(name = "PATERNO", length = 50)
  @Schema(description = "Apellido paterno del usuario", example = "Perez", maxLength = 50)
  private String paterno;

  @Column(name = "MATERNO", length = 50)
  @Schema(description = "Apellido materno del usuario", example = "Garcia", maxLength = 50)
  private String materno;

  @OneToOne
  @JoinColumn(name = "PERFIL")
  private SisPerfil sisPerfil;
}
