package pe.com.bbva.gifole.common.enums;

public enum FeriadosEnum {
  ANIO_NUEVO(1, 1),
  DIA_TRABAJO(1, 5),
  SAN_PEDRO(29, 6),
  FIESTAS_PATRIAS_28(28, 7),
  FIESTAS_PATRIAS_29(29, 7),
  SANTA_ROSA(30, 8),
  COMBATE_ANGAMOS(8, 10),
  TODOS_LOS_SANTOS(1, 11),
  INMACULADA_CONCEPCION(8, 12),
  NAVIDAD(25, 12);

  private int dia;
  private int mes;

  private FeriadosEnum(int dia, int mes) {
    this.dia = dia;
    this.mes = mes;
  }

  public int getDia() {
    return dia;
  }

  public int getMes() {
    return mes;
  }
}
