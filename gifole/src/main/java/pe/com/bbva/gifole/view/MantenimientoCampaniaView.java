package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.datepicker.DatePicker;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.NumberField;
import com.vaadin.flow.component.textfield.TextArea;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import pe.com.bbva.gifole.common.bean.CampaniaMultiplicaBean;

// @CssImport("./themes/bbva/views/mantenimiento-campania-view.css")
@PageTitle("Mantenimiento de Campañas")
@Route(value = "mantenimiento-campania", layout = MainLayout.class)
public class MantenimientoCampaniaView extends VerticalLayout {

  // Constantes
  private static final String ACTIVO = "ACTIVO";
  private static final String INACTIVO = "INACTIVO";

  // Componentes de la interfaz
  private final Grid<CampaniaMultiplicaBean> grid = new Grid<>();
  private final List<CampaniaMultiplicaBean> campaniasList = new ArrayList<>();
  private final List<CampaniaMultiplicaBean> allCampanias = new ArrayList<>();

  // Componentes del drawer
  private VerticalLayout drawerPanel;
  private boolean drawerVisible = false;

  // Filtros
  private ComboBox<String> filtroEstado = new ComboBox<>("Estado");
  private TextField filtroNombre = new TextField("Nombre");
  private TextField filtroDescripcion = new TextField("Descripción");
  private ComboBox<String> filtroCanal = new ComboBox<>("Canal");
  private DatePicker filtroFechaInicio = new DatePicker("Fecha Inicio");
  private DatePicker filtroFechaFin = new DatePicker("Fecha Fin");

  // Formulario
  private TextField txtNombre = new TextField("Nombre");
  private TextArea txtDescripcion = new TextArea("Descripción");
  private TextField txtDivisa = new TextField("Divisa");
  private NumberField txtValor1 = new NumberField("Valor 1");
  private NumberField txtValor2 = new NumberField("Valor 2");
  private TextField txtRango1 = new TextField("Rango 1");
  private TextField txtRango2 = new TextField("Rango 2");
  private DatePicker dtpFechaInicio = new DatePicker("Fecha Inicio");
  private DatePicker dtpFechaFin = new DatePicker("Fecha Fin");
  private ComboBox<String> cmbEstado = new ComboBox<>("Estado");
  private ComboBox<String> cmbCanal = new ComboBox<>("Canal");
  private Button btnGuardar = new Button("Guardar");
  private Button btnCancelar = new Button("Cancelar");
  private Button btnNuevo = new Button("Nueva Campaña", VaadinIcon.PLUS.create());
  private Button btnCerrarDrawer = new Button(VaadinIcon.CLOSE.create());

  public MantenimientoCampaniaView() {
    addClassName("app-main mantenimiento-campania-view");
    setSizeFull();
    setPadding(true);
    setSpacing(true);
    getStyle().set("position", "relative");

    // Configurar la cuadrícula
    configureGrid();

    // Configurar el formulario
    configureForm();

    // Crear layout principal
    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Crear título
    H2 pageTitle = new H2("Mantenimiento de Campañas");
    pageTitle.addClassName("bbva-page-title");
    pageTitle.getStyle().set("margin", "0 0 1rem 0");

    // Crear y configurar componentes
    HorizontalLayout filtersPanel = createFiltersPanel();
    HorizontalLayout toolbar = createToolbar();
    VerticalLayout gridCard = new VerticalLayout(grid);
    gridCard.addClassName("bbva-grid-card");
    gridCard.setSizeFull();

    // Crear drawer flotante
    drawerPanel = createDrawerPanel();

    // Agregar componentes al layout principal
    mainLayout.add(pageTitle, filtersPanel, toolbar, gridCard);
    mainLayout.setFlexGrow(1, gridCard);

    add(mainLayout, drawerPanel);

    // Cargar datos de ejemplo
    loadSampleData();
  }

  private void configureGrid() {
    grid.addClassName("bbva-grid");
    grid.setSizeFull();
    grid.setSelectionMode(Grid.SelectionMode.SINGLE);

    // Configurar columnas
    grid.addColumn(CampaniaMultiplicaBean::getNombre).setHeader("Nombre").setAutoWidth(true);
    grid.addColumn(c -> c.getFechaInicio() != null ? c.getFechaInicio().toString() : "")
        .setHeader("Inicio")
        .setAutoWidth(true);
    grid.addColumn(c -> c.getFechaFin() != null ? c.getFechaFin().toString() : "")
        .setHeader("Fin")
        .setAutoWidth(true);
    grid.addColumn(CampaniaMultiplicaBean::getEstado).setHeader("Estado").setAutoWidth(true);
    grid.addColumn(CampaniaMultiplicaBean::getCanal).setHeader("Canal").setAutoWidth(true);
    grid.addColumn(c -> c.getValor1() + " - " + c.getValor2())
        .setHeader("Valores")
        .setAutoWidth(true);
    grid.addColumn(c -> c.getRango1() + " - " + c.getRango2())
        .setHeader("Rangos")
        .setAutoWidth(true);

    // Configurar acciones
    grid.addComponentColumn(
            campania -> {
              Button editButton = new Button("Editar");
              editButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
              editButton.addClickListener(e -> editCampania(campania));
              return editButton;
            })
        .setHeader("Acciones")
        .setAutoWidth(true);
  }

  private void configureForm() {
    // Configurar campos del formulario
    txtNombre.setRequired(true);
    txtDescripcion.setRequired(true);
    dtpFechaInicio.setRequired(true);
    dtpFechaFin.setRequired(true);
    cmbEstado.setRequired(true);
    cmbCanal.setRequired(true);

    // Configurar opciones de combos
    cmbEstado.setItems(ACTIVO, INACTIVO);
    cmbCanal.setItems("BANCA_POR_INTERNET", "BANCA_MOVIL", "AGENCIA", "TODOS");

    // Configurar botones
    btnGuardar.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnGuardar.addClassName("bbva-button");
    btnGuardar.addClickListener(e -> saveCampania());

    btnCancelar.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
    btnCancelar.addClassName("bbva-button");
    btnCancelar.addClickListener(e -> clearForm());

    btnNuevo.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnNuevo.addClassName("bbva-button");
    btnNuevo.addClickListener(e -> newCampania());
  }

  private VerticalLayout createFormContent() {
    VerticalLayout formContent = new VerticalLayout();
    formContent.setPadding(false);
    formContent.setSpacing(true);
    formContent.setWidthFull();

    // Configurar campos del formulario
    txtNombre.setLabel("Nombre:");
    txtNombre.setWidthFull();
    txtNombre.setRequired(true);

    txtDescripcion.setLabel("Descripción:");
    txtDescripcion.setWidthFull();
    txtDescripcion.setRequired(true);

    dtpFechaInicio.setLabel("Fecha de Inicio:");
    dtpFechaInicio.setWidthFull();
    dtpFechaInicio.setRequired(true);

    dtpFechaFin.setLabel("Fecha de Fin:");
    dtpFechaFin.setWidthFull();
    dtpFechaFin.setRequired(true);

    cmbEstado.setLabel("Estado:");
    cmbEstado.setWidthFull();
    cmbEstado.setRequired(true);
    cmbEstado.setItems(ACTIVO, INACTIVO);

    cmbCanal.setLabel("Canal:");
    cmbCanal.setWidthFull();
    cmbCanal.setRequired(true);
    cmbCanal.setItems("BANCA_POR_INTERNET", "BANCA_MOVIL", "AGENCIA", "TODOS");

    // Configurar campos adicionales
    txtDivisa.setLabel("Divisa:");
    txtDivisa.setWidthFull();

    txtValor1.setLabel("Valor 1:");
    txtValor1.setWidthFull();

    txtValor2.setLabel("Valor 2:");
    txtValor2.setWidthFull();

    txtRango1.setLabel("Rango 1:");
    txtRango1.setWidthFull();

    txtRango2.setLabel("Rango 2:");
    txtRango2.setWidthFull();

    // Configurar botones
    HorizontalLayout buttonsLayout = new HorizontalLayout();
    buttonsLayout.setWidthFull();
    buttonsLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.END);

    btnGuardar.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnGuardar.addClassName("bbva-button");
    btnGuardar.addClickListener(e -> saveCampania());

    btnCancelar.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
    btnCancelar.addClassName("bbva-button");
    btnCancelar.addClickListener(e -> clearForm());

    buttonsLayout.add(btnCancelar, btnGuardar);

    // Agregar todos los componentes al formulario
    formContent.add(
        txtNombre,
        txtDescripcion,
        dtpFechaInicio,
        dtpFechaFin,
        cmbEstado,
        cmbCanal,
        txtDivisa,
        txtValor1,
        txtValor2,
        txtRango1,
        txtRango2,
        buttonsLayout);

    return formContent;
  }

  private HorizontalLayout createToolbar() {
    HorizontalLayout toolbar = new HorizontalLayout();
    toolbar.setWidthFull();
    toolbar.setJustifyContentMode(FlexComponent.JustifyContentMode.END);
    toolbar.setAlignItems(FlexComponent.Alignment.CENTER);
    toolbar.addClassName("bbva-toolbar");

    // Botón para abrir/cerrar el drawer
    btnNuevo.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnNuevo.addClassName("bbva-filters-button");
    btnNuevo.addClickListener(e -> toggleDrawer());

    toolbar.add(btnNuevo);
    return toolbar;
  }

  private VerticalLayout createDrawerPanel() {
    VerticalLayout drawer = new VerticalLayout();
    drawer.addClassName("drawer-panel");
    drawer.setWidth("450px");
    drawer.setHeight("100%");
    drawer.setPadding(true);
    drawer.setSpacing(true);

    // Estilos CSS para el drawer flotante
    drawer
        .getStyle()
        .set("position", "fixed")
        .set("top", "0")
        .set("right", "-450px") // Inicialmente oculto
        .set("background", "var(--bbva-white)")
        .set("box-shadow", "-4px 0 20px rgba(0, 0, 0, 0.15)")
        .set("z-index", "1000")
        .set("transition", "right 0.3s ease-in-out")
        .set("overflow-y", "auto");

    // Header del drawer
    HorizontalLayout drawerHeader = new HorizontalLayout();
    drawerHeader.setWidthFull();
    drawerHeader.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    drawerHeader.setAlignItems(FlexComponent.Alignment.CENTER);

    H2 drawerTitle = new H2("Formulario de Campaña");
    drawerTitle.addClassName("bbva-page-title");
    drawerTitle.getStyle().set("margin", "0");

    btnCerrarDrawer.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
    btnCerrarDrawer.addClickListener(e -> toggleDrawer());

    drawerHeader.add(drawerTitle, btnCerrarDrawer);

    // Contenido del formulario
    VerticalLayout formContent = createFormContent();

    drawer.add(drawerHeader, formContent);
    drawer.setFlexGrow(1, formContent);

    return drawer;
  }

  private void toggleDrawer() {
    if (drawerVisible) {
      closeDrawer();
    } else {
      openDrawer();
    }
  }

  private void openDrawer() {
    drawerVisible = true;
    drawerPanel.getStyle().set("right", "0");
    btnNuevo.setText("Cerrar");
    btnNuevo.setIcon(VaadinIcon.CLOSE.create());
  }

  private void closeDrawer() {
    drawerVisible = false;
    drawerPanel.getStyle().set("right", "-450px");
    btnNuevo.setText("Nueva Campaña");
    btnNuevo.setIcon(VaadinIcon.PLUS.create());
    clearForm();
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.END);
    filtersPanel.setSpacing(true);
    filtersPanel.setPadding(true);

    // Configurar filtros
    filtroEstado.addClassName("bbva-input-floating");
    filtroEstado.setItems("TODOS", ACTIVO, INACTIVO);
    filtroEstado.setValue("TODOS");
    filtroEstado.setPlaceholder("Estado");
    filtroEstado.setClearButtonVisible(true);
    filtroEstado.setWidth("150px");

    filtroCanal.addClassName("bbva-input-floating");
    filtroCanal.setItems("TODOS", "BANCA_POR_INTERNET", "BANCA_MOVIL", "AGENCIA");
    filtroCanal.setValue("TODOS");
    filtroCanal.setPlaceholder("Canal");
    filtroCanal.setClearButtonVisible(true);
    filtroCanal.setWidth("150px");

    filtroNombre.addClassName("bbva-input-floating");
    filtroNombre.setPlaceholder("Filtrar por nombre...");
    filtroNombre.setWidth("200px");

    filtroDescripcion.addClassName("bbva-input-floating");
    filtroDescripcion.setPlaceholder("Filtrar por descripción...");
    filtroDescripcion.setWidth("250px");

    filtroFechaInicio.addClassName("bbva-input-floating");
    filtroFechaInicio.setPlaceholder("Fecha inicio");
    filtroFechaInicio.setWidth("160px");

    filtroFechaFin.addClassName("bbva-input-floating");
    filtroFechaFin.setPlaceholder("Fecha fin");
    filtroFechaFin.setWidth("160px");

    // Añadir listeners para los filtros
    filtroEstado.addValueChangeListener(e -> applyFilters());
    filtroNombre.addValueChangeListener(e -> applyFilters());
    filtroDescripcion.addValueChangeListener(e -> applyFilters());
    filtroCanal.addValueChangeListener(e -> applyFilters());
    filtroFechaInicio.addValueChangeListener(e -> applyFilters());
    filtroFechaFin.addValueChangeListener(e -> applyFilters());

    // Añadir los filtros al panel
    filtersPanel.add(
        filtroEstado,
        filtroNombre,
        filtroDescripcion,
        filtroCanal,
        filtroFechaInicio,
        filtroFechaFin);

    return filtersPanel;
  }

  private void loadSampleData() {
    // Datos de ejemplo
    CampaniaMultiplicaBean campania1 = new CampaniaMultiplicaBean();
    campania1.setId(1L);
    campania1.setNombre("Campaña Verano 2023");
    campania1.setDescripcion("Promoción de verano para todos los clientes");
    campania1.setDivisa("PEN");
    campania1.setValor1("100");
    campania1.setValor2("500");
    campania1.setRango1("1");
    campania1.setRango2("5");
    campania1.setFechaInicio(new Date());
    campania1.setFechaFin(new Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000));
    campania1.setEstado(ACTIVO);
    campania1.setCanal("BANCA_POR_INTERNET");

    CampaniaMultiplicaBean campania2 = new CampaniaMultiplicaBean();
    campania2.setId(2L);
    campania2.setNombre("Black Friday");
    campania2.setDescripcion("Ofertas especiales para Black Friday");
    campania2.setDivisa("USD");
    campania2.setValor1("50");
    campania2.setValor2("300");
    campania2.setRango1("1");
    campania2.setRango2("3");
    campania2.setFechaInicio(new Date());
    campania2.setFechaFin(new Date(System.currentTimeMillis() + 15L * 24 * 60 * 60 * 1000));
    campania2.setEstado(ACTIVO);
    campania2.setCanal("BANCA_MOVIL");

    allCampanias.add(campania1);
    allCampanias.add(campania2);

    applyFilters();
  }

  private void applyFilters() {
    List<CampaniaMultiplicaBean> filtered = new ArrayList<>(allCampanias);

    // Aplicar filtros
    if (filtroEstado.getValue() != null && !filtroEstado.getValue().equals("Todos")) {
      filtered.removeIf(c -> !c.getEstado().equals(filtroEstado.getValue()));
    }

    if (filtroNombre.getValue() != null && !filtroNombre.getValue().isEmpty()) {
      String filter = filtroNombre.getValue().toLowerCase();
      filtered.removeIf(c -> !c.getNombre().toLowerCase().contains(filter));
    }

    if (filtroDescripcion.getValue() != null && !filtroDescripcion.getValue().isEmpty()) {
      String filter = filtroDescripcion.getValue().toLowerCase();
      filtered.removeIf(c -> !c.getDescripcion().toLowerCase().contains(filter));
    }

    if (filtroCanal.getValue() != null && !filtroCanal.getValue().equals("Todos")) {
      filtered.removeIf(c -> !c.getCanal().equals(filtroCanal.getValue()));
    }

    if (filtroFechaInicio.getValue() != null) {
      filtered.removeIf(
          c ->
              c.getFechaInicio() == null
                  || c.getFechaInicio()
                      .before(java.sql.Date.valueOf(filtroFechaInicio.getValue())));
    }

    if (filtroFechaFin.getValue() != null) {
      filtered.removeIf(
          c ->
              c.getFechaFin() == null
                  || c.getFechaFin()
                      .after(java.sql.Date.valueOf(filtroFechaFin.getValue().plusDays(1))));
    }

    campaniasList.clear();
    campaniasList.addAll(filtered);
    grid.setItems(campaniasList);
  }

  private void newCampania() {
    clearForm();
    // Aquí podrías abrir un diálogo o mostrar el formulario
  }

  private void editCampania(CampaniaMultiplicaBean campania) {
    // Cargar los datos de la campaña en el formulario
    txtNombre.setValue(campania.getNombre() != null ? campania.getNombre() : "");
    txtDescripcion.setValue(campania.getDescripcion() != null ? campania.getDescripcion() : "");
    txtDivisa.setValue(campania.getDivisa() != null ? campania.getDivisa() : "");
    txtValor1.setValue(campania.getValor1() != null ? Double.parseDouble(campania.getValor1()) : 0);
    txtValor2.setValue(campania.getValor2() != null ? Double.parseDouble(campania.getValor2()) : 0);
    txtRango1.setValue(campania.getRango1() != null ? campania.getRango1() : "");
    txtRango2.setValue(campania.getRango2() != null ? campania.getRango2() : "");

    if (campania.getFechaInicio() != null) {
      dtpFechaInicio.setValue(
          campania.getFechaInicio().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
    }

    if (campania.getFechaFin() != null) {
      dtpFechaFin.setValue(
          campania.getFechaFin().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
    }

    cmbEstado.setValue(campania.getEstado() != null ? campania.getEstado() : "");
    cmbCanal.setValue(campania.getCanal() != null ? campania.getCanal() : "");
  }

  private void saveCampania() {
    // Validar campos requeridos
    if (txtNombre.isEmpty()
        || txtDescripcion.isEmpty()
        || dtpFechaInicio.isEmpty()
        || dtpFechaFin.isEmpty()
        || cmbEstado.isEmpty()
        || cmbCanal.isEmpty()) {

      Notification.show(
              "Por favor complete todos los campos requeridos", 3000, Notification.Position.MIDDLE)
          .addThemeVariants(NotificationVariant.LUMO_ERROR);
      return;
    }

    // Crear o actualizar la campaña
    CampaniaMultiplicaBean campania = new CampaniaMultiplicaBean();
    campania.setId(allCampanias.size() + 1L); // ID temporal
    campania.setNombre(txtNombre.getValue());
    campania.setDescripcion(txtDescripcion.getValue());
    campania.setDivisa(txtDivisa.getValue());
    campania.setValor1(String.valueOf(txtValor1.getValue()));
    campania.setValor2(String.valueOf(txtValor2.getValue()));
    campania.setRango1(txtRango1.getValue());
    campania.setRango2(txtRango2.getValue());
    campania.setFechaInicio(java.sql.Date.valueOf(dtpFechaInicio.getValue()));
    campania.setFechaFin(java.sql.Date.valueOf(dtpFechaFin.getValue()));
    campania.setEstado(cmbEstado.getValue());
    campania.setCanal(cmbCanal.getValue());

    // Si es una nueva campaña, agregarla a la lista
    if (!allCampanias.contains(campania)) {
      allCampanias.add(campania);
      Notification.show("Campaña creada exitosamente", 3000, Notification.Position.TOP_END)
          .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
    } else {
      // Actualizar campaña existente
      int index = allCampanias.indexOf(campania);
      if (index != -1) {
        allCampanias.set(index, campania);
        Notification.show("Campaña actualizada exitosamente", 3000, Notification.Position.TOP_END)
            .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
      }
    }

    // Actualizar la cuadrícula
    applyFilters();
    clearForm();
  }

  private void clearForm() {
    txtNombre.clear();
    txtDescripcion.clear();
    txtDivisa.clear();
    txtValor1.clear();
    txtValor2.clear();
    txtRango1.clear();
    txtRango2.clear();
    dtpFechaInicio.clear();
    dtpFechaFin.clear();
    cmbEstado.clear();
    cmbCanal.clear();
  }
}
