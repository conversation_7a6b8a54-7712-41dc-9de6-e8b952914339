package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.datepicker.DatePicker;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;

@PageTitle("Mantenimiento de Campañas")
@Route(value = "mantenimiento-campania", layout = MainLayout.class)
public class MantenimientoCampaniaView extends VerticalLayout {

  // Filtros
  private final ComboBox<String> filtroEstado = new ComboBox<>("Estado");
  private final TextField filtroNombre = new TextField("Nombre");
  private final TextField filtroDescripcion = new TextField("Descripción");
  private final ComboBox<String> filtroCanal = new ComboBox<>("Canal");
  private final DatePicker filtroFechaInicio = new DatePicker("Fecha Inicio");
  private final DatePicker filtroFechaFin = new DatePicker("Fecha Fin");

  public MantenimientoCampaniaView() {
    addClassName("app-main");
    setSizeFull();
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Filtros de Mantenimiento de Campañas");
    title.addClassName("bbva-page-title");

    // Panel de filtros
    HorizontalLayout filtersPanel = createFiltersPanel();

    mainLayout.add(title, filtersPanel);
    add(mainLayout);
  }

  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.END);
    filtersPanel.setSpacing(true);
    filtersPanel.setPadding(true);

    filtroEstado.addClassName("bbva-input-floating");
    filtroEstado.setItems("TODOS", "ACTIVO", "INACTIVO");
    filtroEstado.setValue("TODOS");
    filtroEstado.setWidth("150px");

    filtroNombre.addClassName("bbva-input-floating");
    filtroNombre.setPlaceholder("Filtrar por nombre...");
    filtroNombre.setWidth("200px");

    filtroDescripcion.addClassName("bbva-input-floating");
    filtroDescripcion.setPlaceholder("Filtrar por descripción...");
    filtroDescripcion.setWidth("250px");

    filtroCanal.addClassName("bbva-input-floating");
    filtroCanal.setItems("TODOS", "BANCA_POR_INTERNET", "BANCA_MOVIL", "AGENCIA");
    filtroCanal.setValue("TODOS");
    filtroCanal.setWidth("150px");

    filtroFechaInicio.addClassName("bbva-input-floating");
    filtroFechaInicio.setPlaceholder("Fecha inicio");
    filtroFechaInicio.setWidth("160px");

    filtroFechaFin.addClassName("bbva-input-floating");
    filtroFechaFin.setPlaceholder("Fecha fin");
    filtroFechaFin.setWidth("160px");

    filtersPanel.add(
        filtroEstado,
        filtroNombre,
        filtroDescripcion,
        filtroCanal,
        filtroFechaInicio,
        filtroFechaFin);

    return filtersPanel;
  }
}
