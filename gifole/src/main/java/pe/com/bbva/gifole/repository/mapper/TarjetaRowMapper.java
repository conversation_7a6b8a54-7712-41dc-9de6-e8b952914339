package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.TCToc;

@SuppressWarnings("rawtypes")
@Component
public class TarjetaRowMapper implements RowMapper {

  public Object mapRow(ResultSet rs, int rowNum) throws SQLException {

    TCToc param = new TCToc();
    param.setId(rs.getLong("ID"));
    param.setCodigoCentral(rs.getString("COD_CENTRAL"));
    param.setNroContrato(rs.getString("NRO_CONTRATO"));
    param.setNroTarjeta(rs.getString("NRO_TARJETA"));
    param.setFechaModificacionFormat(rs.getString("FECHA_MODIFICACION"));
    param.setMultibin(rs.getString("MULTIBIN"));
    param.setLinea(rs.getString("LINEA_CREDITO"));
    param.setFechaPago(rs.getString("FECHA_PAGO"));

    return param;
  }
}
