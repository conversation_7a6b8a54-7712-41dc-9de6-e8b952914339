package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;

@PageTitle("Historial de Carga")
@Route(value = "historial-carga", layout = MainLayout.class)
public class HistorialCargaView extends VerticalLayout {

  private final ComboBox<String> tipoArchivoFilter = new ComboBox<>("Tipo de Archivo");
  private final Button filtrarButton = new Button("Filtrar");

  public HistorialCargaView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Filtros de Historial de Carga");
    title.addClassName("bbva-page-title");

    // Panel de filtros
    VerticalLayout filterCard = createFilterCard();

    mainLayout.add(title, filterCard);
    add(mainLayout);
  }

  private VerticalLayout createFilterCard() {
    VerticalLayout filterCard = new VerticalLayout();
    filterCard.setWidthFull();
    filterCard.addClassName("filter-card");
    filterCard.getStyle().set("border", "1px solid #ddd");
    filterCard.getStyle().set("border-radius", "8px");
    filterCard.getStyle().set("box-shadow", "0 2px 4px rgba(0, 0, 0, 0.1)");
    filterCard.getStyle().set("padding", "20px");

    // Layout horizontal para los filtros
    HorizontalLayout filterLayout = new HorizontalLayout();
    filterLayout.setWidthFull();
    filterLayout.setSpacing(true);

    // Configurar ComboBox
    tipoArchivoFilter.setPlaceholder("Seleccione tipo de archivo");
    tipoArchivoFilter.setWidth("300px");
    tipoArchivoFilter.setItems("Todos", "Categoría", "Prima Mínima", "Tarifa");

    // Botón filtrar
    filtrarButton.addThemeVariants(ButtonVariant.LUMO_PRIMARY);

    filterLayout.add(tipoArchivoFilter, filtrarButton);
    filterCard.add(filterLayout);

    return filterCard;
  }
}
