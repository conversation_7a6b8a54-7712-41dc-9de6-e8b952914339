package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import pe.com.bbva.gifole.common.enums.TipoArchivoEnum;
import pe.com.bbva.gifole.domain.VehCotizadorCargaHistorial;

@PageTitle("Historial de Carga")
@Route(value = "historial-carga", layout = MainLayout.class)
public class HistorialCargaView extends VerticalLayout {

  // Constantes
  private static final String CATEGORIA = "Categoría";
  private static final String PRIMA_MINIMA = "Prima Mínima";
  private static final String TARIFA = "Tarifa";
  private static final String PROCESADO = "Procesado";
  private static final String NO_TERMINADO = "No Terminado";
  private static final String CATEGORIA_TXT = "categoria.txt";
  private static final String PRIMA_MINIMA_TXT = "prima_minima.txt";
  private static final String TARIFA_TXT = "tarifa.txt";
  private static final String TODOS = "Todos";

  private final Grid<VehCotizadorCargaHistorial> grid =
      new Grid<>(VehCotizadorCargaHistorial.class, false);
  private final List<VehCotizadorCargaHistorial> historialList = new ArrayList<>();

  // Filtros
  private final ComboBox<String> tipoArchivoFilter = new ComboBox<>();
  private final Button filtrarButton = new Button("Filtrar");

  public HistorialCargaView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    // Layout principal
    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Card para filtros
    VerticalLayout filterCard = createFilterCard();

    // Card para el Grid
    VerticalLayout gridCard = createGridCard();

    mainLayout.add(filterCard, gridCard);
    mainLayout.setFlexGrow(0, filterCard);
    mainLayout.setFlexGrow(1, gridCard);

    add(mainLayout);

    // Cargar datos de ejemplo
    loadSampleData();
  }

  private VerticalLayout createFilterCard() {
    VerticalLayout filterCard = new VerticalLayout();
    filterCard.setWidthFull();
    filterCard.getStyle().set("border", "1px solid #ddd");
    filterCard.getStyle().set("border-radius", "8px");
    filterCard.getStyle().set("box-shadow", "0 2px 4px rgba(0, 0, 0, 0.1)");
    filterCard.getStyle().set("padding", "20px");
    filterCard.addClassName("filter-card");

    H2 filterTitle = new H2("Historial de Carga de Archivos");
    filterTitle.addClassName("bbva-page-title");

    // Layout horizontal para los filtros
    HorizontalLayout filterLayout = new HorizontalLayout();
    filterLayout.setWidthFull();
    filterLayout.setSpacing(true);
    filterLayout.setAlignItems(FlexComponent.Alignment.END);

    // Configurar ComboBox de tipo de archivo
    tipoArchivoFilter.setLabel("Tipo de Archivo");
    tipoArchivoFilter.setPlaceholder("Seleccione tipo de archivo");
    tipoArchivoFilter.setWidth("300px");

    // Agregar opciones del enum
    List<String> tiposArchivo = new ArrayList<>();
    tiposArchivo.add(TODOS);
    for (TipoArchivoEnum tipo : TipoArchivoEnum.values()) {
      tiposArchivo.add(tipo.getNombre());
    }
    tipoArchivoFilter.setItems(tiposArchivo);
    tipoArchivoFilter.setValue("Automás"); // Valor por defecto según la imagen

    // Configurar botón filtrar
    filtrarButton.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    filtrarButton.addClickListener(e -> aplicarFiltros());

    filterLayout.add(tipoArchivoFilter, filtrarButton);
    filterCard.add(filterTitle, filterLayout);

    return filterCard;
  }

  private VerticalLayout createGridCard() {
    VerticalLayout gridCard = new VerticalLayout();
    gridCard.setSizeFull();
    gridCard.addClassName("bbva-grid-card");

    configureGrid();
    gridCard.add(grid);

    return gridCard;
  }

  private void configureGrid() {
    grid.setSizeFull();
    grid.addClassName("historial-grid");

    // Configurar columnas según la imagen
    grid.addColumn(VehCotizadorCargaHistorial::getTipoCarga)
        .setHeader("Tipo de Carga")
        .setSortable(true);

    grid.addColumn(VehCotizadorCargaHistorial::getNombre).setHeader("Nombre").setSortable(true);

    grid.addColumn(historial -> formatDate(historial.getCreacion()))
        .setHeader("Fecha de Carga")
        .setSortable(true);

    grid.addColumn(historial -> formatDate(historial.getModificacion()))
        .setHeader("Fecha de Proceso")
        .setSortable(true);

    grid.addColumn(VehCotizadorCargaHistorial::getEstado).setHeader("Estado").setSortable(true);

    grid.addColumn(VehCotizadorCargaHistorial::getError).setHeader("Error");

    // Configurar selección
    grid.setSelectionMode(Grid.SelectionMode.SINGLE);
  }

  private String formatDate(Date date) {
    if (date == null) return "";
    SimpleDateFormat sdf = new SimpleDateFormat("dd MMM. yyyy HH:mm:ss");
    return sdf.format(date);
  }

  private void aplicarFiltros() {
    String tipoSeleccionado = tipoArchivoFilter.getValue();

    if (tipoSeleccionado == null || TODOS.equals(tipoSeleccionado)) {
      grid.setItems(historialList);
    } else {
      List<VehCotizadorCargaHistorial> filtrados =
          historialList.stream().filter(h -> tipoSeleccionado.equals(h.getTipoCarga())).toList();
      grid.setItems(filtrados);
    }
  }

  private void loadSampleData() {
    // Datos de ejemplo basados en la imagen proporcionada

    // Registros de Categoría
    historialList.add(
        createHistorialItem(
            CATEGORIA,
            CATEGORIA_TXT,
            createDate(2023, 7, 15, 17, 56, 17),
            createDate(2023, 7, 15, 17, 48, 33),
            PROCESADO,
            null));

    historialList.add(
        createHistorialItem(
            CATEGORIA,
            CATEGORIA_TXT,
            createDate(2023, 7, 11, 16, 43, 25),
            createDate(2023, 7, 11, 16, 35, 0),
            PROCESADO,
            null));

    historialList.add(
        createHistorialItem(
            CATEGORIA,
            CATEGORIA_TXT,
            createDate(2023, 7, 11, 16, 41, 15),
            createDate(2023, 7, 11, 16, 32, 45),
            NO_TERMINADO,
            "Error: ORA-00001: restricción única (GIFOLE.PK_VEH_TIPO) violada"));

    historialList.add(
        createHistorialItem(
            CATEGORIA,
            CATEGORIA_TXT,
            createDate(2023, 7, 11, 16, 40, 27),
            createDate(2023, 7, 11, 16, 32, 3),
            NO_TERMINADO,
            "Error: ORA-28913: error al ejecutar la llamada de ODCIEXTTABLEOPEN ORA-29400: erro"));

    historialList.add(
        createHistorialItem(
            CATEGORIA,
            CATEGORIA_TXT,
            createDate(2023, 7, 11, 16, 36, 55),
            createDate(2023, 7, 11, 16, 28, 24),
            PROCESADO,
            null));

    historialList.add(
        createHistorialItem(
            CATEGORIA,
            CATEGORIA_TXT,
            createDate(2023, 7, 11, 16, 32, 5),
            createDate(2023, 7, 11, 16, 23, 43),
            PROCESADO,
            null));

    historialList.add(
        createHistorialItem(
            CATEGORIA,
            CATEGORIA_TXT,
            createDate(2023, 7, 11, 16, 21, 26),
            createDate(2023, 7, 11, 16, 13, 4),
            NO_TERMINADO,
            "Error: ORA-00001: restricción única (GIFOLE.PK_VEH_TIPO) violada"));

    historialList.add(
        createHistorialItem(
            CATEGORIA,
            CATEGORIA_TXT,
            createDate(2023, 7, 11, 16, 8, 30),
            createDate(2023, 7, 11, 16, 0, 25),
            NO_TERMINADO,
            "Error: ORA-00001: restricción única (GIFOLE.PK_VEH_TIPO) violada"));

    historialList.add(
        createHistorialItem(
            CATEGORIA,
            CATEGORIA_TXT,
            createDate(2023, 7, 11, 16, 6, 32),
            createDate(2023, 7, 11, 15, 58, 34),
            NO_TERMINADO,
            "Error: ORA-00001: restricción única (GIFOLE.PK_VEH_TIPO) violada"));

    // Registros de Prima Mínima
    historialList.add(
        createHistorialItem(
            PRIMA_MINIMA,
            PRIMA_MINIMA_TXT,
            createDate(2023, 6, 21, 10, 18, 55),
            createDate(2023, 6, 21, 10, 13, 10),
            PROCESADO,
            null));

    historialList.add(
        createHistorialItem(
            PRIMA_MINIMA,
            PRIMA_MINIMA_TXT,
            createDate(2023, 6, 13, 9, 13, 53),
            createDate(2023, 6, 13, 9, 6, 50),
            PROCESADO,
            null));

    // Registros de Tarifa
    historialList.add(
        createHistorialItem(
            TARIFA,
            TARIFA_TXT,
            createDate(2023, 6, 13, 9, 12, 26),
            createDate(2023, 6, 13, 9, 5, 5),
            PROCESADO,
            null));

    historialList.add(
        createHistorialItem(
            TARIFA,
            TARIFA_TXT,
            createDate(2023, 6, 13, 9, 8, 24),
            createDate(2023, 6, 13, 9, 2, 2),
            PROCESADO,
            null));

    historialList.add(
        createHistorialItem(
            CATEGORIA,
            CATEGORIA_TXT,
            createDate(2023, 6, 12, 18, 0, 54),
            createDate(2023, 6, 12, 17, 53, 46),
            NO_TERMINADO,
            "Error: ORA-00001: restricción única (GIFOLE.PK_VEH_TIPO) violada"));

    // Configurar grid con todos los datos
    grid.setItems(historialList);
  }

  private VehCotizadorCargaHistorial createHistorialItem(
      String tipoCarga,
      String nombre,
      Date fechaCarga,
      Date fechaProceso,
      String estado,
      String error) {
    VehCotizadorCargaHistorial item = new VehCotizadorCargaHistorial();
    item.setTipoCarga(tipoCarga);
    item.setNombre(nombre);
    item.setCreacion(fechaCarga);
    item.setModificacion(fechaProceso);
    item.setEstado(estado);
    item.setError(error);
    return item;
  }

  @SuppressWarnings("deprecation")
  private Date createDate(int year, int month, int day, int hour, int minute, int second) {
    return new Date(year - 1900, month - 1, day, hour, minute, second);
  }
}
