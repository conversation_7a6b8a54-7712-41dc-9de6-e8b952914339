package pe.com.bbva.gifole.common.bean;

import java.util.List;

public class UsuarioBean {
  private static final boolean AUTH_ERROR_DEFAULT = false;
  private boolean authError = AUTH_ERROR_DEFAULT;
  private List<String> rolesLDAP;

  public boolean isAuthError() {
    return authError;
  }

  public void setAuthError(boolean authError) {
    this.authError = authError;
  }

  public List<String> getRolesLDAP() {
    return rolesLDAP;
  }

  public void setRolesLDAP(List<String> rolesLDAP) {
    this.rolesLDAP = rolesLDAP;
  }
}
