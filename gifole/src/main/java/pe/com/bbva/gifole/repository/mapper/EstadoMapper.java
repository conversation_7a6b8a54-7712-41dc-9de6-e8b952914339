package pe.com.bbva.gifole.repository.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pe.com.bbva.gifole.domain.Estado;

@Component
public class EstadoMapper implements RowMapper<Estado> {

  @Override
  public Estado mapRow(ResultSet rs, int i) throws SQLException {

    Estado estado = new Estado();

    estado.setId(rs.getString("ID"));
    estado.setNombre(rs.getString("NOMBRE"));
    estado.setCreacion(rs.getString("CREACION"));
    estado.setCreador(rs.getString("CREADOR"));
    estado.setObservacion(rs.getString("OBSERVACION"));
    estado.setTextopedido(rs.getString("TEXTOPEDIDO"));

    return estado;
  }
}
