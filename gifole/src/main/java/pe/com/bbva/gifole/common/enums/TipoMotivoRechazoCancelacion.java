package pe.com.bbva.gifole.common.enums;

public enum TipoMotivoRechazoCancelacion {
  PRESTAMOS_PRE001("PRE001", "CUENTA ASOCIADA NO TIENE SALDO SUFICIENTE"),
  PRESTAMOS_PRE002("PRE002", "PRÉSTAMO CON ESTADO JUDICIAL/EXTRAJUDICIAL"),
  PRESTAMOS_PRE003("PRE003", "PRÉSTAMO CON CUOTAS VENCIDAS"),
  TARJETAS_TAR001("TAR001", "CONTRATO CON SALDO UTILIZADO"),
  TARJETAS_TAR002("TAR002", "OPERACIÓN(ES) PENDIENTE(S) DE PROCESAR"),
  TARJETAS_TAR003("TAR003", "TARJETA CON SALDO ACREEDOR SIN CUENTA"),
  SEGUROS_SEG001("SEG001", "EL SEGURO TIENE CUOTAS IMPAGAS"),
  SEGUROS_SEG002("SEG002", "<PERSON><PERSON>ICITANTE NO ES RESPONSABLE DE PAGOS"),
  CUENTAS_CTA001("CTA001", "CUENTAS MANCOMUNADAS"),
  CUENTAS_CTA002("CTA002", "CUENTAS CON RETENCIONES"),
  CUENTAS_CTA003("CTA003", "CUENTA CON  GARANTÍAS "),
  CUENTAS_CTA004("CTA004", "CUENTA CON SALDO NEGATIVOS O PAGOS PENDIENTES"),
  CUENTAS_CTA005("CTA005", "CUENTA CON SALDO A FAVOR"),
  CUENTAS_CTA006("CTA006", "CUENTA CON SERVICIOS AFILIADOS"),
  CUENTAS_CTA007("CTA007", "CUENTA CON PRODUCTOS ASOCIADOS"),
  CUENTAS_CTA008("CTA008", "CUENTA CON CHEQUERA"),
  CUENTAS_CTA009("CTA009", "CUENTAS CON RETENCIONES POR PREMIOS"),
  DEPOSITOS_DEP001("DEP001", "DEPÓSITOS MANCOMUNADOS"),
  DEPOSITOS_DEP002("DEP002", "DEPÓSITO A PLAZO CON RETENCIONES "),
  DEPOSITOS_DEP003("DEP003", "DEPÓSITO A PLAZO CON GARANTÍAS"),
  DEPOSITOS_DEP004(
      "DEP004", "CLIENTE SIN CUENTA ASOCIADA PARA EL PAGO DE INTERESES Y CAPITAL ACTIVA"),
  DEPOSITOS_DEP005("DEP005", "SIN SALDO CERO "),
  DEPOSITOS_DEP006("DEP006", "DIVA");

  private final String codigo;
  private final String descripcionMotivo;

  private TipoMotivoRechazoCancelacion(String codigo, String descripcionMotivo) {

    this.codigo = codigo;
    this.descripcionMotivo = descripcionMotivo;
  }

  public String getCodigo() {
    return codigo;
  }

  public String getDescripcionMotivo() {
    return descripcionMotivo;
  }
}
