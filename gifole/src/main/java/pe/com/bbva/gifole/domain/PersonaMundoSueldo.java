package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "PERSONA_MUNDO_SUELDO")
public class PersonaMundoSueldo implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_PERSONA_MUNDO_SUELDO")
  @TableGenerator(
      name = "SEQ_PERSONA_MUNDO_SUELDO",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.PersonaMundoSueldo",
      allocationSize = 1)
  private Long id;

  @Column(name = "NOMBRE", length = 50)
  private String nombre;

  @Column(name = "APELLIDO", length = 50)
  private String apellido;

  @Column(name = "TIPO_DOCUMENTO", length = 1)
  private String tipoDocumento;

  @Column(name = "DOCUMENTO", length = 20)
  private String documento;

  @Column(name = "DEPARTAMENTO", length = 2)
  private String departamento;

  @Column(name = "CORREO", length = 80)
  private String correo;

  @Column(name = "TELEFONO", length = 20)
  private String telefono;

  @Column(name = "HORARIO", length = 20)
  private String horario;

  @Column(name = "AUTORIZACION", length = 1)
  private String autorizacion;

  @Column(name = "FECHA_REGISTRO")
  private Date fechaRegistro;

  @Column(name = "PROCESADO", length = 1)
  private String procesado;
}
