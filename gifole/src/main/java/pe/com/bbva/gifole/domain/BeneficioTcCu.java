package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "BENEFICIO_TC_CU")
public class BeneficioTcCu implements Serializable {
  @Id
  @Column(name = "ID", length = 10)
  private String id;

  @Column(name = "BIN", length = 10)
  private String bin;

  @Column(name = "ESTADO", length = 50)
  private String estado;

  @Column(name = "DESCRIPCION", length = 200)
  private String descripcion;
}
