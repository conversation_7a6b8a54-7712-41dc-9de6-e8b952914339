package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Entity
@Table(name = "TC_BENEFICIOS")
public class TcBeneficios implements Serializable {

  @Id
  @Column(unique = true, nullable = false, precision = 16)
  @GeneratedValue(strategy = GenerationType.TABLE, generator = "SEQ_TC_BENEFICIOS")
  @TableGenerator(
      name = "SEQ_TC_BENEFICIOS",
      table = "SEQ_ENTIDAD",
      pkColumnName = "ENTIDAD",
      initialValue = 1,
      valueColumnName = "ULTIMO_ID",
      pkColumnValue = "pe.com.bbva.gifole.dominio.TcBeneficios",
      allocationSize = 1)
  private Long id;

  @Column(name = "SALUDO", length = 100)
  private String saludo;

  @Column(name = "TEXTO", length = 100)
  private String texto;

  @Column(name = "URL", length = 100)
  private String url;

  @Column(name = "URL_MOSTRAR", length = 100)
  private String urlMostrar;

  @Column(name = "URL_TRACKING", length = 100)
  private String urlTracking;

  @Column(length = 1)
  private String estado;

  @Column(name = "CREADOR", length = 10)
  private String creador;

  @Column(name = "FECHA_REGISTRO")
  private Date fechaRegistro;

  @Column(name = "EDITOR", length = 10)
  private String editor;

  @Column(name = "FECHA_MODIFICACION")
  private Date fechaModificacion;

  @Column(name = "FECHA_ACTIVACION")
  private Date fechaActivacion;

  @Transient private Date fechaRegistro1;

  @Transient private Date fechaRegistro2;

  @Transient private Date fechaActivacion1;

  @Transient private Date fechaActivacion2;
}
