package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import pe.com.bbva.gifole.common.bean.SemaforoDigitalDetalleBean;
import pe.com.bbva.gifole.util.Utilitario;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Semáforo Digital")
@Route(value = "reporte/otros/consulta-semaforo-digital", layout = MainLayout.class)
public class ConsultaSemaforoDigitalView extends VerticalLayout {

  public ConsultaSemaforoDigitalView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Semáforo Digital");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    DataTable<SemaforoDigitalDetalleBean> dataTable =
        DataTable.<SemaforoDigitalDetalleBean>builder()
            .id("tabla-semaforo-digital")
            .column(
                "codigoCentral",
                "Código Central",
                detalle -> detalle.getSemaforoDigitalBean().getCodigoCentral(),
                "100px")
            .column(
                "tipoDocumento",
                "Tipo Documento",
                detalle -> detalle.getSemaforoDigitalBean().getTipoDocumento(),
                "120px")
            .column(
                "numeroDocumento",
                "Número de Documento",
                detalle -> detalle.getSemaforoDigitalBean().getNroDocumento(),
                "130px")
            .column(
                "nombresApellidos",
                "Nombre Cliente",
                detalle -> detalle.getSemaforoDigitalBean().getNombreCompletoCliente(),
                "200px")
            .column(
                "correo",
                "Correo",
                detalle -> detalle.getSemaforoDigitalBean().getCorreo(),
                "200px")
            .column(
                "canal", "Canal", detalle -> detalle.getSemaforoDigitalBean().getCanal(), "100px")
            .column(
                "fechaHoraRegistro",
                "Fecha Registro",
                detalle -> Utilitario.formatearFecha(detalle.getCreacion(), "dd/MM/yyyy HH:mm"),
                "150px")
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
