package pe.com.bbva.gifole.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@Entity
@Table(name = "TC_ADICIONAL")
public class TCAdicional implements Serializable {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SeqGen")
  @SequenceGenerator(name = "SeqGen", sequenceName = "SQ_TC_ADICIONAL", allocationSize = 1)
  @Column(name = "ID")
  private Long id;

  @Column(name = "CODIGO_CENTRAL", length = 10)
  private String codigoCentral;

  @Column(name = "NOMBRE", length = 100)
  private String nombre;

  @Column(name = "TARJETA_TITULAR", length = 20)
  private String tarjetaTitular;

  @Column(name = "NOMBRE_TARJETA", length = 50)
  private String nombreTarjeta;

  @Column(name = "CORREO", length = 80)
  private String correo;

  @Column(name = "DIRECCION", length = 250)
  private String direccionAdi;

  @Column(name = "CANAL", length = 8)
  private String canal;

  @Column(name = "DIVISA", length = 10)
  private String divisa;

  @Column(name = "FECHA_REGISTRO")
  private Date fechaRegistro;

  @Column(name = "OFICINA_SOLICITANTE")
  private String oficinaSolicitante;

  @Column(name = "LOCALIDAD")
  private String localidad;

  @Column(name = "OFICINA_RECOJO")
  private String oficinaRecojo;

  @Transient private List<TCAdicionalDetalle> tcAdicionalDetalles;

  @Transient private Date fechaRegistro1;

  @Transient private Date fechaRegistro2;

  @Transient private String origenEnvio;
}
