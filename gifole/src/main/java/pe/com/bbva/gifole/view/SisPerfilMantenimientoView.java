package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import pe.com.bbva.gifole.dto.SisPerfilMantenimientoDTO;

/** Vista simplificada para el mantenimiento de perfiles del sistema - Sin tablas */
@PageTitle("Mantenimiento de Perfiles del Sistema")
@Route(value = "sis-perfil-mantenimiento", layout = MainLayout.class)
public class SisPerfilMantenimientoView extends VerticalLayout {

  // Constantes
  private static final String ACTIVO = "ACTIVO";
  private static final String INACTIVO = "INACTIVO";

  // Datos
  private final List<SisPerfilMantenimientoDTO> allPerfiles = new ArrayList<>();
  private List<SisPerfilMantenimientoDTO> filteredPerfiles = new ArrayList<>();

  // Filtros
  private final TextField filtroDescripcion = new TextField();
  private final ComboBox<String> filtroEstado = new ComboBox<>();

  public SisPerfilMantenimientoView() {
    setSizeFull();
    addClassName("app-main"); // Usando clase global para background
    setPadding(true);
    setSpacing(true);

    // Crear layout principal
    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Panel de filtros
    HorizontalLayout filtersPanel = createFiltersPanel();

    // Card principal simplificada (sin tabla)
    VerticalLayout contentCard = createContentCard();

    mainLayout.add(filtersPanel, contentCard);
    add(mainLayout);

    // Cargar datos de ejemplo
    loadSampleData();
  }

  /** Crea el panel de filtros */
  private HorizontalLayout createFiltersPanel() {
    HorizontalLayout filtersPanel = new HorizontalLayout();
    filtersPanel.addClassName("bbva-filters-card");
    filtersPanel.setWidthFull();
    filtersPanel.setAlignItems(Alignment.END);
    filtersPanel.setSpacing(true);
    filtersPanel.setPadding(true);

    // Configurar filtros
    filtroDescripcion.setLabel("Descripción");
    filtroDescripcion.addClassName("bbva-input-floating");
    filtroDescripcion.setWidth("200px");
    filtroDescripcion.setPlaceholder("Buscar por descripción...");

    filtroEstado.setLabel("Estado");
    filtroEstado.addClassName("bbva-input-floating");
    filtroEstado.setWidth("150px");
    filtroEstado.setItems("Todos", ACTIVO, INACTIVO);
    filtroEstado.setValue("Todos");

    // Botones de acción usando clases globales
    Button btnBuscar = new Button("Buscar", new Icon(VaadinIcon.SEARCH));
    btnBuscar.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnBuscar.addClassName("bbva-filters-button");
    btnBuscar.addClickListener(e -> aplicarFiltros());

    Button btnLimpiar = new Button("Limpiar", new Icon(VaadinIcon.REFRESH));
    btnLimpiar.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
    btnLimpiar.addClassName("bbva-filters-button");
    btnLimpiar.addClickListener(e -> limpiarFiltros());

    filtersPanel.add(filtroDescripcion, filtroEstado, btnBuscar, btnLimpiar);

    return filtersPanel;
  }

  /** Crea la card principal simplificada (sin tabla) */
  private VerticalLayout createContentCard() {
    VerticalLayout contentCard = new VerticalLayout();
    contentCard.setSizeFull();
    contentCard.addClassName("bbva-grid-card");
    contentCard.setSpacing(true);
    contentCard.setPadding(true);

    // Header con título y botón
    HorizontalLayout headerLayout = new HorizontalLayout();
    headerLayout.setWidthFull();
    headerLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
    headerLayout.setAlignItems(FlexComponent.Alignment.CENTER);

    H2 title = new H2("Perfiles del Sistema");
    title.addClassName("bbva-grid-title");

    Button btnNuevo = new Button("Nuevo Perfil", new Icon(VaadinIcon.PLUS));
    btnNuevo.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
    btnNuevo.addClickListener(e -> crearNuevoPerfil());

    headerLayout.add(title, btnNuevo);

    // Contenido simplificado (sin tabla)
    VerticalLayout content = new VerticalLayout();
    content.add(new H2("Vista simplificada sin tablas"));
    content.add(
        new com.vaadin.flow.component.html.Span(
            "Los filtros funcionan en memoria. Total de perfiles cargados: " + allPerfiles.size()));

    contentCard.add(headerLayout, content);

    return contentCard;
  }

  /** Aplica los filtros a los datos */
  private void aplicarFiltros() {
    filteredPerfiles =
        allPerfiles.stream().filter(this::cumpleFiltros).collect(Collectors.toList());

    // DataTable simplificado - solo muestra headers (sin actualización de datos)

    // Mostrar notificación
    Notification.show(String.format("Se encontraron %d perfiles", filteredPerfiles.size()))
        .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
  }

  /** Verifica si un perfil cumple con los filtros */
  private boolean cumpleFiltros(SisPerfilMantenimientoDTO perfil) {
    // Filtro por descripción
    if (!filtroDescripcion.isEmpty()
        && !perfil
            .getDescripcion()
            .toLowerCase()
            .contains(filtroDescripcion.getValue().toLowerCase())) {
      return false;
    }

    // Filtro por estado
    if (!filtroEstado.getValue().equals("Todos")
        && !perfil.getEstadoCodigo().equals(filtroEstado.getValue())) {
      return false;
    }

    return true;
  }

  /** Limpia todos los filtros */
  private void limpiarFiltros() {
    filtroDescripcion.clear();
    filtroEstado.setValue("Todos");

    // Mostrar todos los datos
    filteredPerfiles = new ArrayList<>(allPerfiles);
    // DataTable simplificado - solo muestra headers (sin actualización de datos)

    Notification.show("Filtros limpiados").addThemeVariants(NotificationVariant.LUMO_CONTRAST);
  }

  /** Crea un nuevo perfil */
  private void crearNuevoPerfil() {
    Notification.show("Función: Crear nuevo perfil")
        .addThemeVariants(NotificationVariant.LUMO_PRIMARY);
    // Aquí iría la lógica para abrir el formulario de nuevo perfil
  }

  /** Edita un perfil existente */
  private void editarPerfil(SisPerfilMantenimientoDTO perfil) {
    Notification.show("Editando perfil: " + perfil.getDescripcion())
        .addThemeVariants(NotificationVariant.LUMO_PRIMARY);
    // Aquí iría la lógica para editar el perfil seleccionado
  }

  /** Elimina un perfil */
  private void eliminarPerfil(SisPerfilMantenimientoDTO perfil) {
    allPerfiles.remove(perfil);
    filteredPerfiles.remove(perfil);

    // DataTable simplificado - solo muestra headers (sin actualización de datos)

    Notification.show("Perfil eliminado: " + perfil.getDescripcion())
        .addThemeVariants(NotificationVariant.LUMO_ERROR);
  }

  /** Carga datos de ejemplo */
  private void loadSampleData() {
    // Crear perfiles de ejemplo
    String[] descripciones = {
      "Administrador del Sistema",
      "Operador de Campañas",
      "Consultor de Reportes",
      "Supervisor de Operaciones",
      "Analista de Datos",
      "Gestor de Contenido",
      "Auditor Interno",
      "Soporte Técnico",
      "Coordinador de Procesos",
      "Especialista en Seguridad",
      "Desarrollador Backend",
      "Tester de Calidad",
      "Administrador de Base de Datos",
      "Consultor de Negocio",
      "Gerente de Proyecto"
    };

    for (int i = 0; i < descripciones.length; i++) {
      String estado = (i % 3 == 0) ? INACTIVO : ACTIVO; // Algunos inactivos para variedad
      String estadoDescripcion = ACTIVO.equals(estado) ? "Activo" : "Inactivo";

      SisPerfilMantenimientoDTO perfil =
          new SisPerfilMantenimientoDTO(
              (long) (i + 1), descripciones[i], estado, estadoDescripcion);

      allPerfiles.add(perfil);
    }

    // Inicializar datos filtrados
    filteredPerfiles = new ArrayList<>(allPerfiles);
  }
}
