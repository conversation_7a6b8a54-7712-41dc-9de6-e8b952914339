package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import pe.com.bbva.gifole.components.DataTable;
import pe.com.bbva.gifole.dto.GridColumn;
import pe.com.bbva.gifole.dto.SisPerfilMantenimientoDTO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Vista de ejemplo para el mantenimiento de perfiles del sistema
 * Demuestra el uso del componente DataTable con SisPerfilMantenimientoDTO
 */

@PageTitle("Mantenimiento de Perfiles del Sistema")
@Route(value = "sis-perfil-mantenimiento", layout = MainLayout.class)
public class SisPerfilMantenimientoView extends VerticalLayout {

    // Constantes
    private static final String ACTIVO = "ACTIVO";
    private static final String INACTIVO = "INACTIVO";

    // Datos
    private final List<SisPerfilMantenimientoDTO> allPerfiles = new ArrayList<>();
    private List<SisPerfilMantenimientoDTO> filteredPerfiles = new ArrayList<>();

    // Componentes principales
    private DataTable<SisPerfilMantenimientoDTO> dataTable;

    // Filtros
    private final TextField filtroDescripcion = new TextField();
    private final ComboBox<String> filtroEstado = new ComboBox<>();

    public SisPerfilMantenimientoView() {
        setSizeFull();
        addClassName("app-main"); // Usando clase global para background
        setPadding(true);
        setSpacing(true);

        // Crear layout principal
        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setSpacing(true);
        mainLayout.setPadding(false);

        // Panel de filtros
        HorizontalLayout filtersPanel = createFiltersPanel();

        // Card principal con DataTable
        VerticalLayout tableCard = createTableCard();

        mainLayout.add(filtersPanel, tableCard);
        add(mainLayout);

        // Cargar datos de ejemplo
        loadSampleData();
        
        // Actualizar DataTable con los datos cargados
        dataTable.setItems(filteredPerfiles);
    }

    /**
     * Crea el panel de filtros
     */
    private HorizontalLayout createFiltersPanel() {
        HorizontalLayout filtersPanel = new HorizontalLayout();
        filtersPanel.addClassName("bbva-filters-card");
        filtersPanel.setWidthFull();
        filtersPanel.setAlignItems(Alignment.END);
        filtersPanel.setSpacing(true);
        filtersPanel.setPadding(true);

        // Configurar filtros
        filtroDescripcion.setLabel("Descripción");
        filtroDescripcion.addClassName("bbva-input-floating");
        filtroDescripcion.setWidth("200px");
        filtroDescripcion.setPlaceholder("Buscar por descripción...");

        filtroEstado.setLabel("Estado");
        filtroEstado.addClassName("bbva-input-floating");
        filtroEstado.setWidth("150px");
        filtroEstado.setItems("Todos", ACTIVO, INACTIVO);
        filtroEstado.setValue("Todos");

        // Botones de acción usando clases globales
        Button btnBuscar = new Button("Buscar", new Icon(VaadinIcon.SEARCH));
        btnBuscar.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
        btnBuscar.addClassName("bbva-filters-button");
        btnBuscar.addClickListener(e -> aplicarFiltros());

        Button btnLimpiar = new Button("Limpiar", new Icon(VaadinIcon.REFRESH));
        btnLimpiar.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
        btnLimpiar.addClassName("bbva-filters-button");
        btnLimpiar.addClickListener(e -> limpiarFiltros());

        filtersPanel.add(filtroDescripcion, filtroEstado, btnBuscar, btnLimpiar);

        return filtersPanel;
    }

    /**
     * Crea la card principal con el DataTable
     */
    private VerticalLayout createTableCard() {
        VerticalLayout tableCard = new VerticalLayout();
        tableCard.setSizeFull();
        tableCard.addClassName("bbva-grid-card");
        tableCard.setSpacing(true);
        tableCard.setPadding(true);

        // Header con título y botón
        HorizontalLayout headerLayout = new HorizontalLayout();
        headerLayout.setWidthFull();
        headerLayout.setJustifyContentMode(FlexComponent.JustifyContentMode.BETWEEN);
        headerLayout.setAlignItems(FlexComponent.Alignment.CENTER);

        H2 title = new H2("Perfiles del Sistema");
        title.addClassName("bbva-grid-title");

        Button btnNuevo = new Button("Nuevo Perfil", new Icon(VaadinIcon.PLUS));
        btnNuevo.addThemeVariants(ButtonVariant.LUMO_PRIMARY);
        btnNuevo.addClickListener(e -> crearNuevoPerfil());

        headerLayout.add(title, btnNuevo);

        // Crear DataTable
        dataTable = createDataTable();

        tableCard.add(headerLayout, dataTable);
        tableCard.setFlexGrow(1, dataTable);

        return tableCard;
    }

    /**
     * Crea el DataTable con las columnas configuradas
     */
    private DataTable<SisPerfilMantenimientoDTO> createDataTable() {
        // Definir columnas
        List<GridColumn> columns = Arrays.asList(
                new GridColumn("id", "ID", "80px"),
                new GridColumn("descripcion", "Descripción", "300px"),
                new GridColumn("estadoCodigo", "Estado", "120px"),
                new GridColumn("estadoDescripcion", "Descripción Estado", "200px"));

        // Crear DataTable con 8 registros por página
        DataTable<SisPerfilMantenimientoDTO> table = new DataTable<>(columns, filteredPerfiles, 8);

        // Configurar selección en el grid interno
        table.getGrid().setSelectionMode(com.vaadin.flow.component.grid.Grid.SelectionMode.SINGLE);
        table.getGrid().addSelectionListener(event -> {
            event.getFirstSelectedItem().ifPresent(this::editarPerfil);
        });

        // Agregar columna de acciones
        table.getGrid().addComponentColumn(this::createActionButtons)
                .setHeader("Acciones")
                .setWidth("150px")
                .setFlexGrow(0);

        return table;
    }

    /**
     * Crea los botones de acción para cada fila
     */
    private HorizontalLayout createActionButtons(SisPerfilMantenimientoDTO perfil) {
        Button editButton = new Button(new Icon(VaadinIcon.EDIT));
        editButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY, ButtonVariant.LUMO_SMALL);
        editButton.addClassName("action-button");
        editButton.getElement().setAttribute("title", "Editar");
        editButton.addClickListener(e -> editarPerfil(perfil));

        Button toggleButton = new Button(new Icon(
                ACTIVO.equals(perfil.getEstadoCodigo()) ? VaadinIcon.EYE_SLASH : VaadinIcon.EYE));
        toggleButton.addThemeVariants(
                ButtonVariant.LUMO_TERTIARY,
                ButtonVariant.LUMO_SMALL,
                ACTIVO.equals(perfil.getEstadoCodigo()) ? ButtonVariant.LUMO_ERROR : ButtonVariant.LUMO_SUCCESS);
        toggleButton.addClassName("action-button");
        toggleButton.getElement().setAttribute("title",
                ACTIVO.equals(perfil.getEstadoCodigo()) ? "Desactivar" : "Activar");
        toggleButton.addClickListener(e -> toggleEstadoPerfil(perfil));

        Button deleteButton = new Button(new Icon(VaadinIcon.TRASH));
        deleteButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY, ButtonVariant.LUMO_ERROR, ButtonVariant.LUMO_SMALL);
        deleteButton.addClassName("action-button");
        deleteButton.getElement().setAttribute("title", "Eliminar");
        deleteButton.addClickListener(e -> eliminarPerfil(perfil));

        HorizontalLayout actions = new HorizontalLayout(editButton, toggleButton, deleteButton);
        actions.setSpacing(true);
        actions.addClassName("actions-column");
        return actions;
    }

    /**
     * Aplica los filtros a los datos
     */
    private void aplicarFiltros() {
        filteredPerfiles = allPerfiles.stream()
                .filter(this::cumpleFiltros)
                .collect(Collectors.toList());

        // Actualizar DataTable
        dataTable.setItems(filteredPerfiles);

        // Mostrar notificación
        Notification.show(String.format("Se encontraron %d perfiles", filteredPerfiles.size()))
                .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
    }

    /**
     * Verifica si un perfil cumple con los filtros
     */
    private boolean cumpleFiltros(SisPerfilMantenimientoDTO perfil) {
        // Filtro por descripción
        if (!filtroDescripcion.isEmpty() &&
                !perfil.getDescripcion().toLowerCase().contains(filtroDescripcion.getValue().toLowerCase())) {
            return false;
        }

        // Filtro por estado
        if (!filtroEstado.getValue().equals("Todos") &&
                !perfil.getEstadoCodigo().equals(filtroEstado.getValue())) {
            return false;
        }

        return true;
    }

    /**
     * Limpia todos los filtros
     */
    private void limpiarFiltros() {
        filtroDescripcion.clear();
        filtroEstado.setValue("Todos");

        // Mostrar todos los datos
        filteredPerfiles = new ArrayList<>(allPerfiles);
        dataTable.setItems(filteredPerfiles);

        Notification.show("Filtros limpiados")
                .addThemeVariants(NotificationVariant.LUMO_CONTRAST);
    }

    /**
     * Crea un nuevo perfil
     */
    private void crearNuevoPerfil() {
        Notification.show("Función: Crear nuevo perfil")
                .addThemeVariants(NotificationVariant.LUMO_PRIMARY);
        // Aquí iría la lógica para abrir el formulario de nuevo perfil
    }

    /**
     * Edita un perfil existente
     */
    private void editarPerfil(SisPerfilMantenimientoDTO perfil) {
        Notification.show("Editando perfil: " + perfil.getDescripcion())
                .addThemeVariants(NotificationVariant.LUMO_PRIMARY);
        // Aquí iría la lógica para editar el perfil seleccionado
    }

    /**
     * Cambia el estado de un perfil (Activo/Inactivo)
     */
    private void toggleEstadoPerfil(SisPerfilMantenimientoDTO perfil) {
        String nuevoEstado = ACTIVO.equals(perfil.getEstadoCodigo()) ? INACTIVO : ACTIVO;
        String nuevaDescripcion = ACTIVO.equals(nuevoEstado) ? "Activo" : "Inactivo";

        perfil.setEstadoCodigo(nuevoEstado);
        perfil.setEstadoDescripcion(nuevaDescripcion);

        // Refrescar la tabla
        dataTable.refresh();

        Notification.show(String.format("Perfil '%s' %s",
                perfil.getDescripcion(),
                ACTIVO.equals(nuevoEstado) ? "activado" : "desactivado"))
                .addThemeVariants(NotificationVariant.LUMO_SUCCESS);
    }

    /**
     * Elimina un perfil
     */
    private void eliminarPerfil(SisPerfilMantenimientoDTO perfil) {
        allPerfiles.remove(perfil);
        filteredPerfiles.remove(perfil);

        // Actualizar DataTable
        dataTable.setItems(filteredPerfiles);

        Notification.show("Perfil eliminado: " + perfil.getDescripcion())
                .addThemeVariants(NotificationVariant.LUMO_ERROR);
    }

    /**
     * Carga datos de ejemplo
     */
    private void loadSampleData() {
        // Crear perfiles de ejemplo
        String[] descripciones = {
                "Administrador del Sistema",
                "Operador de Campañas",
                "Consultor de Reportes",
                "Supervisor de Operaciones",
                "Analista de Datos",
                "Gestor de Contenido",
                "Auditor Interno",
                "Soporte Técnico",
                "Coordinador de Procesos",
                "Especialista en Seguridad",
                "Desarrollador Backend",
                "Tester de Calidad",
                "Administrador de Base de Datos",
                "Consultor de Negocio",
                "Gerente de Proyecto"
        };

        for (int i = 0; i < descripciones.length; i++) {
            String estado = (i % 3 == 0) ? INACTIVO : ACTIVO; // Algunos inactivos para variedad
            String estadoDescripcion = ACTIVO.equals(estado) ? "Activo" : "Inactivo";

            SisPerfilMantenimientoDTO perfil = new SisPerfilMantenimientoDTO(
                    (long) (i + 1),
                    descripciones[i],
                    estado,
                    estadoDescripcion);

            allPerfiles.add(perfil);
        }

        // Inicializar datos filtrados
        filteredPerfiles = new ArrayList<>(allPerfiles);
    }
}