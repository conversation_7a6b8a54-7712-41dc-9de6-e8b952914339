package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.Arrays;
import java.util.List;
import pe.com.bbva.gifole.domain.SisPerfil;
import pe.com.bbva.gifole.view.components.DataTable;

/**
 * Vista para el mantenimiento de perfiles del sistema
 *
 * <AUTHOR>
 * @version 1.0
 */
@PageTitle("Mantenimiento de Perfiles")
@Route(value = "sis-perfil-mantenimiento", layout = MainLayout.class)
public class SisPerfilMantenimientoView extends VerticalLayout {

  public SisPerfilMantenimientoView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Mantenimiento de Perfiles del Sistema");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Datos de ejemplo para SisPerfil
    List<SisPerfil> perfiles =
        Arrays.asList(
            createSamplePerfil(1L, "Administrador", "1"),
            createSamplePerfil(2L, "Usuario", "1"),
            createSamplePerfil(3L, "Supervisor", "1"),
            createSamplePerfil(4L, "Auditor", "0"),
            createSamplePerfil(5L, "Consultor", "1"),
            createSamplePerfil(6L, "Operador", "0"));

    // Construir el DataTable usando el Builder
    DataTable<SisPerfil> dataTable =
        DataTable.<SisPerfil>builder()
            .id("tabla-perfiles")
            .column(
                "descripcion",
                "Descripción",
                perfil -> trimToEmpty(perfil.getDescripcion()),
                "250px")
            .column(
                "estado",
                "Estado",
                perfil -> "1".equals(perfil.getEstado()) ? "Activo" : "Inactivo",
                "100px")
            .items(perfiles)
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }

  /** Crea un perfil de ejemplo */
  private SisPerfil createSamplePerfil(Long id, String descripcion, String estado) {
    SisPerfil perfil = new SisPerfil();
    perfil.setId(id);
    perfil.setDescripcion(descripcion);
    perfil.setEstado(estado);
    return perfil;
  }

  /** Método utilitario para trimear strings */
  private String trimToEmpty(String str) {
    return str == null ? "" : str.trim();
  }
}
