package pe.com.bbva.gifole.common.bean;

import java.util.Date;

public class TCCruceTablonBean {

  private Long idTablon;

  private Long idReferido;

  private Long idReferidor;

  private String codigoCentralReferidor;

  private String estadoHistorico;

  private Date fechaHistorico;

  private int cantidadPremio;

  private String nroDocumentoReferidor;

  private String tipoDocumentoReferidor;

  private String nroDocumentoReferido;

  private String nroContrato;

  private String nroTarjeta;

  private Date fechaActivacion;

  private Date fechaContratacion;

  public Long getIdTablon() {
    return idTablon;
  }

  public void setIdTablon(Long idTablon) {
    this.idTablon = idTablon;
  }

  public Long getIdReferido() {
    return idReferido;
  }

  public void setIdReferido(Long idReferido) {
    this.idReferido = idReferido;
  }

  public Long getIdReferidor() {
    return idReferidor;
  }

  public void setIdReferidor(Long idReferidor) {
    this.idReferidor = idReferidor;
  }

  public String getCodigoCentralReferidor() {
    return codigoCentralReferidor;
  }

  public void setCodigoCentralReferidor(String codigoCentralReferidor) {
    this.codigoCentralReferidor = codigoCentralReferidor;
  }

  public String getEstadoHistorico() {
    return estadoHistorico;
  }

  public void setEstadoHistorico(String estadoHistorico) {
    this.estadoHistorico = estadoHistorico;
  }

  public Date getFechaHistorico() {
    return fechaHistorico;
  }

  public void setFechaHistorico(Date fechaHistorico) {
    this.fechaHistorico = fechaHistorico;
  }

  public int getCantidadPremio() {
    return cantidadPremio;
  }

  public void setCantidadPremio(int cantidadPremio) {
    this.cantidadPremio = cantidadPremio;
  }

  public String getNroDocumentoReferidor() {
    return nroDocumentoReferidor;
  }

  public void setNroDocumentoReferidor(String nroDocumentoReferidor) {
    this.nroDocumentoReferidor = nroDocumentoReferidor;
  }

  public String getTipoDocumentoReferidor() {
    return tipoDocumentoReferidor;
  }

  public void setTipoDocumentoReferidor(String tipoDocumentoReferidor) {
    this.tipoDocumentoReferidor = tipoDocumentoReferidor;
  }

  public String getNroDocumentoReferido() {
    return nroDocumentoReferido;
  }

  public void setNroDocumentoReferido(String nroDocumentoReferido) {
    this.nroDocumentoReferido = nroDocumentoReferido;
  }

  public String getNroContrato() {
    return nroContrato;
  }

  public void setNroContrato(String nroContrato) {
    this.nroContrato = nroContrato;
  }

  public String getNroTarjeta() {
    return nroTarjeta;
  }

  public void setNroTarjeta(String nroTarjeta) {
    this.nroTarjeta = nroTarjeta;
  }

  public Date getFechaActivacion() {
    return fechaActivacion;
  }

  public void setFechaActivacion(Date fechaActivacion) {
    this.fechaActivacion = fechaActivacion;
  }

  public Date getFechaContratacion() {
    return fechaContratacion;
  }

  public void setFechaContratacion(Date fechaContratacion) {
    this.fechaContratacion = fechaContratacion;
  }
}
