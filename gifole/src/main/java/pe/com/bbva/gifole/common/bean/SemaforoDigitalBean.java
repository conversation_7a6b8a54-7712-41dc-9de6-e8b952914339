package pe.com.bbva.gifole.common.bean;

import java.io.Serializable;
import java.util.Date;

public class SemaforoDigitalBean implements Serializable {

  private static final long serialVersionUID = 4395374511960448215L;

  private String codigoCentral;
  private String nombreCompletoCliente;
  private String nombreCliente;
  private String apellidosCliente;
  private String tipoDocumento;
  private String nroDocumento;
  private Date fechaHoraRegistro;
  private Date fechaRegistro1;
  private Date fechaRegistro2;
  private String correo;
  private Date fechaHoraModificacion;
  private String canal;

  public String getCodigoCentral() {
    return codigoCentral;
  }

  public void setCodigoCentral(String codigoCentral) {
    this.codigoCentral = codigoCentral;
  }

  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public String getNroDocumento() {
    return nroDocumento;
  }

  public void setNroDocumento(String nroDocumento) {
    this.nroDocumento = nroDocumento;
  }

  public Date getFechaHoraRegistro() {
    return fechaHoraRegistro;
  }

  public void setFechaHoraRegistro(Date fechaHoraRegistro) {
    this.fechaHoraRegistro = fechaHoraRegistro;
  }

  public Date getFechaRegistro1() {
    return fechaRegistro1;
  }

  public void setFechaRegistro1(Date fechaRegistro1) {
    this.fechaRegistro1 = fechaRegistro1;
  }

  public Date getFechaRegistro2() {
    return fechaRegistro2;
  }

  public void setFechaRegistro2(Date fechaRegistro2) {
    this.fechaRegistro2 = fechaRegistro2;
  }

  public Date getFechaHoraModificacion() {
    return fechaHoraModificacion;
  }

  public void setFechaHoraModificacion(Date fechaHoraModificacion) {
    this.fechaHoraModificacion = fechaHoraModificacion;
  }

  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }

  public static long getSerialversionuid() {
    return serialVersionUID;
  }

  public String getNombreCompletoCliente() {
    return nombreCompletoCliente;
  }

  public void setNombreCompletoCliente(String nombreCompletoCliente) {
    this.nombreCompletoCliente = nombreCompletoCliente;
  }

  public String getNombreCliente() {
    return nombreCliente;
  }

  public void setNombreCliente(String nombreCliente) {
    this.nombreCliente = nombreCliente;
  }

  public String getApellidosCliente() {
    return apellidosCliente;
  }

  public void setApellidosCliente(String apellidosCliente) {
    this.apellidosCliente = apellidosCliente;
  }

  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }
}
