package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import pe.com.bbva.gifole.common.bean.PrestamoAlToqueBean;
import pe.com.bbva.gifole.util.Constante;
import pe.com.bbva.gifole.util.Utilitario;
import pe.com.bbva.gifole.view.components.DataTable;

@PageTitle("Consulta Lead Préstamo Al Toque")
@Route(value = "reporte/remarketing/consulta-lead-prestamo-al-toque", layout = MainLayout.class)
public class ConsultaLeadPrestamoAlToqueView extends VerticalLayout {

  public ConsultaLeadPrestamoAlToqueView() {
    setSizeFull();
    addClassName("app-main");
    setPadding(true);
    setSpacing(true);

    VerticalLayout mainLayout = new VerticalLayout();
    mainLayout.setSizeFull();
    mainLayout.setSpacing(true);
    mainLayout.setPadding(false);

    // Título
    H2 title = new H2("Consulta Lead Préstamo Al Toque");
    title.addClassName("bbva-grid-title");

    // DataTable
    VerticalLayout tableCard = createTableCard();

    mainLayout.add(title, tableCard);
    add(mainLayout);
  }

  private VerticalLayout createTableCard() {
    VerticalLayout card = new VerticalLayout();
    card.setSizeFull();
    card.addClassName("bbva-grid-card");
    card.setSpacing(true);
    card.setPadding(true);

    // Construir el DataTable usando el Builder
    DataTable<PrestamoAlToqueBean> dataTable =
        DataTable.<PrestamoAlToqueBean>builder()
            .id("tabla-prestamo-al-toque")
            .column(
                "codigoCentral",
                "Código central",
                bean -> StringUtils.trimToEmpty(bean.getCodigoCentral()),
                "100px")
            .column(
                "tipoDocumento",
                "Tipo de documento",
                bean -> StringUtils.trimToEmpty(bean.getTipoDocumento()),
                "130px")
            .column(
                "numeroDocumento",
                "Número de documento",
                bean -> StringUtils.trimToEmpty(bean.getNumeroDocumento()),
                "140px")
            .column(
                "nombreCompleto",
                "Nombre",
                bean -> StringUtils.trimToEmpty(bean.getNombreCompleto()),
                "220px")
            .column(
                "telefono",
                "Teléfono",
                bean -> StringUtils.trimToEmpty(bean.getTelefono()),
                "120px")
            .column("correo", "Correo", bean -> bean.getCorreo(), "200px")
            .column("monto", "Oferta", bean -> StringUtils.trimToEmpty(bean.getMonto()), "100px")
            .column(
                "marcaPh", "Marca PH", bean -> StringUtils.trimToEmpty(bean.getMarcaPh()), "100px")
            .column(
                "canal",
                "Canal",
                bean -> {
                  String canal = bean.getCanal();
                  if (canal == null) return Constante.CANAL.NOMBRE_BANCA_POR_INTERNET;
                  if (Constante.CANAL.BANCA_POR_INTERNET_UX.equals(canal))
                    return Constante.CANAL.NOMBRE_BANCA_POR_INTERNET;
                  return StringUtils.trimToEmpty(canal);
                },
                "200px")
            .column(
                "fechaRegistro",
                "Fecha lead",
                bean -> Utilitario.formatearFecha(bean.getFechaRegistro(), "dd/MM/yyyy HH:mm"),
                "200px")
            // Datos
            .items(new ArrayList<>()) // Inicialmente vacío
            .pageSize(10)
            .build();

    card.add(dataTable);
    return card;
  }
}
