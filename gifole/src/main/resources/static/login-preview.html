<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BBVA Login - Preview</title>
    <link rel="stylesheet" href="css/bbva-theme.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--bbva-navy);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-card {
            background: var(--bbva-white);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .logo-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .logo-bar {
            width: 8px;
            height: 24px;
            margin-right: 2px;
            border-radius: 1px;
        }
        
        .logo-bar:last-child {
            margin-right: 0;
        }
        
        .logo-bar.blue {
            background-color: var(--bbva-blue);
        }
        
        .logo-bar.light-blue {
            background-color: var(--bbva-light-blue);
        }
        
        .logo-bar.aqua {
            background-color: var(--bbva-aqua);
        }
        
        .login-title {
            color: var(--bbva-navy);
            font-size: 28px;
            font-weight: 600;
            margin: 24px 0 8px 0;
        }
        
        .login-subtitle {
            color: var(--bbva-navy);
            font-size: 14px;
            margin: 0 0 32px 0;
            opacity: 0.8;
        }
        
        .form-group {
            margin-bottom: 20px;
            position: relative;
        }
        
        .form-input {
            width: 100%;
            height: 48px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 0 16px 0 45px;
            font-size: 14px;
            background: var(--bbva-white);
            transition: border-color 0.2s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--bbva-blue);
            box-shadow: 0 0 0 2px rgba(0, 68, 129, 0.1);
        }
        
        .form-input::placeholder {
            color: #999;
        }
        
        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 16px;
        }
        
        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            cursor: pointer;
            font-size: 16px;
        }
        
        .login-button {
            width: 100%;
            height: 48px;
            background: var(--bbva-blue);
            color: var(--bbva-white);
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease;
            margin-top: 8px;
        }
        
        .login-button:hover {
            background: var(--bbva-navy);
        }
        
        .login-footer {
            margin-top: 32px;
            font-size: 12px;
            color: var(--bbva-navy);
            opacity: 0.7;
        }
        
        .footer-link {
            color: var(--bbva-blue);
            text-decoration: none;
        }
        
        .footer-link:hover {
            text-decoration: underline;
        }
        
        /* Icons using Unicode symbols */
        .icon-user::before {
            content: "👤";
        }
        
        .icon-lock::before {
            content: "🔒";
        }
        
        .icon-eye::before {
            content: "👁";
        }
    </style>
</head>
<body>
    <div class="login-card">
        <!-- Logo BBVA -->
        <div class="logo-container">
            <div class="logo-bar blue"></div>
            <div class="logo-bar light-blue"></div>
            <div class="logo-bar aqua"></div>
        </div>
        
        <!-- Header -->
        <h1 class="login-title">Bienvenido a BBVA</h1>
        <p class="login-subtitle">Inicia sesión para acceder a tu cuenta</p>
        
        <!-- Login Form -->
        <form id="loginForm">
            <div class="form-group">
                <span class="input-icon icon-user"></span>
                <input type="text" class="form-input" placeholder="Usuario o correo electrónico" id="username" required>
            </div>
            
            <div class="form-group">
                <span class="input-icon icon-lock"></span>
                <input type="password" class="form-input" placeholder="Contraseña" id="password" required>
                <span class="password-toggle icon-eye" onclick="togglePassword()"></span>
            </div>
            
            <button type="submit" class="login-button">Iniciar sesión</button>
        </form>
        
        <!-- Footer -->
        <div class="login-footer">
            <p>Protegido por BBVA. Consulta nuestra 
                <a href="#" class="footer-link">Política de privacidad</a> y 
                <a href="#" class="footer-link">Términos de servicio</a>
            </p>
        </div>
    </div>

    <script>
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.querySelector('.password-toggle');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.innerHTML = '🙈';
            } else {
                passwordField.type = 'password';
                toggleIcon.innerHTML = '👁';
            }
        }
        
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                alert('Por favor, completa todos los campos');
                return;
            }

            // Simulación de login exitoso - redirigir a página con header
            alert('¡Bienvenido! Login simulado exitoso');
            // Simular redirección a página con header
            window.location.href = 'home-preview.html';
        });
    </script>
</body>
</html>
