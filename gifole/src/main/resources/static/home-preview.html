<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BBVA Gifole - Inicio</title>
    <link rel="stylesheet" href="css/bbva-theme.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }
        
        /* Header con navegación horizontal moderna */
        .main-header {
            background: var(--bbva-navy);
            color: var(--bbva-white);
            padding: 0 24px;
            height: 64px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            border-bottom: 2px solid var(--bbva-blue);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
            min-width: 200px;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 2px;
            margin-right: 12px;
        }

        .logo-bar {
            width: 6px;
            height: 20px;
            border-radius: 1px;
        }

        .logo-bar.blue {
            background-color: var(--bbva-blue);
        }

        .logo-bar.light-blue {
            background-color: var(--bbva-light-blue);
        }

        .logo-bar.aqua {
            background-color: var(--bbva-aqua);
        }

        .app-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--bbva-white);
            margin: 0;
            white-space: nowrap;
        }

        .header-center {
            flex: 1;
            display: flex;
            justify-content: center;
        }

        .main-nav {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-button {
            background: transparent;
            color: var(--bbva-white);
            border: none;
            padding: 8px 16px;
            margin: 0 2px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .nav-button:hover {
            background: var(--bbva-blue);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .nav-button:focus {
            background: var(--bbva-blue);
            outline: 2px solid var(--bbva-aqua);
            outline-offset: 2px;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background: var(--bbva-white);
            min-width: 180px;
            border: 1px solid rgba(0, 68, 129, 0.2);
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            margin-top: 4px;
        }

        .dropdown-content.show {
            display: block;
        }

        .dropdown-item {
            display: block;
            color: var(--bbva-navy);
            padding: 10px 16px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.2s ease;
            border-bottom: 1px solid rgba(0, 68, 129, 0.1);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: rgba(0, 68, 129, 0.1);
            color: var(--bbva-blue);
            padding-left: 20px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
            min-width: 250px;
            justify-content: flex-end;
        }

        .user-info {
            color: var(--bbva-white);
            font-size: 14px;
            font-weight: 500;
        }

        .logout-btn {
            background: var(--bbva-blue);
            color: var(--bbva-white);
            border: 1px solid var(--bbva-light-blue);
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .logout-btn:hover {
            background: var(--bbva-light-blue);
            border-color: var(--bbva-aqua);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        /* Contenido principal */
        .main-content {
            padding: 24px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .welcome-card {
            background: var(--bbva-white);
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }
        
        .welcome-title {
            color: var(--bbva-navy);
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .welcome-subtitle {
            color: var(--bbva-light-blue);
            font-size: 18px;
            margin-bottom: 16px;
        }
        
        .welcome-description {
            color: var(--bbva-navy);
            line-height: 1.6;
        }
        
        /* Responsive design para navegación horizontal */
        @media (max-width: 768px) {
            .main-header {
                padding: 0 12px;
            }

            .header-left {
                min-width: 150px;
            }

            .app-title {
                font-size: 16px;
            }

            .header-right {
                min-width: 180px;
            }

            .user-info {
                display: none;
            }

            .nav-button {
                padding: 6px 12px;
                font-size: 13px;
            }

            .main-nav {
                gap: 2px;
            }
        }
    </style>
</head>
<body>
    <!-- Header principal con navegación horizontal -->
    <header class="main-header">
        <!-- Logo y título -->
        <div class="header-left">
            <div class="logo-container">
                <div class="logo-bar blue"></div>
                <div class="logo-bar light-blue"></div>
                <div class="logo-bar aqua"></div>
            </div>
            <h1 class="app-title">BBVA Gifole</h1>
        </div>

        <!-- Navegación principal -->
        <div class="header-center">
            <nav class="main-nav">
                <div class="nav-dropdown">
                    <button class="nav-button" onclick="toggleDropdown('config-dropdown')">
                        Configuración <span>▼</span>
                    </button>
                    <div id="config-dropdown" class="dropdown-content">
                        <a href="#" class="dropdown-item">Configuración General</a>
                        <a href="#" class="dropdown-item">Parámetros</a>
                        <a href="#" class="dropdown-item">Usuarios</a>
                    </div>
                </div>

                <div class="nav-dropdown">
                    <button class="nav-button" onclick="toggleDropdown('reports-dropdown')">
                        Reportes <span>▼</span>
                    </button>
                    <div id="reports-dropdown" class="dropdown-content">
                        <a href="#" class="dropdown-item">Reportes Generales</a>
                        <a href="#" class="dropdown-item">Estadísticas</a>
                        <a href="#" class="dropdown-item">Exportar</a>
                    </div>
                </div>

                <div class="nav-dropdown">
                    <button class="nav-button" onclick="toggleDropdown('cards-dropdown')">
                        Tarjetas <span>▼</span>
                    </button>
                    <div id="cards-dropdown" class="dropdown-content">
                        <a href="#" class="dropdown-item">Gestión de Tarjetas</a>
                        <a href="#" class="dropdown-item">Nuevas Tarjetas</a>
                        <a href="#" class="dropdown-item">Consultas</a>
                    </div>
                </div>

                <div class="nav-dropdown">
                    <button class="nav-button" onclick="toggleDropdown('accounts-dropdown')">
                        Cuentas <span>▼</span>
                    </button>
                    <div id="accounts-dropdown" class="dropdown-content">
                        <a href="#" class="dropdown-item">Cuentas Corrientes</a>
                        <a href="#" class="dropdown-item">Cuentas de Ahorro</a>
                        <a href="#" class="dropdown-item">Estados de Cuenta</a>
                    </div>
                </div>

                <div class="nav-dropdown">
                    <button class="nav-button" onclick="toggleDropdown('loans-dropdown')">
                        Préstamos <span>▼</span>
                    </button>
                    <div id="loans-dropdown" class="dropdown-content">
                        <a href="#" class="dropdown-item">Solicitudes</a>
                        <a href="#" class="dropdown-item">Aprobaciones</a>
                        <a href="#" class="dropdown-item">Seguimiento</a>
                    </div>
                </div>

                <div class="nav-dropdown">
                    <button class="nav-button" onclick="toggleDropdown('products-dropdown')">
                        Productos <span>▼</span>
                    </button>
                    <div id="products-dropdown" class="dropdown-content">
                        <a href="#" class="dropdown-item">Catálogo</a>
                        <a href="#" class="dropdown-item">Cancelaciones</a>
                        <a href="#" class="dropdown-item">Modificaciones</a>
                    </div>
                </div>

                <div class="nav-dropdown">
                    <button class="nav-button" onclick="toggleDropdown('maintenance-dropdown')">
                        Mantenimiento <span>▼</span>
                    </button>
                    <div id="maintenance-dropdown" class="dropdown-content">
                        <a href="#" class="dropdown-item">Sistema</a>
                        <a href="#" class="dropdown-item">Base de Datos</a>
                        <a href="#" class="dropdown-item">Logs</a>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Usuario y logout -->
        <div class="header-right">
            <span class="user-info">Bienvenido, Usuario</span>
            <button class="logout-btn" onclick="logout()">
                <span>🚪</span> Cerrar Sesión
            </button>
        </div>
    </header>



    <!-- Contenido principal -->
    <main class="main-content">
        <div class="welcome-card">
            <h1 class="welcome-title">Bienvenido a BBVA Gifole</h1>
            <h2 class="welcome-subtitle">Sistema de Gestión</h2>
            <p class="welcome-description">
                Esta es la página principal del sistema BBVA Gifole después del login exitoso.
                Como puedes ver, ahora aparece la barra superior con navegación horizontal moderna.
                <br><br>
                <strong>Nueva navegación horizontal empresarial:</strong>
                <br>• ✅ Barra superior con logo BBVA y título
                <br>• ✅ Navegación horizontal con menús desplegables
                <br>• ✅ Menús: Configuración, Reportes, Tarjetas, Cuentas, Préstamos, Productos, Mantenimiento
                <br>• ✅ Sección de usuario y botón de cerrar sesión
                <br>• ✅ Diseño moderno y empresarial con paleta BBVA
                <br>• ✅ Efectos hover y transiciones suaves
                <br>• ✅ Responsive design para diferentes pantallas
            </p>
        </div>
    </main>

    <script>
        function toggleDropdown(dropdownId) {
            // Cerrar todos los dropdowns abiertos
            const allDropdowns = document.querySelectorAll('.dropdown-content');
            allDropdowns.forEach(dropdown => {
                if (dropdown.id !== dropdownId) {
                    dropdown.classList.remove('show');
                }
            });

            // Toggle el dropdown seleccionado
            const dropdown = document.getElementById(dropdownId);
            dropdown.classList.toggle('show');
        }

        // Cerrar dropdowns al hacer click fuera
        document.addEventListener('click', function(event) {
            if (!event.target.matches('.nav-button')) {
                const dropdowns = document.querySelectorAll('.dropdown-content');
                dropdowns.forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });

        function logout() {
            if (confirm('¿Estás seguro de que quieres cerrar sesión?')) {
                // Redirigir de vuelta al login
                window.location.href = 'login-preview.html';
            }
        }

        // Manejar navegación con teclado
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const dropdowns = document.querySelectorAll('.dropdown-content');
                dropdowns.forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });
    </script>
</body>
</html>
