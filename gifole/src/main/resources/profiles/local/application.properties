server.servlet.context-path=/gifole-web-ui
# Prefijo API global
api.base-path=/api/v1

# Configuracion específica para ambiente LOCAL
server.port=8080
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

spring.application.name=gifole-backend
spring.datasource.jndi-name=java:jboss/datasources/GifoleDS
spring.jpa.hibernate.ddl-auto=update

# Configuracion de schema por defecto
spring.jpa.properties.hibernate.default_schema=GIFOLE

# Configuracion de Swagger/OpenAPI
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.packages-to-scan=pe.com.bbva.gifole.controller

vaadin.productionMode=true