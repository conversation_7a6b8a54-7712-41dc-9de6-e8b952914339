# Archivo de configuracion valores.properties para GIFOLE
# Este archivo contiene las configuraciones necesarias para el funcionamiento de la aplicacion

# Modo de perfil de la aplicacion
modo.perfil=desarrollo

# Configuracion de servicios web GIFOLE
pass.gifole.ws=password123
ruta.gifole.ws=http://localhost:8080/gifole-ws

# Configuracion de servicios de incentivacion
ruta.gifole.ws.incentivacion.toc=http://localhost:8080/gifole-ws/incentivacion/toc
ruta.gifole.ws.incentivacion.ta=http://localhost:8080/gifole-ws/incentivacion/ta

# Servicios REST GIFOLE
servicios.rest.gifole.ws.tarjeta.hincha=http://localhost:8080/gifole-ws/tarjeta/hincha
servicios.rest.gifole.ws.gestor.campania=http://localhost:8080/gifole-ws/gestor/campania
servicios.rest.gifole.ws.seg.vida.renta=http://localhost:8080/gifole-ws/seguro/vida/renta
servicios.rest.gifole.ws.campania.multiplica=http://localhost:8080/gifole-ws/campania/multiplica
servicios.rest.gifole.ws.banner.cross.sell=http://localhost:8080/gifole-ws/banner/cross/sell
servicios.rest.gifole.ws.consultar.prestamo.vehicular=http://localhost:8080/gifole-ws/prestamo/vehicular

# Cancelacion de productos
cancelacion.productos.buzon.envio.mail=<EMAIL>
servicios.rest.gifole.ws.consultar.cancelacion.productos=http://localhost:8080/gifole-ws/cancelacion/productos
servicios.rest.gifole.ws.actualizar.cancelacion.productos=http://localhost:8080/gifole-ws/cancelacion/productos/actualizar
servicios.rest.gifole.ws.motivos.rechazo=http://localhost:8080/gifole-ws/motivos/rechazo

# Cambio Modalidad EECC
cambio.modalidad.eecc.buzon.envio.mail=<EMAIL>
servicios.rest.gifole.ws.consultar.cambio.modalidad.eecc=http://localhost:8080/gifole-ws/cambio/modalidad/eecc
servicios.rest.gifole.ws.actualizar.cambio.modalidad.eecc=http://localhost:8080/gifole-ws/cambio/modalidad/eecc/actualizar
servicios.rest.gifole.ws.motivos.aprobacion.cambio.modalidad.ecc=http://localhost:8080/gifole-ws/motivos/aprobacion/cambio/modalidad

# Semaforo Digital
servicios.rest.gifole.ws.consultar.semaforo.digital=http://localhost:8080/gifole-ws/semaforo/digital

# Raspa y Gana
servicios.rest.gifole.ws.consultar.raspa.y.gana=http://localhost:8080/gifole-ws/raspa/gana
servicios.rest.gifole.ws.consultar.totales.raspa.y.gana=http://localhost:8080/gifole-ws/raspa/gana/totales

# Repositorio de archivos
repositorio.url=http://localhost:8080/repositorio

# Seguros Royal
servicios.rest.gifole.ws.consultar.seguro.vehicular.royal=http://localhost:8080/gifole-ws/seguro/vehicular/royal
servicios.rest.gifole.ws.consultar.venta.seguro.vehicular.royal=http://localhost:8080/gifole-ws/seguro/vehicular/royal/venta
servicios.rest.gifole.ws.consultar.seguro.hogar.royal=http://localhost:8080/gifole-ws/seguro/hogar/royal
servicios.rest.gifole.ws.consultar.seguro.negocioatumedida.royal=http://localhost:8080/gifole-ws/seguro/negocio/royal
servicios.rest.gifole.ws.consultar.seguro.vida.royal=http://localhost:8080/gifole-ws/seguro/vida/royal
servicios.rest.gifole.ws.consultar.seguro.producto=http://localhost:8080/gifole-ws/seguro/producto
servicios.rest.gifole.ws.consultar.seguro.royal=http://localhost:8080/gifole-ws/seguro/royal
servicios.rest.gifole.ws.consultar.seguro.vehicular.royal.openpay=http://localhost:8080/gifole-ws/seguro/vehicular/royal/openpay

# Tarjetas
servicios.rest.gifole.ws.consultar.tarjeta.garantizada=http://localhost:8080/gifole-ws/tarjeta/garantizada
servicios.rest.gifole.ws.consultar.tarjeta.upgrade=http://localhost:8080/gifole-ws/tarjeta/upgrade
servicios.rest.gifole.ws.registrar.beneficios.upgrade=http://localhost:8080/gifole-ws/tarjeta/upgrade/beneficios/registrar
servicios.rest.gifole.ws.consultar.beneficios.upgrade=http://localhost:8080/gifole-ws/tarjeta/upgrade/beneficios
servicios.rest.gifole.ws.actualizar.beneficios.upgrade=http://localhost:8080/gifole-ws/tarjeta/upgrade/beneficios/actualizar
servicios.rest.gifole.ws.eliminar.beneficios.upgrade=http://localhost:8080/gifole-ws/tarjeta/upgrade/beneficios/eliminar

# Mercurio
servicios.rest.gifole.ws.consultar.mercurio=http://localhost:8080/gifole-ws/mercurio

# Configuracion LDAP
ldap.global.url=ldap://localhost:389
ldap.global.rama.roles=ou=roles,dc=bbva,dc=com
ldap.global.rama.usuarios=ou=usuarios,dc=bbva,dc=com