<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
	<Properties>
		<Property name="logDir">/opt/apps/gifole/logs/</Property>
	</Properties>
	<Appenders>
		<File name="FileAppender" fileName="${logDir}gifole-backend.log">
			<PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} [%t] %-5p %c{1} - %m%n" />
		</File>

		<File name="ErrorFileAppender" fileName="${logDir}gifole-backend-ERROR.log">
			<PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} [%t] %-5p %c{1} - %m%n" />
			<ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
		</File>

		<Console name="Console" target="SYSTEM_OUT">
			<PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} [GIFOLE-BACKEND] [%t] %-5p %c{1} - %m%n" />
		</Console>
	</Appenders>
	<Loggers>
		<Logger name="pe.com.bbva.gifole" level="DEBUG" additivity="false">
			<AppenderRef ref="FileAppender" />
			<AppenderRef ref="ErrorFileAppender" />
			<AppenderRef ref="Console" />
		</Logger>

		<Logger name="org.springframework" level="INFO" additivity="false">
			<AppenderRef ref="FileAppender" />
			<AppenderRef ref="Console" />
		</Logger>

		<Logger name="org.hibernate" level="INFO" additivity="false">
			<AppenderRef ref="FileAppender" />
			<AppenderRef ref="Console" />
		</Logger>

		<Root level="INFO">
			<AppenderRef ref="FileAppender" />
			<AppenderRef ref="ErrorFileAppender" />
			<AppenderRef ref="Console" />
		</Root>
	</Loggers>
</Configuration>
