/* ===== BBVA THEME - VARIABLES Y CONFIGURACIÓN BASE ===== */

/* Paleta de colores oficial BBVA */
:root {
  /* Colores principales BBVA */
  --bbva-navy: #072146;        /* Dark blue from BBVA logo */
  --bbva-blue: #004481;        /* Primary blue from BBVA */
  --bbva-light-blue: #1973B8;  /* Secondary blue */
  --bbva-aqua: #02A5A5;        /* Teal/aqua accent */
  --bbva-white: #FFFFFF;       /* White */
  --bbva-gold: #F8CD51;        /* Gold accent from the star icon */
  
  /* Colores adicionales */
  --bbva-gray-50: #F8F9FA;
  --bbva-gray-100: #F5F5F5;
  --bbva-gray-200: #E0E0E0;
  --bbva-gray-300: #D0D0D0;
  --bbva-gray-400: #BDBDBD;
  --bbva-gray-500: #666666;
  --bbva-gray-600: #555555;
  --bbva-gray-700: #333333;
  --bbva-gray-800: #222222;
  --bbva-gray-900: #111111;
  
  /* Estados */
  --bbva-success: #22C55E;
  --bbva-warning: #F59E0B;
  --bbva-error: #EF4444;
  --bbva-info: #3B82F6;
  
  /* Espaciado */
  --bbva-spacing-xs: 4px;
  --bbva-spacing-sm: 8px;
  --bbva-spacing-md: 16px;
  --bbva-spacing-lg: 24px;
  --bbva-spacing-xl: 32px;
  --bbva-spacing-2xl: 48px;
  
  /* Bordes */
  --bbva-border-radius: 4px;
  --bbva-border-radius-sm: 2px;
  --bbva-border-radius-lg: 8px;
  --bbva-border-radius-xl: 12px;
  --bbva-border-color: #E0E0E0;
  
  /* Sombras */
  --bbva-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --bbva-shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --bbva-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
  
  /* Tipografía */
  --bbva-font-size-xs: 0.75rem;
  --bbva-font-size-sm: 0.875rem;
  --bbva-font-size-base: 1rem;
  --bbva-font-size-lg: 1.125rem;
  --bbva-font-size-xl: 1.25rem;
  --bbva-font-size-2xl: 1.5rem;
  --bbva-font-size-3xl: 1.875rem;
  
  /* Transiciones */
  --bbva-transition: 0.2s ease;
  --bbva-transition-fast: 0.1s ease;
  --bbva-transition-slow: 0.3s ease;
  
  /* ===== VAADIN THEME OVERRIDES ===== */
  --lumo-primary-color: var(--bbva-blue);
  --lumo-primary-color-50pct: rgba(0, 68, 129, 0.5);
  --lumo-primary-color-10pct: rgba(0, 68, 129, 0.1);
  --lumo-primary-text-color: var(--bbva-blue);
  --lumo-primary-contrast-color: var(--bbva-white);
  
  --lumo-secondary-text-color: var(--bbva-navy);
  --lumo-tertiary-text-color: var(--bbva-light-blue);
  
  --lumo-success-color: var(--bbva-success);
  --lumo-warning-color: var(--bbva-warning);
  --lumo-error-color: var(--bbva-error);
  
  --lumo-header-text-color: var(--bbva-navy);
  --lumo-body-text-color: var(--bbva-navy);
  
  /* Text field size override */
  --lumo-text-field-size: 2.25rem;
}

/* Text colors */
.bbva-text-primary {
  color: var(--bbva-blue) !important;
}

.bbva-text-secondary {
  color: var(--bbva-light-blue) !important;
}

.bbva-text-navy {
  color: var(--bbva-navy) !important;
}

.bbva-text-aqua {
  color: var(--bbva-aqua) !important;
}

.bbva-text-gold {
  color: var(--bbva-gold) !important;
}

/* Button styles */
.bbva-button-primary {
  background-color: var(--bbva-blue);
  color: var(--bbva-white);
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.bbva-button-primary:hover {
  background-color: var(--bbva-navy);
}

.bbva-button-secondary {
  background-color: var(--bbva-light-blue);
  color: var(--bbva-white);
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.bbva-button-secondary:hover {
  background-color: var(--bbva-blue);
}

/* Header styles */
.bbva-header {
  background-color: var(--bbva-navy);
  color: var(--bbva-white);
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Mantener color blanco solo en el header/navbar */
.bbva-header h1,
.bbva-header h2,
.bbva-header h3,
.bbva-header h4,
.bbva-header h5,
.bbva-header h6,
vaadin-app-layout::part(navbar) h1,
vaadin-app-layout::part(navbar) h2,
vaadin-app-layout::part(navbar) h3,
vaadin-app-layout::part(navbar) h4,
vaadin-app-layout::part(navbar) h5,
vaadin-app-layout::part(navbar) h6 {
  color: var(--bbva-white) !important;
}

/* Card styles */
.bbva-card {
  background-color: var(--bbva-white);
  border: 1px solid rgba(0, 68, 129, 0.1);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Form styles */
.bbva-form-field {
  margin-bottom: 16px;
}

.bbva-form-field label {
  color: var(--bbva-navy);
  font-weight: 500;
  margin-bottom: 4px;
  display: block;
}

/* Login View Styles - Matching the BBVA image design */
.bbva-login-container {
  background: var(--bbva-navy);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.bbva-login-card {
  background: var(--bbva-white);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

/* BBVA Logo simulation */
.bbva-logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
}

.bbva-logo-bar {
  width: 8px;
  height: 24px;
  margin-right: 2px;
  border-radius: 1px;
}

.bbva-logo-bar:last-child {
  margin-right: 0;
}

/* Login form field styling */
vaadin-text-field::part(input-field),
vaadin-password-field::part(input-field) {
  border: 2px solid #e0e0e0 !important;
  border-radius: 8px !important;
  background: var(--bbva-white) !important;
  height: 48px !important;
  transition: border-color 0.2s ease !important;
}

vaadin-text-field:focus-within::part(input-field),
vaadin-password-field:focus-within::part(input-field) {
  border-color: var(--bbva-blue) !important;
  box-shadow: 0 0 0 2px rgba(0, 68, 129, 0.1) !important;
}

/* Login button styling */
.bbva-login-button {
  width: 100%;
  height: 48px;
  background: var(--bbva-blue);
  color: var(--bbva-white);
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 8px;
}

.bbva-login-button:hover {
  background: var(--bbva-navy);
}

/* Footer styling */
.bbva-login-footer {
  margin-top: 32px;
  font-size: 12px;
  color: var(--bbva-navy);
  opacity: 0.7;
  text-align: center;
}

.bbva-login-footer a {
  color: var(--bbva-blue);
  text-decoration: none;
  font-size: 12px;
}

.bbva-login-footer a:hover {
  text-decoration: underline;
}

/* Notification styling for BBVA theme */
vaadin-notification-card {
  background: var(--bbva-white);
  color: var(--bbva-navy);
  border-left: 4px solid var(--bbva-blue);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* ===== NAVEGACIÓN HORIZONTAL SUPERIOR MODERNA ===== */

/* Header principal moderno */
.bbva-modern-header,
.bbva-main-header {
  background: linear-gradient(135deg, var(--bbva-navy) 0%, #0a2a5a 100%) !important;
  color: var(--bbva-white) !important;
  padding: 0 32px !important;
  height: 72px !important;
  box-shadow: 0 4px 20px rgba(7, 33, 70, 0.3) !important;
  border-bottom: 3px solid var(--bbva-blue) !important;
  position: relative !important;
  overflow: hidden !important;
}

.bbva-modern-header::before,
.bbva-main-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(25, 115, 184, 0.1) 25%, 
    rgba(2, 165, 165, 0.1) 50%, 
    rgba(25, 115, 184, 0.1) 75%, 
    transparent 100%);
  pointer-events: none;
}

/* Main layout styling - Force navbar background */
.bbva-modern-layout,
.bbva-main-layout {
  --vaadin-app-layout-navbar-background: var(--bbva-navy) !important;
  --vaadin-app-layout-navbar-text-color: var(--bbva-white) !important;
}

/* Override Vaadin AppLayout navbar specifically */
vaadin-app-layout::part(navbar) {
  background: linear-gradient(135deg, var(--bbva-navy) 0%, #0a2a5a 100%) !important;
  color: var(--bbva-white) !important;
  box-shadow: 0 4px 20px rgba(7, 33, 70, 0.3) !important;
  border-bottom: 3px solid var(--bbva-blue) !important;
}

/* Force AppLayout navbar background */
vaadin-app-layout,
vaadin-app-layout[theme~="bbva"] {
  --vaadin-app-layout-navbar-background: var(--bbva-navy) !important;
  --vaadin-app-layout-navbar-text-color: var(--bbva-white) !important;
}

/* Sección del logo moderna */
.bbva-header-left,
.bbva-logo-section {
  padding: 0 24px 0 0 !important;
  min-width: 280px !important;
  z-index: 2 !important;
  position: relative !important;
}

.bbva-modern-logo,
.bbva-header-logo {
  display: flex !important;
  align-items: center !important;
  margin-right: 16px !important;
  cursor: pointer !important;
  transition: transform 0.3s ease !important;
}

.bbva-modern-logo:hover,
.bbva-header-logo:hover {
  transform: scale(1.05) !important;
}

.bbva-logo-bar,
.bbva-header-logo-bar {
  width: 8px !important;
  height: 32px !important;
  margin-right: 3px !important;
  border-radius: 2px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.bbva-logo-bar-1 {
  background: linear-gradient(135deg, var(--bbva-blue), var(--bbva-light-blue)) !important;
  animation: logoBar1 2s ease-in-out infinite alternate !important;
}

.bbva-logo-bar-2 {
  background: linear-gradient(135deg, var(--bbva-light-blue), var(--bbva-aqua)) !important;
  animation: logoBar2 2s ease-in-out infinite alternate 0.3s !important;
}

.bbva-logo-bar-3 {
  background: linear-gradient(135deg, var(--bbva-aqua), var(--bbva-gold)) !important;
  animation: logoBar3 2s ease-in-out infinite alternate 0.6s !important;
}

@keyframes logoBar1 {
  0% { height: 32px; }
  100% { height: 28px; }
}

@keyframes logoBar2 {
  0% { height: 32px; }
  100% { height: 36px; }
}

@keyframes logoBar3 {
  0% { height: 32px; }
  100% { height: 30px; }
}

.bbva-modern-title,
.bbva-app-title {
  font-size: 24px !important;
  font-weight: 700 !important;
  color: var(--bbva-white) !important;
  margin: 0 !important;
  letter-spacing: -0.5px !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  background: linear-gradient(135deg, var(--bbva-white), var(--bbva-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  white-space: nowrap !important;
}

/* Sección de navegación moderna */
.bbva-header-center,
.bbva-navigation-section {
  flex: 1 !important;
  justify-content: center !important;
  z-index: 2 !important;
  position: relative !important;
}

.bbva-nav-container {
  justify-content: center !important;
  gap: 8px !important;
}

.bbva-main-menubar {
  background: transparent !important;
  border: none !important;
}

.bbva-nav-button,
.bbva-menu-item {
  color: var(--bbva-white) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px !important;
  padding: 12px 20px !important;
  margin: 0 4px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  letter-spacing: 0.5px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  backdrop-filter: blur(10px) !important;
  min-width: 120px !important;
  cursor: pointer !important;
}

.bbva-nav-button::before,
.bbva-menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.bbva-nav-button:hover::before,
.bbva-menu-item:hover::before {
  left: 100%;
}

.bbva-nav-button:hover,
.bbva-menu-item:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: var(--bbva-aqua) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
  color: var(--bbva-white) !important;
}

.bbva-nav-button:focus,
.bbva-menu-item:focus {
  outline: 2px solid var(--bbva-gold) !important;
  outline-offset: 2px !important;
}

.bbva-nav-button-active {
  background: linear-gradient(135deg, var(--bbva-blue), var(--bbva-light-blue)) !important;
  border-color: var(--bbva-aqua) !important;
  box-shadow: 0 4px 15px rgba(0, 68, 129, 0.4) !important;
  transform: translateY(-1px) !important;
}

/* Submenús */
.bbva-submenu-item {
  color: var(--bbva-navy) !important;
  background: var(--bbva-white) !important;
  padding: 8px 16px !important;
  border-bottom: 1px solid rgba(0, 68, 129, 0.1) !important;
  transition: all 0.2s ease !important;
}

.bbva-submenu-item:hover {
  background: rgba(0, 68, 129, 0.1) !important;
  color: var(--bbva-blue) !important;
  padding-left: 20px !important;
}

/* Sección de usuario */
.bbva-user-section {
  padding: 0 0 0 16px !important;
  min-width: 250px !important;
  justify-content: flex-end !important;
}

.bbva-user-info {
  color: var(--bbva-white) !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  margin-right: 16px !important;
}

.bbva-logout-button {
  color: var(--bbva-white) !important;
  background: var(--bbva-blue) !important;
  border: 1px solid var(--bbva-light-blue) !important;
  padding: 6px 12px !important;
  border-radius: 4px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.bbva-logout-button:hover {
  background: var(--bbva-light-blue) !important;
  border-color: var(--bbva-aqua) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Estilos específicos para Vaadin MenuBar */
vaadin-menu-bar {
  background: transparent !important;
}

vaadin-menu-bar-button {
  color: var(--bbva-white) !important;
  background: transparent !important;
  border: none !important;
  padding: 8px 16px !important;
  margin: 0 2px !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

vaadin-menu-bar-button:hover {
  background: var(--bbva-blue) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

vaadin-menu-bar-button[focus-ring] {
  background: var(--bbva-blue) !important;
  outline: 2px solid var(--bbva-aqua) !important;
  outline-offset: 2px !important;
}

/* Overlay de submenús */
vaadin-context-menu-overlay {
  background: var(--bbva-white) !important;
  border: 1px solid rgba(0, 68, 129, 0.2) !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  margin-top: 4px !important;
}

vaadin-context-menu-item {
  color: var(--bbva-navy) !important;
  padding: 10px 16px !important;
  font-size: 14px !important;
  transition: all 0.2s ease !important;
}

vaadin-context-menu-item:hover {
  background: rgba(0, 68, 129, 0.1) !important;
  color: var(--bbva-blue) !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .bbva-main-header {
    padding: 0 12px !important;
  }

  .bbva-logo-section {
    min-width: 150px !important;
  }

  .bbva-app-title {
    font-size: 16px !important;
  }

  .bbva-user-section {
    min-width: 180px !important;
  }

  .bbva-user-info {
    display: none !important;
  }

  .bbva-menu-item {
    padding: 6px 12px !important;
    font-size: 13px !important;
  }
}