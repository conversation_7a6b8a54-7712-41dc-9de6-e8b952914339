/* ===== FORM CARD COMPONENT - ESTILOS PROFESIONALES BBVA ===== */

/* 
 * Estilos profesionales para FormCard con identidad visual BBVA
 * Diseño moderno, limpio y funcional para formularios
 */

/* ===== CONTENEDOR PRINCIPAL ===== */

/* Contenedor del FormCard */
.bbva-form-card {
  background-color: var(--bbva-white);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 68, 129, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 68, 129, 0.1);
  padding: 24px;
  transition: all 0.3s ease;
  font-family: 'Helvetica Neue', 'Segoe UI', Arial, sans-serif;
}

.bbva-form-card:hover {
  box-shadow: 0 8px 30px rgba(0, 68, 129, 0.12), 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* ===== TÍTULO DE LA TARJETA ===== */

.form-card-title {
  color: var(--bbva-navy, #072146);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 24px 0;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(0, 68, 129, 0.1);
  position: relative;
}

.form-card-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, var(--bbva-blue) 0%, var(--bbva-aqua) 100%);
  border-radius: 1px;
}

/* ===== CONTENEDOR DE INPUTS ===== */

.form-inputs-container {
  width: 100%;
}

/* Filas de inputs */
.form-input-row {
  margin-bottom: 20px;
  gap: 20px;
}

.form-input-row:last-child {
  margin-bottom: 0;
}

/* Wrapper de cada input */
.form-input-wrapper {
  flex: 1;
  min-width: 0; /* Permite que el flex funcione correctamente */
}

/* ===== ESTILOS DE INPUTS ===== */

/* Estilos base para todos los inputs */
.form-input {
  width: 100% !important;
  font-family: 'Helvetica Neue', 'Segoe UI', Arial, sans-serif;
}

/* TextField personalizado */
.form-input vaadin-text-field {
  --lumo-primary-color: var(--bbva-blue);
  --lumo-primary-text-color: var(--bbva-navy);
  --lumo-contrast-10pct: rgba(0, 68, 129, 0.1);
  --lumo-contrast-20pct: rgba(0, 68, 129, 0.2);
}

.form-input vaadin-text-field::part(input-field) {
  background-color: var(--bbva-white);
  border: 2px solid rgba(0, 68, 129, 0.15);
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.form-input vaadin-text-field::part(input-field):hover {
  border-color: rgba(0, 68, 129, 0.3);
  box-shadow: 0 2px 8px rgba(0, 68, 129, 0.1);
}

.form-input vaadin-text-field[focused]::part(input-field) {
  border-color: var(--bbva-blue);
  box-shadow: 0 0 0 2px rgba(0, 68, 129, 0.2);
}

/* NumberField personalizado */
.form-input vaadin-number-field {
  --lumo-primary-color: var(--bbva-blue);
  --lumo-primary-text-color: var(--bbva-navy);
}

.form-input vaadin-number-field::part(input-field) {
  background-color: var(--bbva-white);
  border: 2px solid rgba(0, 68, 129, 0.15);
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.form-input vaadin-number-field::part(input-field):hover {
  border-color: rgba(0, 68, 129, 0.3);
  box-shadow: 0 2px 8px rgba(0, 68, 129, 0.1);
}

.form-input vaadin-number-field[focused]::part(input-field) {
  border-color: var(--bbva-blue);
  box-shadow: 0 0 0 2px rgba(0, 68, 129, 0.2);
}

/* DatePicker personalizado */
.form-input vaadin-date-picker {
  --lumo-primary-color: var(--bbva-blue);
  --lumo-primary-text-color: var(--bbva-navy);
}

.form-input vaadin-date-picker::part(input-field) {
  background-color: var(--bbva-white);
  border: 2px solid rgba(0, 68, 129, 0.15);
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.form-input vaadin-date-picker::part(input-field):hover {
  border-color: rgba(0, 68, 129, 0.3);
  box-shadow: 0 2px 8px rgba(0, 68, 129, 0.1);
}

.form-input vaadin-date-picker[focused]::part(input-field) {
  border-color: var(--bbva-blue);
  box-shadow: 0 0 0 2px rgba(0, 68, 129, 0.2);
}

/* ===== LABELS ===== */

.form-input vaadin-text-field::part(label),
.form-input vaadin-number-field::part(label),
.form-input vaadin-date-picker::part(label) {
  color: var(--bbva-navy, #072146);
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 4px;
}

/* ===== CAMPOS REQUERIDOS ===== */

.form-input vaadin-text-field[required]::part(required-indicator),
.form-input vaadin-number-field[required]::part(required-indicator),
.form-input vaadin-date-picker[required]::part(required-indicator) {
  color: #dc3545;
}

/* ===== ESTADOS DE ERROR ===== */

.form-input vaadin-text-field[invalid]::part(input-field),
.form-input vaadin-number-field[invalid]::part(input-field),
.form-input vaadin-date-picker[invalid]::part(input-field) {
  border-color: #dc3545;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .bbva-form-card {
    padding: 16px;
    border-radius: 8px;
  }
  
  .form-card-title {
    font-size: 1.25rem;
    margin-bottom: 16px;
  }
  
  .form-input-row {
    flex-direction: column;
    gap: 16px;
    margin-bottom: 16px;
  }
  
  .form-input-wrapper {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .bbva-form-card {
    padding: 12px;
  }
  
  .form-card-title {
    font-size: 1.125rem;
    margin-bottom: 12px;
  }
  
  .form-input-row {
    gap: 12px;
    margin-bottom: 12px;
  }
}

/* ===== ANIMACIONES ===== */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bbva-form-card {
  animation: fadeInUp 0.5s ease-out;
}

/* ===== ACCESIBILIDAD ===== */

.form-input vaadin-text-field:focus-within,
.form-input vaadin-number-field:focus-within,
.form-input vaadin-date-picker:focus-within {
  outline: 2px solid var(--bbva-blue);
  outline-offset: 2px;
}