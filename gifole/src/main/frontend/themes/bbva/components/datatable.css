/* ===== DATATABLE COMPONENT - ESTILOS PROFESIONALES BBVA ===== */

/* 
 * Estilos profesionales para DataTable con identidad visual BBVA
 * Diseño moderno, limpio y funcional
 */

/* ===== CONTENEDOR PRINCIPAL ===== */

/* Contenedor del DataTable */
.bbva-custom-datatable {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 68, 129, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: var(--bbva-white);
  font-family: 'Helvetica Neue', 'Segoe UI', Arial, sans-serif;
  border: 1px solid rgba(0, 68, 129, 0.1);
  transition: all 0.3s ease;
}

.bbva-custom-datatable:hover {
  box-shadow: 0 8px 30px rgba(0, 68, 129, 0.12), 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Contenedor de tabla */
.table-container {
  background-color: var(--bbva-white);
  overflow-x: auto;
  overflow-y: hidden;
  position: relative;
}

/* Prevenir scrollbars innecesarios */
.bbva-custom-datatable {
  overflow: hidden;
}

.bbva-custom-datatable * {
  box-sizing: border-box;
}

/* ===== ESTILOS DE TABLA HTML ===== */

/* Tabla principal */
.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--bbva-gray-800, #2c3e50);
  background-color: var(--bbva-white);
}

/* Header de tabla */
.data-table thead {
  background: linear-gradient(135deg, var(--bbva-blue) 0%, var(--bbva-navy) 100%);
  position: relative;
}

.data-table thead::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--bbva-aqua) 0%, var(--bbva-gold) 100%);
}

.data-table th {
  background: transparent;
  color: var(--bbva-white);
  font-weight: 600;
  font-size: 0.8rem;
  padding: 16px 20px;
  text-align: left;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  border: none;
  position: relative;
  white-space: nowrap;
}

.data-table th:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 25%;
  bottom: 25%;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.2);
}

/* Body de tabla */
.data-table tbody {
  background-color: var(--bbva-white);
}

.data-table td {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 68, 129, 0.08);
  color: var(--bbva-gray-700, #495057);
  font-weight: 400;
  vertical-align: middle;
  transition: all 0.2s ease;
  position: relative;
}

/* Filas alternadas */
.data-table tbody tr:nth-child(even) {
  background-color: rgba(0, 68, 129, 0.02);
}

/* Hover en filas */
.data-table tbody tr {
  transition: all 0.2s ease;
  cursor: pointer;
}

.data-table tbody tr:hover {
  background-color: rgba(0, 68, 129, 0.06);
  box-shadow: 0 2px 8px rgba(0, 68, 129, 0.1);
}

.data-table tbody tr:hover td {
  color: var(--bbva-navy);
  font-weight: 500;
}

/* Bordes laterales en hover */
.data-table tbody tr:hover td:first-child::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, var(--bbva-blue) 0%, var(--bbva-aqua) 100%);
  border-radius: 0 2px 2px 0;
}

/* ===== MENSAJE VACÍO PROFESIONAL ===== */

/* Estado vacío profesional */
.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: linear-gradient(135deg, rgba(0, 68, 129, 0.02) 0%, rgba(2, 165, 165, 0.02) 100%);
  border: 2px dashed rgba(0, 68, 129, 0.1);
  border-radius: 8px;
  margin: 20px;
  color: var(--bbva-gray-600, #6c757d);
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
  position: relative;
}

.empty-message::before {
  content: '📊';
  font-size: 3rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-message::after {
  content: 'No hay datos disponibles para mostrar';
  font-size: 0.875rem;
  color: var(--bbva-gray-500);
  margin-top: 8px;
}

/* ===== FOOTER DE PAGINACIÓN PROFESIONAL ===== */

/* Layout de paginación centrado */
.pagination-layout {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: var(--bbva-gray-700, #495057);
  border-top: 2px solid rgba(0, 68, 129, 0.1);
  padding: 16px 24px;
  border-radius: 0 0 12px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
  font-weight: 500;
  font-size: 0.875rem;
  width: 100%;
  position: relative;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.pagination-layout::before {
  content: '';
  position: absolute;
  top: 0;
  left: 20%;
  right: 20%;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--bbva-aqua) 50%, transparent 100%);
}

/* ===== BOTONES DE PAGINACIÓN PROFESIONALES ===== */

/* Botones de paginación profesionales */
.pagination-button {
  min-width: 44px;
  height: 44px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, var(--bbva-white) 0%, #f8f9fa 100%);
  color: var(--bbva-blue);
  border: 2px solid rgba(0, 68, 129, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 68, 129, 0.1);
}

.pagination-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.pagination-button:hover:not([disabled]) {
  background: linear-gradient(135deg, var(--bbva-blue) 0%, var(--bbva-navy) 100%);
  color: var(--bbva-white);
  box-shadow: 0 6px 20px rgba(0, 68, 129, 0.3);
  border-color: var(--bbva-blue);
}

.pagination-button:hover:not([disabled])::before {
  left: 100%;
}

.pagination-button:active:not([disabled]) {
  box-shadow: 0 2px 8px rgba(0, 68, 129, 0.2);
}

.pagination-button[disabled] {
  opacity: 0.4;
  cursor: not-allowed;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: var(--bbva-gray-400, #adb5bd);
  border: 2px solid rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-button[disabled]::before {
  display: none;
}

/* Secciones del footer */
.pagination-left-buttons, .pagination-right-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-center-info {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

.pagination-center-info .page-info {
  color: var(--bbva-gray-700, #495057);
  font-size: 0.875rem;
  font-weight: 600;
  white-space: nowrap;
  text-align: center;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20px;
  border: 1px solid rgba(0, 68, 129, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .bbva-custom-datatable {
    border-radius: 8px;
    margin: 0 -16px;
  }
  
  .data-table th,
  .data-table td {
    padding: 12px 16px;
    font-size: 0.8rem;
  }
  
  .pagination-layout {
    padding: 12px 16px;
    gap: 16px;
  }
  
  .pagination-button {
    min-width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .pagination-center-info .page-info {
    font-size: 0.8rem;
    padding: 6px 12px;
  }
}

@media (max-width: 480px) {
  .data-table th,
  .data-table td {
    padding: 8px 12px;
    font-size: 0.75rem;
  }
  
  .pagination-layout {
    gap: 12px;
  }
  
  .pagination-button {
    min-width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }
}

/* ===== ANIMACIONES ===== */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animación para el DataTable */
.bbva-custom-datatable {
  animation: fadeIn 0.5s ease-out;
}

/* ===== ACCESIBILIDAD ===== */

.pagination-button:focus {
  outline: 2px solid var(--bbva-blue);
  outline-offset: 2px;
}

.data-table:focus {
  outline: 2px solid var(--bbva-blue);
  outline-offset: 2px;
}

/* Mejoras para lectores de pantalla */
.pagination-button[aria-label] {
  position: relative;
}

.data-table th[scope="col"] {
  /* Mejoras automáticas de accesibilidad */
}