/* ===== DATATABLE COMPONENT - ESTILOS GLOBALES REUTILIZADOS ===== */

/* 
 * Este archivo reutiliza los estilos globales de tablas y paginación
 * definidos en global-styles.css para mantener consistencia
 */

/* ===== GRID BASE STYLES (Copiado de global-styles.css) ===== */

/* Grid container base - Solo para DataTable */
.bbva-datatable vaadin-grid {
  border-radius: var(--bbva-border-radius, 8px);
  overflow: hidden;
  box-shadow: var(--bbva-shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.1));
  background-color: var(--bbva-white);
  font-family: 'Helvetica Neue', Arial, sans-serif;
  height: auto;
  min-height: auto;
}

/* Header cells - Estilo homologado - Solo para DataTable */
.bbva-datatable vaadin-grid::part(header-cell) {
  background-color: var(--bbva-blue);
  color: var(--bbva-white);
  font-weight: 600;
  font-size: 0.875rem;
  padding: 12px 16px;
  border: none;
  text-align: left;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Body cells - Estilo homologado - Solo para DataTable */
.bbva-datatable vaadin-grid::part(cell) {
  padding: 12px 16px;
  border-bottom: 1px solid var(--bbva-border-color);
  font-size: 0.875rem;
  color: var(--bbva-gray-900, #111111);
  background-color: var(--bbva-white);
}

/* Row hover effect - Solo para DataTable */
.bbva-datatable vaadin-grid::part(row):hover vaadin-grid::part(cell) {
  background-color: rgba(0, 68, 129, 0.05);
}

/* Alternate row styling - Solo para DataTable */
.bbva-datatable vaadin-grid::part(row):nth-child(even) vaadin-grid::part(cell) {
  background-color: var(--bbva-gray-50);
}

/* Estilos para los botones de acción */
.bbva-datatable .action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
  cursor: default; /* Reset para el contenedor */
}

/* Estilo para el botón de editar */
.bbva-datatable .action-buttons vaadin-button[title="Editar"] {
  color: var(--bbva-blue, #004481);
  cursor: pointer !important;
  padding: 0.25rem;
  min-width: auto;
  margin: 0;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

/* Estilo para el botón de eliminar */
.bbva-datatable .action-buttons vaadin-button[title="Eliminar"] {
  color: var(--bbva-red, #e31937);
  cursor: pointer !important;
  padding: 0.25rem;
  min-width: auto;
  margin: 0;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

/* Estilo para el contenedor del botón */
.bbva-datatable .action-buttons vaadin-button::part(button) {
  cursor: pointer !important;
}

/* Hover styles */
.bbva-datatable .action-buttons vaadin-button[title="Editar"]:hover,
.bbva-datatable .action-buttons vaadin-button[title="Eliminar"]:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Focus styles */
.bbva-datatable .action-buttons vaadin-button[title="Editar"]:focus-visible,
.bbva-datatable .action-buttons vaadin-button[title="Eliminar"]:focus-visible {
  outline: 2px solid var(--bbva-blue, #004481);
  outline-offset: 2px;
}

/* Selected row styling - Solo para DataTable */
.bbva-datatable vaadin-grid::part(selected-row) vaadin-grid::part(cell) {
  background-color: rgba(0, 68, 129, 0.1);
}

/* ===== SCROLLBAR PERSONALIZADO PARA GRIDS - Solo para DataTable ===== */

.bbva-datatable vaadin-grid::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.bbva-datatable vaadin-grid::-webkit-scrollbar-track {
  background: var(--bbva-gray-light);
  border-radius: 4px;
}

.bbva-datatable vaadin-grid::-webkit-scrollbar-thumb {
  background: var(--bbva-gray-medium);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.bbva-datatable vaadin-grid::-webkit-scrollbar-thumb:hover {
  background: var(--bbva-blue);
}

/* ===== ESTADOS ESPECIALES - Solo para DataTable ===== */

/* Loading state */
.bbva-datatable vaadin-grid[loading] {
  opacity: 0.7;
  pointer-events: none;
  position: relative;
}

/* Focus state - Solo para DataTable - Deshabilitado */
.bbva-datatable vaadin-grid:focus-within {
  outline: none;
}

/* Empty grid state - Solo para DataTable */
.bbva-datatable vaadin-grid[empty] {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bbva-datatable vaadin-grid[empty]::after {
  content: "No hay datos disponibles";
  color: var(--bbva-gray-500);
  font-style: italic;
  font-size: 0.875rem;
}

/* ===== PAGINACIÓN (Copiado de global-styles.css) ===== */

/* Contenedor de paginación personalizada */
.bbva-pagination {
  background-color: var(--bbva-gray-100, #f8f9fa);
  color: var(--bbva-gray-700, #495057);
  border-top: 1px solid var(--bbva-border-color, #E0E0E0);
  padding: 12px 16px;
  border-radius: 0 0 var(--bbva-border-radius-lg, 8px) var(--bbva-border-radius-lg, 8px);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--bbva-spacing-sm, 8px);
  font-weight: 500;
  font-size: 0.875rem;
}

/* Botones del paginador personalizado */
.bbva-pagination .bbva-pagination-button {
  --lumo-primary-color: var(--bbva-blue);
  --lumo-primary-text-color: var(--bbva-white);
  min-width: 100px;
  height: 36px;
  border-radius: var(--bbva-border-radius, 4px);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--bbva-transition, 0.2s ease);
  background-color: var(--bbva-white);
  color: var(--bbva-blue);
  border: 1px solid var(--bbva-border-color, #E0E0E0);
}

.bbva-pagination .bbva-pagination-button:hover:not([disabled]) {
  background-color: var(--bbva-blue);
  color: var(--bbva-white);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 68, 129, 0.2);
}

.bbva-pagination .bbva-pagination-button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--bbva-gray-200, #e9ecef);
  color: var(--bbva-gray-500, #6c757d);
  border: 1px solid var(--bbva-border-color, #E0E0E0);
}

/* Controles de paginación */
.bbva-pagination .bbva-pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--bbva-spacing-sm, 8px);
}

/* Información de página */
.bbva-pagination .bbva-pagination-info {
  color: var(--bbva-gray-700, #495057);
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}

/* Texto del paginador (compatibilidad) */
.bbva-pagination .pagination-info {
  color: var(--bbva-gray-700, #495057);
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}

/* ===== RESPONSIVE DESIGN - Solo para DataTable ===== */

@media (max-width: 1200px) {
  .bbva-datatable vaadin-grid {
    font-size: 0.75rem;
  }
  
  .bbva-datatable vaadin-grid::part(header-cell),
  .bbva-datatable vaadin-grid::part(cell) {
    padding: 8px 12px;
  }
}

@media (max-width: 768px) {
  .bbva-datatable vaadin-grid {
    font-size: 0.7rem;
  }
  
  .bbva-datatable vaadin-grid::part(header-cell),
  .bbva-datatable vaadin-grid::part(cell) {
    padding: 6px 8px;
  }
  
  .bbva-pagination {
    padding: var(--bbva-spacing-sm, 8px);
  }
  
  .bbva-pagination .pagination-info {
    font-size: 0.75rem;
    margin: 0 var(--bbva-spacing-sm, 8px);
  }
  
  .bbva-pagination .pagination-button {
    min-width: 32px;
    height: 28px;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .bbva-datatable vaadin-grid {
    font-size: 0.65rem;
  }
}

/* ===== PRINT STYLES ===== */

@media print {
  .bbva-datatable vaadin-grid::part(header-cell) {
    background-color: #f0f0f0 !important;
    color: #000 !important;
  }
  
  .bbva-datatable vaadin-grid::part(cell) {
    border: 1px solid #ccc;
  }
  
  .bbva-pagination {
    display: none;
  }
}

/* ===== ANIMACIONES ===== */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animación para el DataTable */
.bbva-datatable {
  animation: fadeIn 0.5s ease-out;
}

/* ===== ACCESIBILIDAD - Solo para DataTable ===== */

.bbva-datatable vaadin-grid[aria-label] {
  /* Mejoras de accesibilidad se manejan automáticamente por Vaadin */
}

/* Tooltips */
.bbva-datatable vaadin-grid [title] {
  cursor: help;
}

/* ===== ESTILOS ESPECÍFICOS DEL DATATABLE ===== */

/* Contenedor principal del DataTable */
.bbva-datatable {
  background: var(--bbva-white);
  border-radius: var(--bbva-border-radius-lg, 8px);
  box-shadow: var(--bbva-shadow-md, 0 4px 8px rgba(0, 0, 0, 0.1));
  overflow: hidden;
  padding: 0;
}

/* Grid dentro del DataTable - Estilos adicionales específicos */
.bbva-datatable vaadin-grid {
  border-radius: var(--bbva-border-radius, 8px);
  overflow: hidden; /* El grid interno mantiene overflow hidden */
  margin: 0;
  padding: 0;
}

/* Eliminar espacios extra del tbody */
.bbva-datatable vaadin-grid::part(body) {
  margin: 0;
  padding: 0;
  min-height: auto;
}

/* Asegurar que no hay espacios en las filas */
.bbva-datatable vaadin-grid::part(row) {
  margin: 0;
  padding: 0;
}

/* Estilos para botones de acción en las filas */
.bbva-datatable .action-button {
  margin: 0 2px;
  min-width: 32px;
  height: 32px;
  padding: 0;
  border-radius: var(--bbva-border-radius, 4px);
  transition: all var(--bbva-transition, 0.2s ease);
}

.bbva-datatable .action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.bbva-datatable .action-button vaadin-icon {
  font-size: 16px;
}

/* Columna de acciones */
.bbva-datatable .actions-column {
  text-align: center;
  white-space: nowrap;
}