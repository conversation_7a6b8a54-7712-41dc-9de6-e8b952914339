/* BBVA Modern Navigation Layout - Rediseñado desde cero */

/* ===== LAYOUT PRINCIPAL ===== */

/* Configuración base del AppLayout */
vaadin-app-layout {
  --vaadin-app-layout-navbar-background: var(--bbva-navy) !important;
  --vaadin-app-layout-navbar-text-color: var(--bbva-white) !important;
}

vaadin-app-layout::part(navbar) {
  background: linear-gradient(135deg, var(--bbva-navy) 0%, #0a2a5a 100%) !important;
  color: var(--bbva-white) !important;
  box-shadow: 0 4px 20px rgba(7, 33, 70, 0.3) !important;
  border-bottom: 3px solid var(--bbva-blue) !important;
}

.bbva-modern-layout {
  --vaadin-app-layout-navbar-background: var(--bbva-navy) !important;
  --vaadin-app-layout-navbar-text-color: var(--bbva-white) !important;
}

/* ===== HEADER MODERNO ===== */

.bbva-modern-header {
  background: linear-gradient(135deg, var(--bbva-navy) 0%, #0a2a5a 100%) !important;
  color: var(--bbva-white) !important;
  padding: 0 16px !important;
  height: 56px !important;
  box-shadow: 0 4px 20px rgba(7, 33, 70, 0.3) !important;
  border-bottom: 3px solid var(--bbva-blue) !important;
  position: relative !important;
  overflow: hidden !important;
  width: 100% !important;
  max-width: 100vw !important;
  box-sizing: border-box !important;
}

.bbva-modern-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(25, 115, 184, 0.1) 25%, 
    rgba(2, 165, 165, 0.1) 50%, 
    rgba(25, 115, 184, 0.1) 75%, 
    transparent 100%);
  pointer-events: none;
}

/* ===== SECCIÓN IZQUIERDA - LOGO Y TÍTULO ===== */

.bbva-header-left {
  padding: 0 !important;
  min-width: 200px !important;
  max-width: 250px !important;
  flex-shrink: 0 !important;
  z-index: 2 !important;
  position: relative !important;
}

/* Logo moderno con animación */
.bbva-modern-logo {
  display: flex !important;
  align-items: center !important;
  margin-right: 16px !important;
  cursor: pointer !important;
  transition: transform 0.3s ease !important;
}

.bbva-modern-logo:hover {
  transform: scale(1.05) !important;
}

.bbva-logo-bar {
  width: 8px !important;
  height: 28px !important;
  margin-right: 3px !important;
  border-radius: 2px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.bbva-logo-bar::before {
  content: '';
  position: absolute;
  top: -100%;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.3));
  transition: top 0.3s ease;
}

.bbva-modern-logo:hover .bbva-logo-bar::before {
  top: 0;
}

.bbva-logo-bar-1 {
  background: linear-gradient(135deg, var(--bbva-blue), var(--bbva-light-blue)) !important;
  animation: logoBar1 2s ease-in-out infinite alternate !important;
}

.bbva-logo-bar-2 {
  background: linear-gradient(135deg, var(--bbva-light-blue), var(--bbva-aqua)) !important;
  animation: logoBar2 2s ease-in-out infinite alternate 0.3s !important;
}

.bbva-logo-bar-3 {
  background: linear-gradient(135deg, var(--bbva-aqua), var(--bbva-gold)) !important;
  animation: logoBar3 2s ease-in-out infinite alternate 0.6s !important;
}

@keyframes logoBar1 {
  0% { height: 32px; }
  100% { height: 28px; }
}

@keyframes logoBar2 {
  0% { height: 32px; }
  100% { height: 36px; }
}

@keyframes logoBar3 {
  0% { height: 32px; }
  100% { height: 30px; }
}

/* Título moderno */
.bbva-modern-title {
  font-size: 18px !important; /* Reducido para barra compacta */
  font-weight: 700 !important;
  color: var(--bbva-white) !important;
  margin: 0 !important;
  letter-spacing: -0.5px !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  background: linear-gradient(135deg, var(--bbva-white), var(--bbva-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ===== SECCIÓN CENTRAL - NAVEGACIÓN (MENU BAR) ===== */

.bbva-header-center {
  flex-grow: 1 !important;
  z-index: 2 !important;
  position: relative !important;
  display: flex !important;
  justify-content: center !important;
  overflow: hidden !important;
  min-width: 0 !important;
}

.bbva-menu-bar {
  background: transparent !important;
  border: none !important;
  color: var(--bbva-white) !important;
  width: 100% !important;
  overflow: hidden !important;
  flex-wrap: nowrap !important;
}

/* Estilo para cada botón del menu bar */
.bbva-menu-bar vaadin-menu-bar-button {
  color: var(--bbva-white) !important;
  background: transparent !important;
  border: 1px solid transparent !important;
  border-radius: 8px !important;
  padding: 6px 8px !important;
  font-weight: 600 !important;
  font-size: 12px !important;
  letter-spacing: 0.3px !important;
  transition: all 0.3s ease !important;
  margin: 0 1px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  flex-shrink: 1 !important;
  min-width: 0 !important;
}

/* Efecto hover para los botones */
.bbva-menu-bar vaadin-menu-bar-button:hover {
  background: rgba(7, 33, 70, 0.8) !important;
  color: var(--bbva-white) !important;
}

/* Botón cuando el submenú está abierto */
.bbva-menu-bar vaadin-menu-bar-button[aria-expanded="true"] {
  background: var(--bbva-navy) !important;
  border-bottom: 2px solid var(--bbva-blue) !important;
}

/* Estilo para el popup (overlay) del submenú */
vaadin-context-menu-list-box[theme~="bbva-dark-menu"] {
  background: linear-gradient(135deg, var(--bbva-navy) 0%, #0a2a5a 100%) !important;
  border: 1px solid var(--bbva-blue) !important;
  border-radius: 8px !important;
  padding: 8px !important;
}

/* Estilo para cada item dentro del submenú */
vaadin-context-menu-item[theme~="bbva-dark-menu"] {
  color: var(--bbva-white) !important;
  background: transparent !important;
  padding: 10px 16px !important;
  border-radius: 4px !important;
  font-size: 14px;
}

/* Efecto hover para los items del submenú */
vaadin-context-menu-item[theme~="bbva-dark-menu"]:hover {
  background-color: rgba(25, 115, 184, 0.3) !important;
}

/* Iconos dentro de los botones del menú */
.bbva-menu-bar vaadin-icon {
  color: var(--bbva-aqua) !important;
  width: 14px;
  height: 14px;
  margin-right: 2px !important;
}

/* Texto dentro de los botones del menú */
.bbva-menu-bar span {
  font-weight: 600 !important;
  font-size: 13px !important;
  color: var(--bbva-white) !important;
}

/* ===== SECCIÓN DERECHA - USUARIO Y NOTIFICACIONES ===== */

.bbva-header-right {
  padding: 0 !important;
  min-width: 200px !important;
  max-width: 250px !important;
  flex-shrink: 0 !important;
  justify-content: flex-end !important;
  z-index: 2 !important;
  position: relative !important;
}

/* Botón de notificaciones */
.bbva-notification-button {
  color: var(--bbva-white) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  padding: 0 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  margin-right: 12px !important;
}

.bbva-notification-button:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.1) !important;
}

/* Badge de notificaciones */
.bbva-notification-badge {
  position: absolute !important;
  top: -8px !important;
  right: -8px !important;
  background: var(--bbva-gold) !important;
  color: var(--bbva-navy) !important;
  border-radius: 50% !important;
  width: 18px !important;
  height: 18px !important;
  font-size: 10px !important;
  font-weight: 700 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  animation: pulse 2s infinite !important;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Contenedor de usuario */
.bbva-user-container {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 6px 12px !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 24px !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  backdrop-filter: blur(10px) !important;
}

.bbva-user-container:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* Avatar del usuario */
.bbva-user-avatar {
  width: 32px !important;
  height: 32px !important;
  border: 2px solid var(--bbva-aqua) !important;
}

/* Información del usuario */
.bbva-user-info {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
}

.bbva-user-name {
  font-weight: 600 !important;
  font-size: 14px !important;
  color: var(--bbva-white) !important;
  line-height: 1.2 !important;
}

.bbva-user-role {
  font-weight: 400 !important;
  font-size: 12px !important;
  color: rgba(255, 255, 255, 0.8) !important;
  line-height: 1.2 !important;
}

/* ===== MENÚS CONTEXTUALES ===== */

.bbva-context-menu {
  background: var(--bbva-white) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2) !important;
  padding: 8px !important;
  backdrop-filter: blur(20px) !important;
}

.bbva-user-menu {
  background: var(--bbva-white) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2) !important;
  padding: 8px !important;
  min-width: 200px !important;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1200px) {
  .bbva-modern-header {
    padding: 0 16px !important;
  }
  
  .bbva-header-left {
    min-width: 200px !important;
  }
  
  .bbva-header-right {
    min-width: 200px !important;
  }
  
  .bbva-nav-button {
    padding: 10px 16px !important;
    font-size: 13px !important;
    min-width: 100px !important;
  }
}

@media (max-width: 768px) {
  .bbva-modern-header {
    height: 64px !important;
    padding: 0 12px !important;
  }
  
  .bbva-modern-title {
    font-size: 18px !important;
  }
  
  .bbva-user-info {
    display: none !important;
  }
  
  .bbva-nav-button {
    padding: 8px 12px !important;
    font-size: 12px !important;
    min-width: 80px !important;
  }
  
  .bbva-nav-container {
    gap: 4px !important;
  }
}