/* Styles specific to LoginView */

/* Login view container - Estilos base en global-styles.css */

/* Login form container */
.login-form-container {
  background-color: var(--bbva-white);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 32px;
  width: 100%;
  max-width: 400px;
}

/* Login logo */
.login-logo {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

/* Login form fields */
.login-field {
  width: 100%;
  margin-bottom: 16px;
}

.login-field label {
  color: var(--bbva-navy);
  font-weight: 500;
}

/* Login button */
.login-button {
  background-color: var(--bbva-blue);
  color: var(--bbva-white);
  font-weight: 600;
  width: 100%;
  margin-top: 16px;
  border-radius: 4px;
}

.login-button:hover {
  background-color: var(--bbva-light-blue);
}

/* Login footer */
.login-footer {
  margin-top: 24px;
  text-align: center;
  color: var(--bbva-navy);
  font-size: 14px;
}

/* Login error message */
.login-error {
  color: #d32f2f;
  margin-top: 16px;
  font-size: 14px;
  text-align: center;
}