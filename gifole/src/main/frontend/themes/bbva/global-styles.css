/* ===== BBVA GLOBAL STYLES - CLASES UTILITARIAS Y ESTILOS GLOBALES ===== */

/* Reset y estilos base */
h2 {
  color: var(--bbva-blue) !important;
}

/* ===== CLASES UTILITARIAS BBVA ===== */

/* ===== LAYOUT & CONTAINERS ===== */

/* Main application container - Contenedor principal de la aplicación */
.app-main {
    background-color: var(--bbva-light-gray);
    padding: 20px;
}

/* ===== CARDS & CONTAINERS ===== */

/* Card básica BBVA - Para contenedores generales */
.bbva-card {
  background-color: var(--bbva-white) !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  padding: 24px !important;
  margin-bottom: 24px !important;
}


/* ===== TEXT UTILITIES ===== */

/* Colores de texto BBVA */
.bbva-text-primary {
  color: var(--bbva-blue) !important;
}

.bbva-text-navy {
  color: var(--bbva-navy) !important;
}

.bbva-text-aqua {
  color: var(--bbva-aqua) !important;
}

.bbva-text-gold {
  color: var(--bbva-gold) !important;
}

/* Button utility classes */
.bbva-button-primary {
  background-color: var(--bbva-blue) !important;
  color: var(--bbva-white) !important;
  font-weight: 600 !important;
  border-radius: 4px !important;
}

.bbva-button-secondary {
  background-color: var(--bbva-light-blue) !important;
  color: var(--bbva-white) !important;
  font-weight: 600 !important;
  border-radius: 4px !important;
}

/* Common form styling */
.bbva-form-field {
  width: 100% !important;
  margin-bottom: 16px !important;
}

.bbva-form-field label {
  color: var(--bbva-navy) !important;
  font-weight: 500 !important;
}

/* ===== TÍTULOS DE PÁGINA HOMOLOGADOS ===== */

/* Estilo homologado para títulos de página */
.bbva-page-title {
  color: var(--bbva-blue) !important;
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin: 0 0 20px 0 !important;
  padding-bottom: 10px !important;
  border-bottom: 2px solid var(--bbva-blue) !important;
  text-transform: none !important;
  letter-spacing: normal !important;
}

/*
 * SOLUCIÓN DEFINITIVA: Se anula la variable --lumo-header-text-color 
 * directamente en el componente vaadin-date-picker-overlay, que es el calendario emergente.
 * De esta forma, cuando el calendario aparece, usa el color azul BBVA para el encabezado
 * del mes, en lugar del color blanco heredado del tema.
 */
vaadin-date-picker-overlay {
    --lumo-header-text-color: var(--bbva-blue);
}


/* ===== FORMS & INPUTS ===== */

/* 
 * Estilos para formularios e inputs del sistema BBVA
 * Incluye inputs básicos, flotantes y validaciones
 */


/* ===== FLOATING INPUT STYLES ===== */

/* Input flotante BBVA - Para campos con labels superpuestos (estilo moderno) */
.bbva-input-floating {
  --lumo-primary-color: var(--bbva-blue);
  --lumo-primary-color-50pct: rgba(0, 68, 129, 0.5);
  --lumo-primary-color-10pct: rgba(0, 68, 129, 0.1);
  --lumo-primary-text-color: var(--bbva-blue);
  --lumo-contrast-10pct: rgba(0, 68, 129, 0.1);
  margin-bottom: 8px;
  position: relative;
}

.bbva-input-floating::part(label) {
  position: absolute;
  top: 30%;
  left: 1rem;
  transform: translateY(-50%);
  background: var(--bbva-white);
  padding: 0 0.5rem;
  color: var(--bbva-gray-600);
  font-size: 0.75rem;
  font-weight: 500;
  z-index: 2;
  line-height: 1;
}

/* Estilos base para todos los inputs flotantes */
.bbva-input-floating::part(input-field) {
  border: 2px solid var(--bbva-border-color);
  border-radius: var(--bbva-border-radius, 4px);
  transition: all 0.2s ease;
  background-color: var(--bbva-white);
  min-height: var(--lumo-text-field-size, 2.25rem) !important;
  height: var(--lumo-text-field-size, 2.25rem) !important;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.bbva-input-floating::part(input-field):focus-within {
  border-color: var(--bbva-blue, #004481);
  box-shadow: 0 0 0 3px rgba(0, 68, 129, 0.1);
}

.bbva-input-floating::part(input-field):hover {
  border-color: var(--bbva-light-blue);
}

/* Ajuste específico solo para ComboBox toggle button */
vaadin-combo-box.bbva-input-floating::part(toggle-button) {
  height: 100%;
  display: flex;
  align-items: center;
}

/* ===== FILTERS & SEARCH ===== */

/* 
 * Estilos para filtros y búsquedas
 * Incluye cards de filtros y botones de acción
 */

/* Card de filtros - Contenedor para formularios de filtrado */
.bbva-filters-card {
  width: 100%;
  height: auto;
  min-height: fit-content;
  background-color: var(--bbva-white);
  border-radius: var(--bbva-border-radius-lg, 8px);
  box-shadow: var(--bbva-shadow-md, 0 4px 8px rgba(0, 0, 0, 0.1));
  padding: var(--bbva-spacing-md);
  padding-bottom: var(--bbva-spacing-xs);
  display: flex;
  flex-wrap: wrap;
  gap: var(--bbva-spacing-md, 16px);
  align-items: flex-start;
  justify-content: flex-start;
}

/* Botón de filtros - Para acciones de filtrado y búsqueda */
.bbva-filters-button {
  --lumo-primary-color: var(--bbva-blue);
  --lumo-primary-text-color: var(--bbva-white);
  --lumo-contrast-10pct: rgba(0, 68, 129, 0.1);
  border-radius: var(--bbva-border-radius, 4px);
  font-weight: 500;
  transition: all var(--bbva-transition, 0.2s ease);
  align-self: flex-start;
  height: var(--lumo-text-field-size, 2.25rem);
  min-width: 120px;
  margin-top: 24px;
}

.bbva-filters-button:hover {
  background-color: var(--bbva-light-blue);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 68, 129, 0.3);
}

/* Responsive para filtros */
@media (max-width: 768px) {
  .bbva-filters-card {
    padding: var(--bbva-spacing-md, 16px);
    margin-bottom: var(--bbva-spacing-md, 16px);
  }
  
  
  .bbva-filters-button {
    align-self: stretch;
    margin-top: var(--bbva-spacing-sm, 8px);
  }
}


