# BBVA Gifole - Proyecto MVC

## Descripción
Proyecto Spring Boot con arquitectura MVC utilizando Vaadin para el frontend y la paleta de colores oficial de BBVA.

## Estructura del Proyecto

```
src/main/java/pe/com/bbva/gifole/
├── GifoleApplication.java          # Clase principal de Spring Boot
├── ServletInitializer.java         # Inicializador para despliegue WAR
├── config/                         # Configuraciones
│   ├── DatabaseConfig.java         # Configuración de base de datos
│   └── VaadinConfig.java          # Configuración de Vaadin
├── dto/                           # Data Transfer Objects
│   └── BaseDTO.java               # DTO base
├── exception/                     # Manejo de excepciones
│   ├── GifoleException.java       # Excepción personalizada
│   └── GlobalExceptionHandler.java # Manejador global de excepciones
├── model/                         # Entidades JPA
│   └── BaseEntity.java            # Entidad base con auditoría
├── service/                       # Servicios de negocio
│   ├── BaseService.java           # Interfaz de servicio base
│   └── impl/                      # Implementaciones
│       └── BaseServiceImpl.java   # Implementación base con EntityManager
├── util/                          # Utilidades
│   ├── Constants.java             # Constantes de la aplicación
│   └── DateUtils.java             # Utilidades para fechas
└── view/                          # Vistas Vaadin
    ├── BaseView.java              # Vista base
    ├── HomeView.java              # Vista principal
    ├── MainLayout.java            # Layout principal con navegación
    └── theme/                     # Tema y estilos
        └── BBVATheme.java         # Constantes de tema BBVA
```

## Paleta de Colores BBVA

El proyecto utiliza la paleta de colores oficial de BBVA:

- **BBVA Navy**: `#072146` - Azul oscuro del logo BBVA
- **BBVA Blue**: `#004481` - Azul primario de BBVA
- **BBVA Light Blue**: `#1973B8` - Azul secundario
- **BBVA Aqua**: `#02A5A5` - Acento teal/aqua
- **BBVA White**: `#FFFFFF` - Blanco
- **BBVA Gold**: `#F8CD51` - Acento dorado del ícono estrella

## Tecnologías Utilizadas

- **Spring Boot 3.5.3** - Framework principal
- **Vaadin 24.8.2** - Framework de UI
- **JPA** - Persistencia de datos (sin Spring Data JPA)
- **JBoss DataSource** - Conexión a base de datos empresarial
- **Java 21** - Versión de Java

## Configuración

### Base de Datos
- **DataSource**: `java:jboss/datasources/GifoleDS`
- **JPA**: Configurado para usar EntityManager directamente
- **Transacciones**: Gestionadas por Spring

### Vaadin
- **Tema**: Personalizado con colores BBVA
- **CSS**: `/static/css/bbva-theme.css`
- **Navegación**: Layout principal con menú lateral

## Cómo Ejecutar

1. Clonar el repositorio
2. Ejecutar: `./mvnw spring-boot:run`
3. Abrir navegador en: `http://localhost:8080`

## Estructura MVC

### Model (Modelo)
- **BaseEntity**: Entidad base con campos de auditoría (id, createdAt, updatedAt)
- Ubicación: `pe.com.bbva.gifole.model`

### View (Vista)
- **Vaadin Components**: Interfaz de usuario reactiva
- **BaseView**: Vista base con tema BBVA aplicado
- **MainLayout**: Layout principal con navegación
- Ubicación: `pe.com.bbva.gifole.view`

### Servicios
- **BaseService**: Interfaz de servicio con operaciones comunes
- **BaseServiceImpl**: Implementación base usando EntityManager directamente
- **Comunicación**: Las vistas Vaadin se comunican directamente con los servicios
- Ubicación: `pe.com.bbva.gifole.service`

## Próximos Pasos

1. Crear entidades específicas del dominio
2. Implementar servicios de negocio
3. Desarrollar vistas Vaadin específicas
4. Configurar seguridad
5. Agregar validaciones
6. Implementar tests unitarios e integración