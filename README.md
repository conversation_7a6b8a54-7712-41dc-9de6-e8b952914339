# Sistema GIFOLE - Arquitectura y Componentes

Este proyecto implementa el sistema GIFOLE de BBVA utilizando una arquitectura de 3 capas con contenedores Docker para facilitar el desarrollo y despliegue.

## 🏗️ Arquitectura del Sistema

```
┌─────────────────────────────────────────────────────────────────┐
│                        SISTEMA GIFOLE                          │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────┐  │
│  │     GIFOLE      │    │   JBOSS-EAP-8   │    │  GIFOLE-BD  │  │
│  │  (Aplicación)   │◄──►│ (App Server)    │◄──►│ (Base Datos)│  │
│  │                 │    │                 │    │             │  │
│  │ Spring Boot 3.5 │    │ WildFly 28.x    │    │ Oracle XE   │  │
│  │ Vaadin 24.8     │    │ Puerto: 8080    │    │ Puerto:1523 │  │
│  │ Java 21         │    │ Admin: 9990     │    │ SID: XE     │  │
│  └─────────────────┘    └─────────────────┘    └─────────────┘  │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 📁 Estructura del Proyecto

```
gifole/
├── gifole/                     # Aplicación Spring Boot + Vaadin
├── jboss-eap-8/               # Servidor de aplicaciones JBoss EAP 8
├── gifole-bd/                 # Base de datos Oracle containerizada
└── README.md                  # Este archivo
```

## 🔄 Interacción entre Componentes

### 1. **GIFOLE-BD** (Base de Datos)
- **Tecnología**: Oracle XE 21c en Docker
- **Puerto**: 1523 (externo) → 1521 (interno)
- **Función**: Almacena todos los datos del sistema GIFOLE
- **Características**:
  - 80+ tablas del sistema GIFOLE
  - 136,685+ registros de datos iniciales
  - Usuarios: `GIFOLE`, `APP_GIFOLE`, `GIFPRI`, etc.
  - Esquemas: GIFOLE (principal), GIFPRI, CAFUAP

### 2. **JBOSS-EAP-8** (Servidor de Aplicaciones)
- **Tecnología**: WildFly 28.x (compatible con JBoss EAP 8)
- **Puertos**: 8080 (aplicaciones), 9990 (administración)
- **Función**: Servidor de aplicaciones Java EE
- **Características**:
  - Datasource Oracle configurado automáticamente
  - JNDI: `java:jboss/datasources/GifoleDS`
  - Pool de conexiones (5-20 conexiones)
  - Driver Oracle JDBC 23.3 integrado

### 3. **GIFOLE** (Aplicación Web)
- **Tecnología**: Spring Boot 3.5.3 + Vaadin 24.8.2
- **Función**: Aplicación web principal del sistema GIFOLE
- **Características**:
  - Interfaz de usuario con Vaadin
  - API REST con Swagger/OpenAPI
  - Tema personalizado BBVA
  - Múltiples perfiles (local, dev, qa, prod)

## 🔗 Flujo de Conexión

```
1. Usuario → Navegador Web (http://localhost:8080)
2. Navegador → JBoss EAP 8 (gifole-web-ui.war)
3. JBoss EAP 8 → Aplicación Spring Boot
4. Spring Boot → DataSource JNDI (java:jboss/datasources/GifoleDS)
5. DataSource → Base de datos Oracle (oracle-gifole-bd:1521/GIFOLE)
```

## 🚀 Cómo Ejecutar el Sistema Completo

### Paso 1: Iniciar Base de Datos
```bash
cd gifole-bd
docker-compose up -d
```

### Paso 2: Iniciar JBoss EAP 8
```bash
cd jboss-eap-8
docker-compose up -d
```

### Paso 3: Compilar y Desplegar Aplicación
```bash
cd gifole
# Compilar WAR
./mvnw clean package -P prod

# Copiar WAR a JBoss
cp target/gifole-web-ui.war ../jboss-eap-8/deployments/
```

### Paso 4: Verificar Despliegue
- **Aplicación**: http://localhost:8080/gifole-web-ui
- **Admin JBoss**: http://localhost:9990 (admin/admin123)
- **API Docs**: http://localhost:8080/gifole-web-ui/swagger-ui.html

## 🔧 Configuración por Ambiente

### Local (Desarrollo)
- **DataSource**: `java:jboss/datasources/GifoleDS`
- **Context Path**: `/gifole-web-ui`
- **Base URL**: http://localhost:8080/gifole-web-ui

### Producción
- **DataSource**: `java:jboss/datasources/APP_GIFOLE`
- **Context Path**: `/gifole-backend`
- **Modo Producción**: Vaadin optimizado

## 🌐 Red Docker

Todos los componentes se comunican a través de la red Docker `gifole-network`:

```yaml
networks:
  gifole-network:
    driver: bridge
```

## 📊 Datos del Sistema

- **Tablas**: 80+ tablas del sistema GIFOLE
- **Registros**: 136,685+ registros iniciales
- **Módulos**: Afiliaciones, Cupones, Vehículos, Seguros, Préstamos, etc.

## 🔍 Monitoreo y Logs

```bash
# Ver logs de base de datos
docker-compose -f gifole-bd/docker-compose.yml logs -f

# Ver logs de JBoss
docker-compose -f jboss-eap-8/docker-compose.yml logs -f

# Verificar estado de datasource
curl -s http://localhost:9990/management \
  --user admin:admin123 --digest \
  -H "Content-Type: application/json" \
  -d '{"operation":"test-connection-in-pool","address":["subsystem","datasources","data-source","GifoleDS"]}'
```

## 🛠️ Tecnologías Utilizadas

| Componente | Tecnología | Versión |
|------------|------------|---------|
| Aplicación | Spring Boot | 3.5.3 |
| UI Framework | Vaadin | 24.8.2 |
| Java | OpenJDK | 21 |
| App Server | WildFly | 28.x |
| Base de Datos | Oracle XE | 21c |
| Contenedores | Docker | Latest |

---

**Desarrollado para BBVA - Sistema GIFOLE**
