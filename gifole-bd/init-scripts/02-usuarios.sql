
-- <PERSON><PERSON><PERSON> al contenedor GIFOLE
ALTER SESSION SET CONTAINER = GIFOLE;

alter session set "_ORACLE_SCRIPT"=true;

/* CREAR USUARIO GIFOLE */

CREATE USER GIFOLE IDENTIFIED BY GIFOLE 
DEFAULT TABLESPACE GIFOLE_DATA
TEMPORARY TABLESPACE TEMP
PROFILE DEFAULT
QUOTA UNLIMITED ON GIFOLE_DATA 
QUOTA UNLIMITED ON GIFOLE_INDEX;
  
GRANT CREATE PROCEDURE TO GIFOLE;
GRANT CREATE SEQUENCE TO GIFOLE;
GRANT CREATE SESSION TO GIFOLE;
GRA<PERSON> CREATE TABLE TO GIFOLE;

/* CREAR USUARIO APP_GIFOLE */

CREATE USER APP_GIFOLE IDENTIFIED BY APP_GIFOLE 
DEFAULT TABLESPACE APP_GIFOLE_DATA
TEMPORARY TABLESPACE TEMP
PROFILE DEFAULT
QUOTA UNLIMITED ON APP_GIF<PERSON>E_DATA 
QUOTA UNLIMITED ON APP_GIFOLE_INDEX;
  
GRANT CREATE PROCEDURE TO APP_GIFOLE;
GRANT CREATE SEQUENCE TO APP_GIFOLE;
GRANT CREATE SESSION TO APP_GIFOLE;
GRANT CREATE TABLE TO APP_GIFOLE;

/* CREAR USUARIO APP_GIFPRI */

CREATE USER APP_GIFPRI IDENTIFIED BY APP_GIFPRI 
DEFAULT TABLESPACE APP_GIFPRI_DATA
TEMPORARY TABLESPACE TEMP
PROFILE DEFAULT
QUOTA UNLIMITED ON APP_GIFPRI_DATA 
QUOTA UNLIMITED ON APP_GIFPRI_INDEX;
  
GRANT CREATE PROCEDURE TO APP_GIFPRI;
GRANT CREATE SEQUENCE TO APP_GIFPRI;
GRANT CREATE SESSION TO APP_GIFPRI;
GRANT CREATE TABLE TO APP_GIFPRI;

/* CREAR USUARIO APP_CAFUAP */

CREATE USER APP_CAFUAP IDENTIFIED BY APP_CAFUAP 
DEFAULT TABLESPACE APP_CAFUAP_DATA
TEMPORARY TABLESPACE TEMP
PROFILE DEFAULT
QUOTA UNLIMITED ON APP_CAFUAP_DATA 
QUOTA UNLIMITED ON APP_CAFUAP_INDEX;
  
GRANT CREATE PROCEDURE TO APP_CAFUAP;
GRANT CREATE SEQUENCE TO APP_CAFUAP;
GRANT CREATE SESSION TO APP_CAFUAP;
GRANT CREATE TABLE TO APP_CAFUAP;

/* CREAR USUARIO GIFPRI */

CREATE USER GIFPRI IDENTIFIED BY GIFPRI 
DEFAULT TABLESPACE GIFPRI_DATA
TEMPORARY TABLESPACE TEMP
PROFILE DEFAULT
QUOTA UNLIMITED ON GIFPRI_DATA 
QUOTA UNLIMITED ON GIFPRI_INDEX;
  
GRANT CREATE PROCEDURE TO GIFPRI;
GRANT CREATE SEQUENCE TO GIFPRI;
GRANT CREATE SESSION TO GIFPRI;
GRANT CREATE TABLE TO GIFPRI;
