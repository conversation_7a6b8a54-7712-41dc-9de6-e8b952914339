
-- <PERSON><PERSON><PERSON> al contenedor GIFOLE
ALTER SESSION SET CONTAINER = GIFOLE;

--------------------------------------------------------
--  DDL for Table AFILIACION_EECC_FFMM
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."AFILIACION_EECC_FFMM" 
   (	"ID" NUMBER(16,0), 
	"NOMBRE" NVARCHAR2(150), 
	"CORREO1" NVARCHAR2(80), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"CODIGO_CENTRAL" NVARCHAR2(8), 
	"PROCESADO" CHAR(1)
   );
  GRANT SELECT ON "GIFOLE"."AFILIACION_EECC_FFMM" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."AFILIACION_EECC_FFMM" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."AFILIACION_EECC_FFMM" TO "APP_GIFOLE";
  GRANT UPDATE ON "GIFOLE"."AFILIACION_EECC_FFMM" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table AFILIACION_EECC_FFMM_DETALLE
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."AFILIACION_EECC_FFMM_DETALLE" 
   (	"ID" NUMBER(16,0), 
	"NOMBRE_FONDO" NVARCHAR2(50), 
	"NUMERO_CONTRATO" NVARCHAR2(25), 
	"AFILIACION_EECC_FFMM" NUMBER(16,0)
   );
  GRANT UPDATE ON "GIFOLE"."AFILIACION_EECC_FFMM_DETALLE" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."AFILIACION_EECC_FFMM_DETALLE" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."AFILIACION_EECC_FFMM_DETALLE" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."AFILIACION_EECC_FFMM_DETALLE" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table CUPONES_RASPA_GANA xxxxxxxxxxxx
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."CUPONES_RASPA_GANA" 
   (	"ID" NUMBER, 
	"CODIGO_SORTEO" VARCHAR2(10), 
	"ESTADO" CHAR(1), 
	"FECHA_REGISTRO" DATE
   );
  GRANT DELETE ON "GIFOLE"."CUPONES_RASPA_GANA" TO "APP_GIFOLE";
  GRANT UPDATE ON "GIFOLE"."CUPONES_RASPA_GANA" TO "GIFPRI";
  GRANT SELECT ON "GIFOLE"."CUPONES_RASPA_GANA" TO "GIFPRI";
  GRANT INSERT ON "GIFOLE"."CUPONES_RASPA_GANA" TO "GIFPRI";
  GRANT DELETE ON "GIFOLE"."CUPONES_RASPA_GANA" TO "GIFPRI";
  GRANT UPDATE ON "GIFOLE"."CUPONES_RASPA_GANA" TO "APP_GIFPRI";
  GRANT SELECT ON "GIFOLE"."CUPONES_RASPA_GANA" TO "APP_GIFPRI";
  GRANT INSERT ON "GIFOLE"."CUPONES_RASPA_GANA" TO "APP_GIFPRI";
  GRANT DELETE ON "GIFOLE"."CUPONES_RASPA_GANA" TO "APP_GIFPRI";
  GRANT UPDATE ON "GIFOLE"."CUPONES_RASPA_GANA" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."CUPONES_RASPA_GANA" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."CUPONES_RASPA_GANA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table EMPRESA_MUNDO_SUELDO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."EMPRESA_MUNDO_SUELDO" 
   (	"ID" NUMBER(16,0), 
	"NOMBRE" NVARCHAR2(50), 
	"NUMERO" NVARCHAR2(11), 
	"DEPARTAMENTO" NVARCHAR2(20), 
	"CORREO" NVARCHAR2(80), 
	"TELEFONO1" NVARCHAR2(20), 
	"HORARIO" NVARCHAR2(20), 
	"CONTACTO" NVARCHAR2(100), 
	"PROCESADO" CHAR(1), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"TELEFONO2" NVARCHAR2(20), 
	"AUTORIZACION" CHAR(1)
   );
  GRANT UPDATE ON "GIFOLE"."EMPRESA_MUNDO_SUELDO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."EMPRESA_MUNDO_SUELDO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."EMPRESA_MUNDO_SUELDO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."EMPRESA_MUNDO_SUELDO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table EXT_CUPONES_RASPA_GANA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."EXT_CUPONES_RASPA_GANA" 
   (	"CODIGO_SORTEO" VARCHAR2(10)
   );
--   ORGANIZATION EXTERNAL 
--    ( TYPE ORACLE_LOADER
--      DEFAULT DIRECTORY "GIFOLE_LOAD_RASPA_GANA_DIR"
--      ACCESS PARAMETERS
--      ( FIELDS (	
--  CODIGO_SORTEO position (1:10)  
--    ) 
--       )
--      LOCATION
--       ( 'cupones.txt'
--       )
--    );
  GRANT SELECT ON "GIFOLE"."EXT_CUPONES_RASPA_GANA" TO "APP_GIFOLE";
  GRANT ALTER ON "GIFOLE"."EXT_CUPONES_RASPA_GANA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table EXT_VEH_AUTOMAS
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."EXT_VEH_AUTOMAS" 
   (	"CODMARCA" CHAR(4), 
	"MARCA" CHAR(40), 
	"CODMODELO" CHAR(8), 
	"MODELO" CHAR(40), 
	"ANHO_FABRICACION" CHAR(4), 
	"DIVISA" CHAR(3), 
	"VALOR_COMERCIAL" CHAR(15)
   ); 
   
--   ORGANIZATION EXTERNAL 
--    ( TYPE ORACLE_LOADER
--      DEFAULT DIRECTORY "GIFOLE_LOAD_DIR"
--      ACCESS PARAMETERS
--      ( FIELDS (	
--  CODMARCA position (1:4),
--  MARCA position (5:44),
--	CODMODELO position (45:52),
--  MODELO position (53:92),
--  ANHO_FABRICACION position (93:96),
--  DIVISA position (97:99),
--	VALOR_COMERCIAL position (100:114)
--    ) 
--           )
--      LOCATION
--       ( 'automas_ant.txt'
--       )
--    );
  GRANT SELECT ON "GIFOLE"."EXT_VEH_AUTOMAS" TO "APP_GIFOLE";
  GRANT ALTER ON "GIFOLE"."EXT_VEH_AUTOMAS" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table EXT_VEH_BENEFICIO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."EXT_VEH_BENEFICIO" 
   (	"ID" CHAR(4), 
	"DESCRIPCION" CHAR(100), 
	"TIPO" CHAR(1), 
	"ORDEN" CHAR(12)
   );
--   ORGANIZATION EXTERNAL 
--    ( TYPE ORACLE_LOADER
--      DEFAULT DIRECTORY "GIFOLE_LOAD_DIR"
--      ACCESS PARAMETERS
--      ( RECORDS DELIMITED BY NEWLINE 
--	 CHARACTERSET UTF8
--	 SKIP 0    
--	 BADFILE '%a_%p.bad' 
--	 LOGFILE '%a_%p.log' 
--	 FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '"'  LRTRIM  
--	 MISSING FIELD VALUES ARE NULL  
--	 REJECT ROWS WITH ALL NULL FIELDS 
--    ( ID CHAR(4),	
--	DESCRIPCION CHAR(100),
--	TIPO CHAR(1),
--	ORDEN CHAR(12)
--    ) 
--           )
--      LOCATION
--       ( 'beneficio_ant.txt'
--       )
--    );
  GRANT SELECT ON "GIFOLE"."EXT_VEH_BENEFICIO" TO "APP_GIFOLE";
  GRANT ALTER ON "GIFOLE"."EXT_VEH_BENEFICIO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table EXT_VEH_CATEGORIA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."EXT_VEH_CATEGORIA" 
   (	"CODMARCA" CHAR(4), 
	"MARCA" CHAR(40), 
	"CODMODELO" CHAR(8), 
	"MODELO" CHAR(40), 
	"CODCLASE" CHAR(4), 
	"CODTIPO" CHAR(7), 
	"TIPO" CHAR(30), 
	"CODIGO" CHAR(4), 
	"NOMBRE" CHAR(30)
   );
--   ORGANIZATION EXTERNAL 
--    ( TYPE ORACLE_LOADER
--      DEFAULT DIRECTORY "GIFOLE_LOAD_DIR"
--      ACCESS PARAMETERS
--      ( FIELDS (	
--  CODMARCA position (1:4),
--  MARCA position (5:44),
--	CODMODELO position (45:52),
--  MODELO position (53:92),
--  CODCLASE position (130:133),
--  CODTIPO position (93:99),
--  TIPO position (100:129),
--  CODIGO position (164:167),
--	NOMBRE position (168:197)
--    ) 
--           )
--      LOCATION
--       ( 'categoria_ant.txt'
--       )
--    );
  GRANT ALTER ON "GIFOLE"."EXT_VEH_CATEGORIA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table EXT_VEH_CATEGORIA_TEST
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."EXT_VEH_CATEGORIA_TEST" 
   (	"CODMARCA" CHAR(4), 
	"MARCA" CHAR(40), 
	"CODMODELO" CHAR(8), 
	"MODELO" CHAR(40), 
	"CODCLASE" CHAR(4), 
	"CODTIPO" CHAR(7), 
	"TIPO" CHAR(30), 
	"CODIGO" CHAR(4), 
	"NOMBRE" CHAR(30)
   ); 
--   ORGANIZATION EXTERNAL 
--    ( TYPE ORACLE_LOADER
--      DEFAULT DIRECTORY "GIFOLE_LOAD_DIR"
--      ACCESS PARAMETERS
--      ( FIELDS (	
--  CODMARCA position (1:4),
--  MARCA position (5:44),
--	CODMODELO position (45:52),
--  MODELO position (53:92),
--  CODCLASE position (130:133),
--  CODTIPO position (93:99),
--  TIPO position (100:129),
--  CODIGO position (164:167),
--	NOMBRE position (168:197)
--    ) 
--               )
--      LOCATION
--       ( 'categoria.txt'
--       )
--    );
--------------------------------------------------------
--  DDL for Table EXT_VEH_CLIENTE
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."EXT_VEH_CLIENTE" 
   (	"TIP_DOC" CHAR(1), 
	"DOCUMENTO" CHAR(10), 
	"CLIENTE" CHAR(8), 
	"TIER" CHAR(1)
   ); 
--   ORGANIZATION EXTERNAL 
--    ( TYPE ORACLE_LOADER
--      DEFAULT DIRECTORY "GIFOLE_LOAD_DIR"
--      ACCESS PARAMETERS
--      ( FIELDS (	
--  TIP_DOC position (1),
--  DOCUMENTO position (2:11),
--	CLIENTE position (12:19),
--  TIER position (20)
--    ) 
--           )
--      LOCATION
--       ( 'cliente_ant.txt'
--       )
--    );
  GRANT SELECT ON "GIFOLE"."EXT_VEH_CLIENTE" TO "APP_GIFOLE";
  GRANT ALTER ON "GIFOLE"."EXT_VEH_CLIENTE" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table EXT_VEH_DESCUENTO_CAPA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."EXT_VEH_DESCUENTO_CAPA" 
   (	"ID" CHAR(12), 
	"CODIGO_CAPA" CHAR(4), 
	"FEC_INI_VIG" CHAR(10), 
	"FEC_FIN_VIG" CHAR(10), 
	"TIPO_FACTOR" CHAR(1), 
	"TIPO_TASA" CHAR(1), 
	"VALOR_TASA" CHAR(12)
   ); 
--   ORGANIZATION EXTERNAL 
--    ( TYPE ORACLE_LOADER
--      DEFAULT DIRECTORY "GIFOLE_LOAD_DIR"
--      ACCESS PARAMETERS
--      ( RECORDS DELIMITED BY NEWLINE 
--	 CHARACTERSET UTF8
--	 SKIP 0    
--	 BADFILE '%a_%p.bad' 
--	 LOGFILE '%a_%p.log' 
--	 FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '"'  LRTRIM  
--	 MISSING FIELD VALUES ARE NULL  
--	 REJECT ROWS WITH ALL NULL FIELDS 
--    ( ID CHAR(12),
--	CODIGO_CAPA CHAR(4),
--	FEC_INI_VIG CHAR(10),
--	FEC_FIN_VIG CHAR(10),
--	TIPO_FACTOR CHAR(1),
--	TIPO_TASA CHAR(1),
--	VALOR_TASA CHAR(12)
--    ) 
--           )
--      LOCATION
--       ( 'descuento_capa_ant.txt'
--       )
--    );
  GRANT SELECT ON "GIFOLE"."EXT_VEH_DESCUENTO_CAPA" TO "APP_GIFOLE";
  GRANT ALTER ON "GIFOLE"."EXT_VEH_DESCUENTO_CAPA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table EXT_VEH_PLAN
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."EXT_VEH_PLAN" 
   (	"CODIGO" CHAR(4), 
	"DESCRIPCION" CHAR(40), 
	"ESTILO" CHAR(30), 
	"FACTOR_BANCARIO" CHAR(1), 
	"MONTO_MINIMO" CHAR(12), 
	"ESTADO" CHAR(1), 
	"ORDEN" CHAR(12)
   ); 
--   ORGANIZATION EXTERNAL 
--    ( TYPE ORACLE_LOADER
--      DEFAULT DIRECTORY "GIFOLE_LOAD_DIR"
--      ACCESS PARAMETERS
--      ( RECORDS DELIMITED BY NEWLINE 
--	 CHARACTERSET UTF8
--	 SKIP 0    
--	 BADFILE '%a_%p.bad' 
--	 LOGFILE '%a_%p.log' 
--	 FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '"'  LRTRIM  
--	 MISSING FIELD VALUES ARE NULL  
--	 REJECT ROWS WITH ALL NULL FIELDS 
--    (	CODIGO CHAR(4),
--	DESCRIPCION CHAR(40),
--	ESTILO CHAR(30),
--	FACTOR_BANCARIO CHAR(1),
--	MONTO_MINIMO CHAR(12),
--	ESTADO CHAR(1),
--	ORDEN CHAR(12)
--    ) 
--           )
--      LOCATION
--       ( 'plan_ant.txt'
--       )
--    );
  GRANT SELECT ON "GIFOLE"."EXT_VEH_PLAN" TO "APP_GIFOLE";
  GRANT ALTER ON "GIFOLE"."EXT_VEH_PLAN" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table EXT_VEH_PLAN_BENEFICIO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."EXT_VEH_PLAN_BENEFICIO" 
   (	"ID" CHAR(12), 
	"BENEFICIO" CHAR(12), 
	"PLAN" CHAR(4), 
	"DETALLE" CHAR(100)
   ); 
--   ORGANIZATION EXTERNAL 
--    ( TYPE ORACLE_LOADER
--      DEFAULT DIRECTORY "GIFOLE_LOAD_DIR"
--      ACCESS PARAMETERS
--      ( RECORDS DELIMITED BY NEWLINE 
--	 CHARACTERSET UTF8
--	 SKIP 0    
--	 BADFILE '%a_%p.bad' 
--	 LOGFILE '%a_%p.log' 
--	 FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '"'  LRTRIM  
--	 MISSING FIELD VALUES ARE NULL  
--	 REJECT ROWS WITH ALL NULL FIELDS 
--    (	ID CHAR(12),
--	BENEFICIO CHAR(12),
--	PLAN CHAR(4),
--	DETALLE CHAR(100)
--    ) 
--           )
--      LOCATION
--       ( 'plan_beneficio_ant.txt'
--       )
--    );
  GRANT SELECT ON "GIFOLE"."EXT_VEH_PLAN_BENEFICIO" TO "APP_GIFOLE";
  GRANT ALTER ON "GIFOLE"."EXT_VEH_PLAN_BENEFICIO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table EXT_VEH_PRIMA_MINIMA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."EXT_VEH_PRIMA_MINIMA" 
   (	"CODCATEGORIA" CHAR(4), 
	"CODPLAN" CHAR(2), 
	"MONTO" CHAR(15), 
	"DIVISA" CHAR(3)
   ); 
--   ORGANIZATION EXTERNAL 
--    ( TYPE ORACLE_LOADER
--      DEFAULT DIRECTORY "GIFOLE_LOAD_DIR"
--      ACCESS PARAMETERS
--      ( FIELDS (	
--  CODCATEGORIA position (1:4),
--  CODPLAN position (5:6),
--	MONTO position (7:21),
--  DIVISA position (22:24)
--    ) 
--           )
--      LOCATION
--       ( 'prima_minima_ant.txt'
--       )
--    );
  GRANT SELECT ON "GIFOLE"."EXT_VEH_PRIMA_MINIMA" TO "APP_GIFOLE";
  GRANT ALTER ON "GIFOLE"."EXT_VEH_PRIMA_MINIMA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table EXT_VEH_SUNARP
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."EXT_VEH_SUNARP" 
   (	"NUM_PLACA_VIG" NVARCHAR2(10), 
	"NUM_PLACA_NO_VIG" NVARCHAR2(10), 
	"MARCA" NVARCHAR2(4), 
	"MODELO" NVARCHAR2(8), 
	"CLASE" NVARCHAR2(4), 
	"TIPO" NVARCHAR2(7), 
	"ANHO_FABRICACION" NVARCHAR2(4)
   ); 
--   ORGANIZATION EXTERNAL 
--    ( TYPE ORACLE_LOADER
--      DEFAULT DIRECTORY "GIFOLE_LOAD_DIR"
--      ACCESS PARAMETERS
--      ( RECORDS DELIMITED BY NEWLINE
--      FIELDS (NUM_PLACA_VIG position (01:10),
--              NUM_PLACA_NO_VIG position (11:20),
--              MARCA position (124:127),
--              MODELO position (116:123),
--              CLASE position (128:131),
--              TIPO position (132:138),
--              ANHO_FABRICACION position (139:142)
--             )
--             )
--      LOCATION
--       ( 'sunarp_ant.txt'
--       )
--    )
--   REJECT LIMIT UNLIMITED;
  GRANT SELECT ON "GIFOLE"."EXT_VEH_SUNARP" TO "APP_GIFOLE";
  GRANT ALTER ON "GIFOLE"."EXT_VEH_SUNARP" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table EXT_VEH_TARIFA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."EXT_VEH_TARIFA" 
   (	"CATEGORIA" CHAR(4), 
	"PLAN" CHAR(4), 
	"CODIGO_USO" CHAR(4), 
	"ANHO_ANTIGUEDAD" CHAR(4), 
	"CODIGO_UBICACION" CHAR(1), 
	"TASA_MES" CHAR(8), 
	"TASA_ANUAL" CHAR(8)
   ); 
--   ORGANIZATION EXTERNAL 
--    ( TYPE ORACLE_LOADER
--      DEFAULT DIRECTORY "GIFOLE_LOAD_DIR"
--      ACCESS PARAMETERS
--      ( FIELDS (	
--CATEGORIA position (1:4),
--	PLAN position (5:8),
--  CODIGO_USO position (9:12),
--  ANHO_ANTIGUEDAD position (63:66),
--  CODIGO_UBICACION position (67:67),
--  TASA_MES position (68:75),
--	TASA_ANUAL position (76:83)
--    ) 
--           )
--      LOCATION
--       ( 'tarifa_ant.txt'
--       )
--    );
  GRANT SELECT ON "GIFOLE"."EXT_VEH_TARIFA" TO "APP_GIFOLE";
  GRANT ALTER ON "GIFOLE"."EXT_VEH_TARIFA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table PARAMETRO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."PARAMETRO" 
   (	"ID" NUMBER(16,0), 
	"CODIGO" NVARCHAR2(50), 
	"NOMBRE" NVARCHAR2(50), 
	"DESCRIPCION" NVARCHAR2(200), 
	"VALOR" NVARCHAR2(200), 
	"TIPO" NVARCHAR2(80), 
	"ESTADO" CHAR(1) DEFAULT 'A', 
	"ORDEN" NUMBER(16,0) DEFAULT 1, 
	"CREADOR" NUMBER(16,0), 
	"CREACION" TIMESTAMP (6), 
	"EDITOR" NUMBER(16,0), 
	"EDICION" TIMESTAMP (6), 
	"FLAG_ADJUNTAR_ARCHIVO" NVARCHAR2(1)
   );
  GRANT UPDATE ON "GIFOLE"."PARAMETRO" TO "APP_GIFPRI";
  GRANT SELECT ON "GIFOLE"."PARAMETRO" TO "APP_GIFPRI";
  GRANT INSERT ON "GIFOLE"."PARAMETRO" TO "APP_GIFPRI";
  GRANT DELETE ON "GIFOLE"."PARAMETRO" TO "APP_GIFPRI";
  GRANT UPDATE ON "GIFOLE"."PARAMETRO" TO "GIFPRI";
  GRANT SELECT ON "GIFOLE"."PARAMETRO" TO "GIFPRI";
  GRANT INSERT ON "GIFOLE"."PARAMETRO" TO "GIFPRI";
  GRANT DELETE ON "GIFOLE"."PARAMETRO" TO "GIFPRI";
  GRANT UPDATE ON "GIFOLE"."PARAMETRO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."PARAMETRO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."PARAMETRO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."PARAMETRO" TO "APP_GIFOLE";
  
  UPDATE GIFOLE.PARAMETRO SET CREACION = SYSDATE;
  UPDATE GIFOLE.PARAMETRO SET EDICION = SYSDATE;
 
 
 
 
 
 
 
 
 
 
 
--------------------------------------------------------
--  DDL for Table PERSONA_MUNDO_SUELDO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."PERSONA_MUNDO_SUELDO" 
   (	"ID" NUMBER(16,0), 
	"NOMBRE" NVARCHAR2(50), 
	"APELLIDO" NVARCHAR2(50), 
	"TIPO_DOCUMENTO" NVARCHAR2(20), 
	"DOCUMENTO" NVARCHAR2(20), 
	"DEPARTAMENTO" NVARCHAR2(20), 
	"CORREO" NVARCHAR2(80), 
	"TELEFONO" NVARCHAR2(20), 
	"HORARIO" NVARCHAR2(20), 
	"PROCESADO" CHAR(1), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"AUTORIZACION" CHAR(1)
   );
  GRANT UPDATE ON "GIFOLE"."PERSONA_MUNDO_SUELDO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."PERSONA_MUNDO_SUELDO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."PERSONA_MUNDO_SUELDO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."PERSONA_MUNDO_SUELDO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table PRESTAMO_AL_TOQUE
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."PRESTAMO_AL_TOQUE" 
   (	"ID" NUMBER, 
	"CODIGO_CENTRAL" NVARCHAR2(8), 
	"TIPO_DOCUMENTO" NVARCHAR2(20), 
	"NUMERO_DOCUMENTO" NVARCHAR2(20), 
	"NOMBRE_COMPLETO" NVARCHAR2(200), 
	"TELEFONO" NVARCHAR2(20), 
	"CORREO" NVARCHAR2(100), 
	"MONTO" NVARCHAR2(20), 
	"CUOTA" NVARCHAR2(20), 
	"PLAZO" NVARCHAR2(20), 
	"TEA" NVARCHAR2(10), 
	"TCEA" NVARCHAR2(10), 
	"SIMULACION" NVARCHAR2(10), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"CANAL" NVARCHAR2(10), 
	"ESTADO" NVARCHAR2(20)
   );
  GRANT UPDATE ON "GIFOLE"."PRESTAMO_AL_TOQUE" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."PRESTAMO_AL_TOQUE" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."PRESTAMO_AL_TOQUE" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."PRESTAMO_AL_TOQUE" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table PRESTAMO_HIPOTECARIO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."PRESTAMO_HIPOTECARIO" 
   (	"ID" NUMBER(16,0), 
	"NOMBRE" NVARCHAR2(50), 
	"APELLIDO" NVARCHAR2(50), 
	"TIPO_DOCUMENTO" NVARCHAR2(20), 
	"DOCUMENTO" NVARCHAR2(20), 
	"DEPARTAMENTO" NVARCHAR2(20), 
	"CORREO" NVARCHAR2(80), 
	"TELEFONO" NVARCHAR2(20), 
	"HORARIO" NVARCHAR2(20), 
	"SITUACION_LABORAL" NVARCHAR2(20), 
	"TIPO" NVARCHAR2(20), 
	"PROCESADO" CHAR(1), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"AUTORIZACION" CHAR(1)
   );
  GRANT UPDATE ON "GIFOLE"."PRESTAMO_HIPOTECARIO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."PRESTAMO_HIPOTECARIO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."PRESTAMO_HIPOTECARIO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."PRESTAMO_HIPOTECARIO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table PRESTAMO_HIPOTECARIO_PRUEBA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."PRESTAMO_HIPOTECARIO_PRUEBA" 
   (	"ID" NUMBER(16,0), 
	"NOMBRE" NVARCHAR2(50), 
	"APELLIDO" NVARCHAR2(50), 
	"TIPO_DOCUMENTO" NVARCHAR2(20), 
	"DOCUMENTO" NVARCHAR2(20), 
	"DEPARTAMENTO" NVARCHAR2(20), 
	"CORREO" NVARCHAR2(80), 
	"TELEFONO" NVARCHAR2(20), 
	"HORARIO" NVARCHAR2(20), 
	"SITUACION_LABORAL" NVARCHAR2(20), 
	"TIPO" NVARCHAR2(20), 
	"PROCESADO" CHAR(1), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"AUTORIZACION" CHAR(1), 
	"USUARIO" VARCHAR2(20)
   );
  GRANT UPDATE ON "GIFOLE"."PRESTAMO_HIPOTECARIO_PRUEBA" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."PRESTAMO_HIPOTECARIO_PRUEBA" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."PRESTAMO_HIPOTECARIO_PRUEBA" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."PRESTAMO_HIPOTECARIO_PRUEBA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table QTZ_BLOB_TRIGGERS
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."QTZ_BLOB_TRIGGERS" 
   (	"SCHED_NAME" VARCHAR2(120), 
	"TRIGGER_NAME" VARCHAR2(200), 
	"TRIGGER_GROUP" VARCHAR2(200), 
	"BLOB_DATA" BLOB
   );
  GRANT UPDATE ON "GIFOLE"."QTZ_BLOB_TRIGGERS" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."QTZ_BLOB_TRIGGERS" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."QTZ_BLOB_TRIGGERS" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."QTZ_BLOB_TRIGGERS" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table QTZ_CALENDARS
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."QTZ_CALENDARS" 
   (	"SCHED_NAME" VARCHAR2(120), 
	"CALENDAR_NAME" VARCHAR2(200), 
	"CALENDAR" BLOB
   );
  GRANT UPDATE ON "GIFOLE"."QTZ_CALENDARS" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."QTZ_CALENDARS" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."QTZ_CALENDARS" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."QTZ_CALENDARS" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table QTZ_CRON_TRIGGERS
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."QTZ_CRON_TRIGGERS" 
   (	"SCHED_NAME" VARCHAR2(120), 
	"TRIGGER_NAME" VARCHAR2(200), 
	"TRIGGER_GROUP" VARCHAR2(200), 
	"CRON_EXPRESSION" VARCHAR2(120), 
	"TIME_ZONE_ID" VARCHAR2(80)
   );
  GRANT UPDATE ON "GIFOLE"."QTZ_CRON_TRIGGERS" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."QTZ_CRON_TRIGGERS" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."QTZ_CRON_TRIGGERS" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."QTZ_CRON_TRIGGERS" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table QTZ_FIRED_TRIGGERS
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."QTZ_FIRED_TRIGGERS" 
   (	"SCHED_NAME" VARCHAR2(120), 
	"ENTRY_ID" VARCHAR2(95), 
	"TRIGGER_NAME" VARCHAR2(200), 
	"TRIGGER_GROUP" VARCHAR2(200), 
	"INSTANCE_NAME" VARCHAR2(200), 
	"FIRED_TIME" NUMBER(13,0), 
	"SCHED_TIME" NUMBER(13,0), 
	"PRIORITY" NUMBER(13,0), 
	"STATE" VARCHAR2(16), 
	"JOB_NAME" VARCHAR2(200), 
	"JOB_GROUP" VARCHAR2(200), 
	"IS_NONCONCURRENT" VARCHAR2(1), 
	"REQUESTS_RECOVERY" VARCHAR2(1)
   );
  GRANT UPDATE ON "GIFOLE"."QTZ_FIRED_TRIGGERS" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."QTZ_FIRED_TRIGGERS" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."QTZ_FIRED_TRIGGERS" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."QTZ_FIRED_TRIGGERS" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table QTZ_JOB_DETAILS
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."QTZ_JOB_DETAILS" 
   (	"SCHED_NAME" VARCHAR2(120), 
	"JOB_NAME" VARCHAR2(200), 
	"JOB_GROUP" VARCHAR2(200), 
	"DESCRIPTION" VARCHAR2(250), 
	"JOB_CLASS_NAME" VARCHAR2(250), 
	"IS_DURABLE" VARCHAR2(1), 
	"IS_NONCONCURRENT" VARCHAR2(1), 
	"IS_UPDATE_DATA" VARCHAR2(1), 
	"REQUESTS_RECOVERY" VARCHAR2(1), 
	"JOB_DATA" BLOB
   );
  GRANT UPDATE ON "GIFOLE"."QTZ_JOB_DETAILS" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."QTZ_JOB_DETAILS" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."QTZ_JOB_DETAILS" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."QTZ_JOB_DETAILS" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table QTZ_LOCKS
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."QTZ_LOCKS" 
   (	"SCHED_NAME" VARCHAR2(120), 
	"LOCK_NAME" VARCHAR2(40)
   );
  GRANT UPDATE ON "GIFOLE"."QTZ_LOCKS" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."QTZ_LOCKS" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."QTZ_LOCKS" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."QTZ_LOCKS" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table QTZ_PAUSED_TRIGGER_GRPS
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."QTZ_PAUSED_TRIGGER_GRPS" 
   (	"SCHED_NAME" VARCHAR2(120), 
	"TRIGGER_GROUP" VARCHAR2(200)
   );
  GRANT UPDATE ON "GIFOLE"."QTZ_PAUSED_TRIGGER_GRPS" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."QTZ_PAUSED_TRIGGER_GRPS" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."QTZ_PAUSED_TRIGGER_GRPS" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."QTZ_PAUSED_TRIGGER_GRPS" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table QTZ_SCHEDULER_STATE
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."QTZ_SCHEDULER_STATE" 
   (	"SCHED_NAME" VARCHAR2(120), 
	"INSTANCE_NAME" VARCHAR2(200), 
	"LAST_CHECKIN_TIME" NUMBER(13,0), 
	"CHECKIN_INTERVAL" NUMBER(13,0)
   );
  GRANT UPDATE ON "GIFOLE"."QTZ_SCHEDULER_STATE" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."QTZ_SCHEDULER_STATE" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."QTZ_SCHEDULER_STATE" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."QTZ_SCHEDULER_STATE" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table QTZ_SIMPLE_TRIGGERS
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."QTZ_SIMPLE_TRIGGERS" 
   (	"SCHED_NAME" VARCHAR2(120), 
	"TRIGGER_NAME" VARCHAR2(200), 
	"TRIGGER_GROUP" VARCHAR2(200), 
	"REPEAT_COUNT" NUMBER(7,0), 
	"REPEAT_INTERVAL" NUMBER(12,0), 
	"TIMES_TRIGGERED" NUMBER(10,0)
   );
  GRANT UPDATE ON "GIFOLE"."QTZ_SIMPLE_TRIGGERS" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."QTZ_SIMPLE_TRIGGERS" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."QTZ_SIMPLE_TRIGGERS" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."QTZ_SIMPLE_TRIGGERS" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table QTZ_SIMPROP_TRIGGERS
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."QTZ_SIMPROP_TRIGGERS" 
   (	"SCHED_NAME" VARCHAR2(120), 
	"TRIGGER_NAME" VARCHAR2(200), 
	"TRIGGER_GROUP" VARCHAR2(200), 
	"STR_PROP_1" VARCHAR2(512), 
	"STR_PROP_2" VARCHAR2(512), 
	"STR_PROP_3" VARCHAR2(512), 
	"INT_PROP_1" NUMBER(10,0), 
	"INT_PROP_2" NUMBER(10,0), 
	"LONG_PROP_1" NUMBER(13,0), 
	"LONG_PROP_2" NUMBER(13,0), 
	"DEC_PROP_1" NUMBER(13,4), 
	"DEC_PROP_2" NUMBER(13,4), 
	"BOOL_PROP_1" VARCHAR2(1), 
	"BOOL_PROP_2" VARCHAR2(1)
   );
  GRANT UPDATE ON "GIFOLE"."QTZ_SIMPROP_TRIGGERS" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."QTZ_SIMPROP_TRIGGERS" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."QTZ_SIMPROP_TRIGGERS" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."QTZ_SIMPROP_TRIGGERS" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table QTZ_TRACE
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."QTZ_TRACE" 
   (	"FIRE_INSTANCE_ID" VARCHAR2(80), 
	"TRIGGER_NAME" VARCHAR2(200), 
	"TRIGGER_GROUP" VARCHAR2(200), 
	"JOB_NAME" VARCHAR2(200), 
	"JOB_GROUP" VARCHAR2(200), 
	"AUTHOR" NUMBER(19,0), 
	"FIRE_TIME" TIMESTAMP (6), 
	"NEXT_FIRE_TIME" TIMESTAMP (6), 
	"PREVIOUS_FIRE_TIME" TIMESTAMP (6), 
	"SCHEDULED_FIRE_TIME" TIMESTAMP (6), 
	"REFIRE_COUNT" NUMBER(*,0)
   );
  GRANT UPDATE ON "GIFOLE"."QTZ_TRACE" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."QTZ_TRACE" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."QTZ_TRACE" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."QTZ_TRACE" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table QTZ_TRACE_STATE
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."QTZ_TRACE_STATE" 
   (	"FIRE_INSTANCE_ID" VARCHAR2(80), 
	"STATE_JOB" NVARCHAR2(30), 
	"STATE_TIME" TIMESTAMP (6), 
	"ERROR" CLOB
   );
  GRANT UPDATE ON "GIFOLE"."QTZ_TRACE_STATE" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."QTZ_TRACE_STATE" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."QTZ_TRACE_STATE" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."QTZ_TRACE_STATE" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table QTZ_TRIGGERS
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."QTZ_TRIGGERS" 
   (	"SCHED_NAME" VARCHAR2(120), 
	"TRIGGER_NAME" VARCHAR2(200), 
	"TRIGGER_GROUP" VARCHAR2(200), 
	"JOB_NAME" VARCHAR2(200), 
	"JOB_GROUP" VARCHAR2(200), 
	"DESCRIPTION" VARCHAR2(250), 
	"NEXT_FIRE_TIME" NUMBER(13,0), 
	"PREV_FIRE_TIME" NUMBER(13,0), 
	"PRIORITY" NUMBER(13,0), 
	"TRIGGER_STATE" VARCHAR2(16), 
	"TRIGGER_TYPE" VARCHAR2(8), 
	"START_TIME" NUMBER(13,0), 
	"END_TIME" NUMBER(13,0), 
	"CALENDAR_NAME" VARCHAR2(200), 
	"MISFIRE_INSTR" NUMBER(2,0), 
	"JOB_DATA" BLOB
   );
  GRANT UPDATE ON "GIFOLE"."QTZ_TRIGGERS" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."QTZ_TRIGGERS" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."QTZ_TRIGGERS" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."QTZ_TRIGGERS" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table RASPA_GANA_CARGA_HISTORIAL
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."RASPA_GANA_CARGA_HISTORIAL" 
   (	"ID" NUMBER, 
	"NOMBRE" NVARCHAR2(50), 
	"ESTADO" CHAR(1), 
	"ESTADO_CATEGORIA" CHAR(1), 
	"ERROR" NVARCHAR2(300), 
	"CREADOR" NUMBER(16,0), 
	"CREACION" TIMESTAMP (6), 
	"MODIFICACION" TIMESTAMP (6)
   ) ;

   COMMENT ON COLUMN "GIFOLE"."RASPA_GANA_CARGA_HISTORIAL"."ESTADO" IS 'S: Sin procesar, P: Procesado, E: No terminado';
   COMMENT ON COLUMN "GIFOLE"."RASPA_GANA_CARGA_HISTORIAL"."ESTADO_CATEGORIA" IS 'A: Activado';
  GRANT UPDATE ON "GIFOLE"."RASPA_GANA_CARGA_HISTORIAL" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."RASPA_GANA_CARGA_HISTORIAL" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."RASPA_GANA_CARGA_HISTORIAL" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."RASPA_GANA_CARGA_HISTORIAL" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table SEGURO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SEGURO" 
   (	"ID" NUMBER(16,0), 
	"NOMBRE" NVARCHAR2(50), 
	"APELLIDO" NVARCHAR2(50), 
	"TIPO_DOCUMENTO" NVARCHAR2(20), 
	"DOCUMENTO" NVARCHAR2(20), 
	"DEPARTAMENTO" NVARCHAR2(20), 
	"CORREO" NVARCHAR2(80), 
	"TELEFONO" NVARCHAR2(20), 
	"HORARIO" NVARCHAR2(20), 
	"FECHA_REGISTRO" TIMESTAMP (0), 
	"AUTORIZACION" CHAR(1)
   ) ;

   COMMENT ON TABLE "GIFOLE"."SEGURO"  IS 'Contendr� los datos del ciente captados por los leads de bbva zona publica';
  GRANT UPDATE ON "GIFOLE"."SEGURO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."SEGURO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."SEGURO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."SEGURO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table SEGURO_PROTECCION_TARJETA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SEGURO_PROTECCION_TARJETA" 
   (	"ID" NUMBER(16,2), 
	"NUMERO_CERTIFICADO" NVARCHAR2(30), 
	"FECHA_EMISION" TIMESTAMP (6), 
	"FECHA_INICIO_SEGURO" TIMESTAMP (6), 
	"OFICINA_GESTORA" NVARCHAR2(10), 
	"COD_VENDEDOR" NVARCHAR2(20), 
	"NUMERO_CTATARJETA_CARGO" NVARCHAR2(30), 
	"NUMERO_TARJETA_ASEGURADA" NVARCHAR2(30), 
	"NOMBRE" NVARCHAR2(80), 
	"TIPO_DOCUMENTO" NVARCHAR2(20), 
	"DOCUMENTO" NVARCHAR2(20), 
	"FECHA_NACIMIENTO" TIMESTAMP (6), 
	"SEXO" NVARCHAR2(20), 
	"CORREO" NVARCHAR2(80), 
	"ESTADO_CIVIL" NVARCHAR2(20), 
	"TELEFONO" NVARCHAR2(20), 
	"DIRECCION" NVARCHAR2(150), 
	"DIRECCION_OPCIONAL" NVARCHAR2(150), 
	"NOMBRE_TIT" NVARCHAR2(50), 
	"TIPO_DOCUMENTO_TIT" NVARCHAR2(20), 
	"DOCUMENTO_TIT" NVARCHAR2(20), 
	"FECHA_NACIMIENTO_TIT" TIMESTAMP (6), 
	"SEXO_TIT" NVARCHAR2(20), 
	"CORREO_TIT" NVARCHAR2(80), 
	"ESTADO_CIVIL_TIT" NVARCHAR2(20), 
	"TELEFONO_TIT" NVARCHAR2(20), 
	"DIRECCION_TIT" NVARCHAR2(150), 
	"DIRECCION_OPCIONAL_TIT" NVARCHAR2(150), 
	"PROCESADO" CHAR(1), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"MODALIDAD" VARCHAR2(60), 
	"PRIMA_SEGURO" VARCHAR2(10), 
	"MONEDA" VARCHAR2(10), 
	"FORMA_PAGO" VARCHAR2(20), 
	"LUGAR_FECHA_EMISION" VARCHAR2(50), 
	"CANAL" NVARCHAR2(8), 
	"NOMBRE_EMAIL" VARCHAR2(80)
   );
  GRANT UPDATE ON "GIFOLE"."SEGURO_PROTECCION_TARJETA" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."SEGURO_PROTECCION_TARJETA" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."SEGURO_PROTECCION_TARJETA" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."SEGURO_PROTECCION_TARJETA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table SEGURO_TIPO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SEGURO_TIPO" 
   (	"ID" NUMBER(16,0), 
	"TIPO" NUMBER(16,0), 
	"SEGURO" NUMBER(16,0), 
	"PROCESADO" CHAR(1)
   ) ;

   COMMENT ON TABLE "GIFOLE"."SEGURO_TIPO"  IS 'Registra los seguros en el cual est� interesado el cliente';
  GRANT UPDATE ON "GIFOLE"."SEGURO_TIPO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."SEGURO_TIPO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."SEGURO_TIPO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."SEGURO_TIPO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table SEGURO_VEHICULAR
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SEGURO_VEHICULAR" 
   (	"ID" NUMBER(16,0), 
	"NOMBRE" NVARCHAR2(50), 
	"APELLIDO" NVARCHAR2(50), 
	"TIPO_DOCUMENTO" NVARCHAR2(20), 
	"DOCUMENTO" NVARCHAR2(20), 
	"DEPARTAMENTO" NVARCHAR2(20), 
	"CORREO" NVARCHAR2(80), 
	"TELEFONO" NVARCHAR2(20), 
	"HORARIO" NVARCHAR2(20), 
	"ESTADO_PLACA" NVARCHAR2(20), 
	"PLACA" NVARCHAR2(20), 
	"PROCESADO" CHAR(1), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"AUTORIZACION" CHAR(1), 
	"ANHO_FABRICACION" NVARCHAR2(4)
   );
  GRANT UPDATE ON "GIFOLE"."SEGURO_VEHICULAR" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."SEGURO_VEHICULAR" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."SEGURO_VEHICULAR" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."SEGURO_VEHICULAR" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table SEQ_ENTIDAD
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SEQ_ENTIDAD" 
   (	"ENTIDAD" NVARCHAR2(250), 
	"ULTIMO_ID" NUMBER(16,0)
   );
  GRANT UPDATE ON "GIFOLE"."SEQ_ENTIDAD" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."SEQ_ENTIDAD" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."SEQ_ENTIDAD" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."SEQ_ENTIDAD" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table SIS_AUDITORIA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SIS_AUDITORIA" 
   (	"ID" NUMBER(16,0), 
	"TABLA" NVARCHAR2(200), 
	"TIPO" CHAR(1), 
	"DATA" NVARCHAR2(2000), 
	"USUARIO" NVARCHAR2(100), 
	"FECHA" TIMESTAMP (6), 
	"ID_REGISTRO" NVARCHAR2(200)
   );
  GRANT UPDATE ON "GIFOLE"."SIS_AUDITORIA" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."SIS_AUDITORIA" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."SIS_AUDITORIA" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."SIS_AUDITORIA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table SIS_ERROR
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SIS_ERROR" 
   (	"ID" NUMBER, 
	"CLASE" NVARCHAR2(200), 
	"METODO" NVARCHAR2(200), 
	"TRAMA" NVARCHAR2(2000), 
	"ERROR" NVARCHAR2(2000), 
	"CREADOR" NVARCHAR2(10), 
	"FECHA_REGISTRO" TIMESTAMP (6)
   );
  GRANT UPDATE ON "GIFOLE"."SIS_ERROR" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."SIS_ERROR" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."SIS_ERROR" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."SIS_ERROR" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table SIS_ERROR_Q
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SIS_ERROR_Q" 
   (	"ID" NUMBER, 
	"CLASE" NVARCHAR2(200), 
	"METODO" NVARCHAR2(200), 
	"TRAMA" NVARCHAR2(2000), 
	"ERROR" NVARCHAR2(2000), 
	"CREADOR" NVARCHAR2(10), 
	"FECHA_REGISTRO" TIMESTAMP (6)
   );
--------------------------------------------------------
--  DDL for Table SIS_ITEM
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SIS_ITEM" 
   (	"ID" NUMBER(16,0), 
	"DESCRIPCION" NVARCHAR2(50), 
	"ITEM" NUMBER(16,0), 
	"ESTADO" CHAR(1)
   );
  GRANT UPDATE ON "GIFOLE"."SIS_ITEM" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."SIS_ITEM" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."SIS_ITEM" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."SIS_ITEM" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table SIS_PERFIL
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SIS_PERFIL" 
   (	"ID" NUMBER(16,0), 
	"DESCRIPCION" NVARCHAR2(30), 
	"ESTADO" CHAR(1)
   );
  GRANT UPDATE ON "GIFOLE"."SIS_PERFIL" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."SIS_PERFIL" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."SIS_PERFIL" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."SIS_PERFIL" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table SIS_PERFIL_ITEM
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SIS_PERFIL_ITEM" 
   (	"ID" NUMBER(16,0), 
	"PERFIL" NUMBER(16,0), 
	"ITEM" NUMBER(16,0), 
	"ESTADO" CHAR(1)
   );
  GRANT UPDATE ON "GIFOLE"."SIS_PERFIL_ITEM" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."SIS_PERFIL_ITEM" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."SIS_PERFIL_ITEM" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."SIS_PERFIL_ITEM" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table SUSCRIPCION_PROGRAMADA_FFMM
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SUSCRIPCION_PROGRAMADA_FFMM" 
   (	"ID" NUMBER(16,0), 
	"NOMBRE" NVARCHAR2(150), 
	"CODIGO_CENTRAL" NVARCHAR2(8), 
	"CORREO" NVARCHAR2(80), 
	"NOMBRE_FONDO" NVARCHAR2(50), 
	"NUMERO_FONDO" NVARCHAR2(25), 
	"NOMBRE_CUENTA" NVARCHAR2(50), 
	"NUMERO_CUENTA" NVARCHAR2(25), 
	"PERIODO" NVARCHAR2(20), 
	"APORTE_PROGRAMADO" NUMBER(15,2), 
	"DIVISA" NVARCHAR2(20), 
	"PROCESADO" CHAR(1), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"DIACARGO" NVARCHAR2(2)
   );
  GRANT UPDATE ON "GIFOLE"."SUSCRIPCION_PROGRAMADA_FFMM" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."SUSCRIPCION_PROGRAMADA_FFMM" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."SUSCRIPCION_PROGRAMADA_FFMM" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."SUSCRIPCION_PROGRAMADA_FFMM" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table SUSCRIPCION_PUB_FFMM
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SUSCRIPCION_PUB_FFMM" 
   (	"ID" NUMBER(16,0), 
	"NOMBRE" NVARCHAR2(50), 
	"APELLIDO" NVARCHAR2(50), 
	"TIPO_DOCUMENTO" NVARCHAR2(20), 
	"DOCUMENTO" NVARCHAR2(20), 
	"DEPARTAMENTO" NVARCHAR2(20), 
	"CORREO" NVARCHAR2(80), 
	"TELEFONO" NVARCHAR2(20), 
	"HORARIO" NVARCHAR2(20), 
	"TIPO_CLIENTE" CHAR(1), 
	"SUSCRIPCION" CHAR(1), 
	"AFILIACION_EECC" CHAR(1), 
	"OBJETIVO" NVARCHAR2(30), 
	"TIPO_CALCULO" CHAR(1), 
	"DIVISA_APORTE" NVARCHAR2(10), 
	"MONTO" NUMBER(15,2), 
	"PLAZO" NUMBER(*,0), 
	"PROCESADO" CHAR(1), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"AUTORIZACION" CHAR(1), 
	"TELEFONO2" VARCHAR2(40)
   ) ;

   COMMENT ON TABLE "GIFOLE"."SUSCRIPCION_PUB_FFMM"  IS 'Tabla que almancenar� la simulaci�n de los usuarios de la zona p�blica';
  GRANT UPDATE ON "GIFOLE"."SUSCRIPCION_PUB_FFMM" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."SUSCRIPCION_PUB_FFMM" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."SUSCRIPCION_PUB_FFMM" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."SUSCRIPCION_PUB_FFMM" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table SUSCRIPCION_PUB_FFMM_DETALLE
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."SUSCRIPCION_PUB_FFMM_DETALLE" 
   (	"ID" NUMBER(16,0), 
	"PERIODO" NVARCHAR2(20), 
	"MONTO_FUTURO" NUMBER(15,2), 
	"MONTO_CUOTA" NUMBER(15,2), 
	"MONTO_RENTABILIDAD" NUMBER(15,2), 
	"SUSCRIPCION_PUB_FFMM" NUMBER(16,0)
   ) ;

   COMMENT ON TABLE "GIFOLE"."SUSCRIPCION_PUB_FFMM_DETALLE"  IS 'Detalle de la simulacion';
  GRANT UPDATE ON "GIFOLE"."SUSCRIPCION_PUB_FFMM_DETALLE" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."SUSCRIPCION_PUB_FFMM_DETALLE" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."SUSCRIPCION_PUB_FFMM_DETALLE" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."SUSCRIPCION_PUB_FFMM_DETALLE" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table TC_ADICIONAL
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."TC_ADICIONAL" 
   (	"ID" NUMBER(16,0), 
	"CODIGO_CENTRAL" NVARCHAR2(10), 
	"NOMBRE" NVARCHAR2(100), 
	"TARJETA_TITULAR" NVARCHAR2(20), 
	"NOMBRE_TARJETA" NVARCHAR2(50), 
	"CORREO" NVARCHAR2(80), 
	"DIRECCION" NVARCHAR2(250), 
	"CANAL" NVARCHAR2(8), 
	"DIVISA" NVARCHAR2(10), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"OFICINA_SOLICITANTE" NVARCHAR2(250), 
	"LOCALIDAD" NVARCHAR2(10), 
	"OFICINA_RECOJO" NVARCHAR2(300)
   );
  GRANT UPDATE ON "GIFOLE"."TC_ADICIONAL" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."TC_ADICIONAL" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."TC_ADICIONAL" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."TC_ADICIONAL" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table TC_ADICIONAL_DETALLE
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."TC_ADICIONAL_DETALLE" 
   (	"ID" NUMBER(16,0), 
	"TC_ADICIONAL" NUMBER(16,0), 
	"NOMBRES" NVARCHAR2(40), 
	"APELLIDO_PATERNO" NVARCHAR2(40), 
	"APELLIDO_MATERNO" NVARCHAR2(40), 
	"TIPO_DOCUMENTO" NVARCHAR2(30), 
	"DOCUMENTO" NVARCHAR2(30), 
	"ESTADO_CIVIL" NVARCHAR2(30), 
	"VINCULO" NVARCHAR2(30), 
	"OCUPACION" NVARCHAR2(60), 
	"LIMITE_CREDITO" NUMBER(*,0), 
	"NUMERO_CONTACTO" NVARCHAR2(20), 
	"CORREO" NVARCHAR2(80), 
	"COD_CLIENTE" NVARCHAR2(10), 
	"TIPO_CLIENTE" CHAR(1), 
	"NRO_TARJETA" NVARCHAR2(20), 
	"ESTADO_ACTUAL" NUMBER, 
	"FECHA_MODIFICACION" TIMESTAMP (6), 
	"NRO_CONTRATO" NVARCHAR2(20), 
	"FLAG_PROCESAR" NUMBER, 
	"DIRECCION_ENTREGA" NVARCHAR2(250), 
	"OFICINA_RECOJO" NVARCHAR2(300)
   );
  GRANT UPDATE ON "GIFOLE"."TC_ADICIONAL_DETALLE" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."TC_ADICIONAL_DETALLE" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."TC_ADICIONAL_DETALLE" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."TC_ADICIONAL_DETALLE" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table TC_ADICIONAL_DET_ESTADO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."TC_ADICIONAL_DET_ESTADO" 
   (	"ID" NUMBER(16,0), 
	"TC_ADICIONAL_DETALLE" NUMBER(16,0), 
	"TC_ESTADO" NUMBER, 
	"TC_MOTIVO" NUMBER, 
	"OBSERVACION" NVARCHAR2(150), 
	"CREACION" TIMESTAMP (6), 
	"CREADOR" NVARCHAR2(20), 
	"EDICION" TIMESTAMP (6), 
	"EDITOR" NVARCHAR2(20)
   );
  GRANT UPDATE ON "GIFOLE"."TC_ADICIONAL_DET_ESTADO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."TC_ADICIONAL_DET_ESTADO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."TC_ADICIONAL_DET_ESTADO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."TC_ADICIONAL_DET_ESTADO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table TC_BENEFICIOS
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."TC_BENEFICIOS" 
   (	"ID" NUMBER, 
	"SALUDO" NVARCHAR2(100), 
	"TEXTO" NVARCHAR2(200), 
	"URL" NVARCHAR2(100), 
	"URL_MOSTRAR" NVARCHAR2(100), 
	"URL_TRACKING" NVARCHAR2(100), 
	"ESTADO" CHAR(1), 
	"CREADOR" NVARCHAR2(10), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"EDITOR" NVARCHAR2(10), 
	"FECHA_MODIFICACION" TIMESTAMP (6), 
	"FECHA_ACTIVACION" TIMESTAMP (6)
   );
  GRANT UPDATE ON "GIFOLE"."TC_BENEFICIOS" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."TC_BENEFICIOS" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."TC_BENEFICIOS" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."TC_BENEFICIOS" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table TC_BENEFICIOS_TARJETA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."TC_BENEFICIOS_TARJETA" 
   (	"ID" NUMBER, 
	"TARJETA" NVARCHAR2(30), 
	"BIN" NVARCHAR2(6), 
	"MARCA" NVARCHAR2(20), 
	"PROGRAMA_LEALTAD" NVARCHAR2(20), 
	"TCEA" NVARCHAR2(10), 
	"MEMBRESIA" NVARCHAR2(10), 
	"EECC" NVARCHAR2(10), 
	"SEG_DESGRAVAMEN" NVARCHAR2(10), 
	"CREADOR" NVARCHAR2(10), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"EDITOR" NVARCHAR2(10), 
	"FECHA_MODIFICACION" TIMESTAMP (6), 
	"ESTADO" CHAR(1)
   );
  GRANT UPDATE ON "GIFOLE"."TC_BENEFICIOS_TARJETA" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."TC_BENEFICIOS_TARJETA" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."TC_BENEFICIOS_TARJETA" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."TC_BENEFICIOS_TARJETA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table TC_ESTADO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."TC_ESTADO" 
   (	"ID" NUMBER, 
	"NOMBRE" NVARCHAR2(40), 
	"CREACION" TIMESTAMP (6), 
	"CREADOR" NVARCHAR2(20), 
	"OBSERVACION" NVARCHAR2(200), 
	"TEXTOPEDIDO" NVARCHAR2(35)
   );
  GRANT UPDATE ON "GIFOLE"."TC_ESTADO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."TC_ESTADO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."TC_ESTADO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."TC_ESTADO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table TC_MARCA_TARJETA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."TC_MARCA_TARJETA" 
   (	"ID" NUMBER, 
	"ID_TC_TARIFARIO" NUMBER, 
	"ID_MARCA_TARJETA" NUMBER, 
	"ESTADO" NUMBER
   );
--------------------------------------------------------
--  DDL for Table TC_MOTIVO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."TC_MOTIVO" 
   (	"ID" NUMBER, 
	"NOMBRE" NVARCHAR2(100), 
	"CREACION" TIMESTAMP (6), 
	"CREADOR" NVARCHAR2(20), 
	"TIPO" NVARCHAR2(5), 
	"ORDEN" NUMBER, 
	"ESTADO" CHAR(1)
   );
  GRANT UPDATE ON "GIFOLE"."TC_MOTIVO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."TC_MOTIVO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."TC_MOTIVO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."TC_MOTIVO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table TC_PROGRAMA_LEALTAD
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."TC_PROGRAMA_LEALTAD" 
   (	"ID" NUMBER, 
	"ID_TC_TARIFARIO" NUMBER, 
	"ID_PROGRAMA_LEALTAD" NUMBER, 
	"ESTADO" NUMBER
   );
--------------------------------------------------------
--  DDL for Table TC_TARIFARIO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."TC_TARIFARIO" 
   (	"ID" NUMBER, 
	"BIN" NVARCHAR2(6), 
	"ID_MARCA_TARJETA" NUMBER, 
	"ID_PROGRAMA_LEALTAD" NUMBER, 
	"RANGO_TCEA" NVARCHAR2(100), 
	"MEMBRESIA" NVARCHAR2(10), 
	"EECC" NVARCHAR2(10), 
	"SEG_DESGRAVAMEN" NVARCHAR2(10), 
	"ESTADO" NUMBER, 
	"IMAGEN" NVARCHAR2(2000), 
	"ORIENTACION" NUMBER, 
	"PRIORIDAD" NUMBER, 
	"OBSERVACION" NVARCHAR2(2000), 
	"DESCRIPCION" VARCHAR2(80), 
	"FLAG_TARJETA_HINCHA" NUMBER, 
	"IMAGEN_TARJETA_HINCHA" NVARCHAR2(2000), 
	"NOMBRE_TARJETA_HINCHA" VARCHAR2(80), 
	"ORIENTACION_TARJETA_HINCHA" NUMBER, 
	"BONO" NVARCHAR2(20), 
	"CONSUMO_BONO" NVARCHAR2(20), 
	"DIAS_BONO" NVARCHAR2(10), 
	"CONSUMO_EX_MEMBRESIA" NVARCHAR2(20)
   );
  GRANT UPDATE ON "GIFOLE"."TC_TARIFARIO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."TC_TARIFARIO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."TC_TARIFARIO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."TC_TARIFARIO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table TC_TOC
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."TC_TOC" 
   (	"ID" NUMBER(16,0), 
	"NOMBRES" NVARCHAR2(40), 
	"APELLIDO_PATERNO" NVARCHAR2(40), 
	"APELLIDO_MATERNO" NVARCHAR2(40), 
	"COD_CENTRAL" NVARCHAR2(8), 
	"COD_RESERVA_MOI" NVARCHAR2(30), 
	"TIPO_TARJETA" NVARCHAR2(20), 
	"PROGRAMA_BENEF" NVARCHAR2(40), 
	"CUENTA_CARGO" NVARCHAR2(20), 
	"FECHA_PAGO" NVARCHAR2(20), 
	"ENVIO_EC" NVARCHAR2(250), 
	"DIRECCION" NVARCHAR2(250), 
	"TELEFONO" NVARCHAR2(9), 
	"CORREO" NVARCHAR2(80), 
	"NRO_CONTRATO" NVARCHAR2(20), 
	"NRO_TARJETA" NVARCHAR2(16), 
	"LOCALIDAD" NVARCHAR2(10), 
	"ESTADO_ACTUAL" NUMBER, 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"CANAL" NVARCHAR2(8), 
	"VALORACION" NUMBER, 
	"FECHA_MODIFICACION" TIMESTAMP (6), 
	"CREADOR" NVARCHAR2(10), 
	"EDITOR" NVARCHAR2(10), 
	"TARJETA" NVARCHAR2(50), 
	"LINEA_CREDITO" NVARCHAR2(12), 
	"TC_ADICIONAL_DETALLE" NUMBER, 
	"FLAG_PROCESAR" NUMBER, 
	"OFICINA_SOLICITANTE" NVARCHAR2(250), 
	"EJECUTIVO" NVARCHAR2(100), 
	"OFICINA_RECOJO" NVARCHAR2(300), 
	"CORREO_EJECUTIVO" NVARCHAR2(100), 
	"PASAJERO_FRECUENTE" NVARCHAR2(11), 
	"BIN" NVARCHAR2(100), 
	"MULTIBIN" NVARCHAR2(100), 
	"OFERTA_DATAZO" NVARCHAR2(30), 
	"NRO_SEGURO" NVARCHAR2(20), 
	"TEA" NVARCHAR2(12)
   );
  GRANT UPDATE ON "GIFOLE"."TC_TOC" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."TC_TOC" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."TC_TOC" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."TC_TOC" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table TC_TOC_DET_ESTADO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."TC_TOC_DET_ESTADO" 
   (	"ID" NUMBER(16,0), 
	"TC_TOC" NUMBER(16,0), 
	"TC_ESTADO" NUMBER, 
	"TC_MOTIVO" NUMBER, 
	"OBSERVACION" NVARCHAR2(150), 
	"CREACION" TIMESTAMP (6), 
	"CREADOR" NVARCHAR2(20), 
	"EDICION" TIMESTAMP (6), 
	"EDITOR" NVARCHAR2(20)
   );
  GRANT UPDATE ON "GIFOLE"."TC_TOC_DET_ESTADO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."TC_TOC_DET_ESTADO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."TC_TOC_DET_ESTADO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."TC_TOC_DET_ESTADO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table TC_TOC_REMARKETING
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."TC_TOC_REMARKETING" 
   (	"ID" NUMBER, 
	"CODIGOCENTRAL" NVARCHAR2(20), 
	"TIPODOCUMENTO" NVARCHAR2(20), 
	"NUMERODOCUMENTO" NVARCHAR2(20), 
	"NOMBRECOMPLETO" NVARCHAR2(200), 
	"TELEFONO" NVARCHAR2(20), 
	"CORREO" NVARCHAR2(100), 
	"FECHAREGISTRO" TIMESTAMP (6), 
	"NOMBRETARJETA" NVARCHAR2(50), 
	"TIPOTARJETA" NVARCHAR2(50), 
	"LIMITETARJETA" NVARCHAR2(50), 
	"TEA" NVARCHAR2(20), 
	"TCEA" NVARCHAR2(20), 
	"TIPOINFORMACION" NVARCHAR2(100), 
	"CANAL" NVARCHAR2(50), 
	"ESTADO" NVARCHAR2(20), 
	"FECHAPAGO" NVARCHAR2(20)
   );
  GRANT ALTER ON "GIFOLE"."TC_TOC_REMARKETING" TO "APP_GIFOLE";
  GRANT UPDATE ON "GIFOLE"."TC_TOC_REMARKETING" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."TC_TOC_REMARKETING" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."TC_TOC_REMARKETING" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."TC_TOC_REMARKETING" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table USUARIO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."USUARIO" 
   (	"ID" NUMBER(16,0), 
	"REGISTRO" NVARCHAR2(7), 
	"NOMBRE" NVARCHAR2(50), 
	"PATERNO" NVARCHAR2(50), 
	"MATERNO" NVARCHAR2(50), 
	"PERFIL" NUMBER(16,0)
   );
  GRANT UPDATE ON "GIFOLE"."USUARIO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."USUARIO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."USUARIO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."USUARIO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_AUTOMAS
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_AUTOMAS" 
   (	"ID" NUMBER(16,0), 
	"MARCA" NVARCHAR2(4), 
	"MODELO" NVARCHAR2(8), 
	"ANHO_FABRICACION" NVARCHAR2(4), 
	"DIVISA" NVARCHAR2(3), 
	"VALOR_COMERCIAL" NUMBER(13,2)
   );
  GRANT UPDATE ON "GIFOLE"."VEH_AUTOMAS" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_AUTOMAS" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_AUTOMAS" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_AUTOMAS" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_BENEFICIO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_BENEFICIO" 
   (	"ID" NUMBER(16,0), 
	"DESCRIPCION" NVARCHAR2(100), 
	"TIPO" CHAR(1), 
	"ORDEN" NUMBER(*,0)
   );
  GRANT UPDATE ON "GIFOLE"."VEH_BENEFICIO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_BENEFICIO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_BENEFICIO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_BENEFICIO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_CATEGORIA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_CATEGORIA" 
   (	"ID" NUMBER(16,0), 
	"MARCA" NVARCHAR2(4), 
	"MODELO" NVARCHAR2(8), 
	"CLASE" NVARCHAR2(4), 
	"TIPO" NVARCHAR2(7), 
	"CODIGO" NVARCHAR2(4), 
	"NOMBRE" NVARCHAR2(30), 
	"ESTADO" CHAR(1), 
	"CREACION" TIMESTAMP (6), 
	"EDICION" TIMESTAMP (6)
   );
  GRANT UPDATE ON "GIFOLE"."VEH_CATEGORIA" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_CATEGORIA" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_CATEGORIA" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_CATEGORIA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_CLIENTE
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_CLIENTE" 
   (	"TIPO_DOCUMENTO" CHAR(1), 
	"DOCUMENTO" NVARCHAR2(20), 
	"CODIGO_CENTRAL" NVARCHAR2(8), 
	"CODIGO_CAPA" NVARCHAR2(4)
   );
  GRANT UPDATE ON "GIFOLE"."VEH_CLIENTE" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_CLIENTE" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_CLIENTE" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_CLIENTE" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_CLIENTE2
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_CLIENTE2" 
   (	"TIPO_DOCUMENTO" CHAR(1), 
	"DOCUMENTO" NVARCHAR2(20), 
	"CODIGO_CENTRAL" NVARCHAR2(8), 
	"CODIGO_CAPA" NVARCHAR2(4)
   );
  GRANT UPDATE ON "GIFOLE"."VEH_CLIENTE2" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_CLIENTE2" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_CLIENTE2" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_CLIENTE2" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_COTIZACION
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_COTIZACION" 
   (	"ID" NUMBER(16,0), 
	"NUM_PLACA" NVARCHAR2(10), 
	"MARCA" NVARCHAR2(4), 
	"MODELO" NVARCHAR2(8), 
	"ANHO_FABRICACION" NVARCHAR2(4), 
	"CAMBIO_GAS" CHAR(1), 
	"DIVISA" NVARCHAR2(3), 
	"VALOR_COMERCIAL" NUMBER(15,2), 
	"NOMBRE" NVARCHAR2(100), 
	"TIPO_DOCUMENTO" NVARCHAR2(20), 
	"DOCUMENTO" NVARCHAR2(20), 
	"CORREO" NVARCHAR2(80), 
	"TELEFONO" NVARCHAR2(20), 
	"PROCESADO" CHAR(1), 
	"FECHA_REGISTRO" TIMESTAMP (6), 
	"AUTORIZACION" CHAR(1), 
	"TIPO" NVARCHAR2(7), 
	"LUGAR_REGISTRO" NVARCHAR2(20), 
	"HORARIO" NVARCHAR2(20)
   );
  GRANT UPDATE ON "GIFOLE"."VEH_COTIZACION" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_COTIZACION" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_COTIZACION" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_COTIZACION" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_COTIZACION_DETALLE
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_COTIZACION_DETALLE" 
   (	"ID" NUMBER(16,0), 
	"COTIZACION" NUMBER(16,0), 
	"PLAN" NVARCHAR2(4), 
	"CUOTA_MENSUAL" NUMBER(15,2), 
	"CUOTA_ANUAL" NUMBER(15,2), 
	"DIVISA" NVARCHAR2(3)
   );
  GRANT ALTER ON "GIFOLE"."VEH_COTIZACION_DETALLE" TO "APP_GIFOLE";
  GRANT UPDATE ON "GIFOLE"."VEH_COTIZACION_DETALLE" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_COTIZACION_DETALLE" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_COTIZACION_DETALLE" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_COTIZACION_DETALLE" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_COTIZADOR_CARGA_HISTORIAL
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_COTIZADOR_CARGA_HISTORIAL" 
   (	"ID" NUMBER(16,2), 
	"CODIGO" NVARCHAR2(40), 
	"TIPO_CARGA" NVARCHAR2(50), 
	"NOMBRE" NVARCHAR2(50), 
	"ESTADO" CHAR(1), 
	"ESTADO_CATEGORIA" CHAR(1), 
	"ERROR" NVARCHAR2(200), 
	"CREADOR" NUMBER(16,0), 
	"CREACION" TIMESTAMP (6), 
	"MODIFICACION" TIMESTAMP (6)
   ) ;

   COMMENT ON COLUMN "GIFOLE"."VEH_COTIZADOR_CARGA_HISTORIAL"."ESTADO" IS 'S: Sin procesar, P: Procesado, E: No terminado';
   COMMENT ON COLUMN "GIFOLE"."VEH_COTIZADOR_CARGA_HISTORIAL"."ESTADO_CATEGORIA" IS 'A: Activado';
  GRANT UPDATE ON "GIFOLE"."VEH_COTIZADOR_CARGA_HISTORIAL" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_COTIZADOR_CARGA_HISTORIAL" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_COTIZADOR_CARGA_HISTORIAL" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_COTIZADOR_CARGA_HISTORIAL" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_DSCTOCAPA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_DSCTOCAPA" 
   (	"ID" NUMBER(16,0), 
	"CODIGO_CAPA" NVARCHAR2(4), 
	"FEC_INI_VIG" DATE, 
	"FEC_FIN_VIG" DATE, 
	"TIPO_FACTOR" CHAR(1), 
	"TIPO_TASA" CHAR(1), 
	"VALOR_TASA" NUMBER(8,3)
   );
  GRANT SELECT ON "GIFOLE"."VEH_DSCTOCAPA" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_DSCTOCAPA" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_DSCTOCAPA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_MARCA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_MARCA" 
   (	"CODIGO" NVARCHAR2(4), 
	"NOMBRE" VARCHAR2(50), 
	"ESTADO" CHAR(1), 
	"CREACION" TIMESTAMP (6), 
	"EDICION" TIMESTAMP (6)
   );
  GRANT UPDATE ON "GIFOLE"."VEH_MARCA" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_MARCA" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_MARCA" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_MARCA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_MODELO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_MODELO" 
   (	"CODIGO" NVARCHAR2(8), 
	"NOMBRE" NVARCHAR2(50), 
	"MARCA" NVARCHAR2(4), 
	"ESTADO" CHAR(1), 
	"CREACION" TIMESTAMP (6), 
	"EDICION" TIMESTAMP (6)
   );
  GRANT UPDATE ON "GIFOLE"."VEH_MODELO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_MODELO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_MODELO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_MODELO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_PLAN
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_PLAN" 
   (	"CODIGO" NVARCHAR2(4), 
	"DESCRIPCION" NVARCHAR2(40), 
	"ESTILO" NVARCHAR2(30), 
	"FACTOR_BANCARIO" CHAR(1), 
	"MONTO_MINIMO" NUMBER(15,2), 
	"ESTADO" CHAR(1), 
	"ORDEN" NUMBER(*,0)
   );
  GRANT UPDATE ON "GIFOLE"."VEH_PLAN" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_PLAN" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_PLAN" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_PLAN" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_PLAN_BENEFICIO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_PLAN_BENEFICIO" 
   (	"ID" NUMBER(16,0), 
	"BENEFICIO" NUMBER(16,0), 
	"PLAN" NVARCHAR2(4), 
	"DETALLE" NVARCHAR2(100)
   );
  GRANT ALTER ON "GIFOLE"."VEH_PLAN_BENEFICIO" TO "APP_GIFOLE";
  GRANT UPDATE ON "GIFOLE"."VEH_PLAN_BENEFICIO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_PLAN_BENEFICIO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_PLAN_BENEFICIO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_PLAN_BENEFICIO" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_PRIMA_MINIMA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_PRIMA_MINIMA" 
   (	"CATEGORIA" NVARCHAR2(4), 
	"PLAN" NVARCHAR2(4), 
	"MONTO" NUMBER(13,2), 
	"DIVISA" NVARCHAR2(3)
   );
  GRANT ALTER ON "GIFOLE"."VEH_PRIMA_MINIMA" TO "APP_GIFOLE";
  GRANT UPDATE ON "GIFOLE"."VEH_PRIMA_MINIMA" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_PRIMA_MINIMA" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_PRIMA_MINIMA" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_PRIMA_MINIMA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_SUNARP
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_SUNARP" 
   (	"NUM_PLACA_VIG" NVARCHAR2(10), 
	"NUM_PLACA_NO_VIG" NVARCHAR2(10), 
	"MARCA" NVARCHAR2(4), 
	"MODELO" NVARCHAR2(8), 
	"CLASE" NVARCHAR2(4), 
	"TIPO" NVARCHAR2(7), 
	"ANHO_FABRICACION" NVARCHAR2(4)
   );
  GRANT UPDATE ON "GIFOLE"."VEH_SUNARP" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_SUNARP" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_SUNARP" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_SUNARP" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_TARIFA
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_TARIFA" 
   (	"ID" NUMBER(16,0), 
	"PLAN" NVARCHAR2(4), 
	"CATEGORIA" NVARCHAR2(4), 
	"CODIGO_USO" NVARCHAR2(4), 
	"ANHO_ANTIGUEDAD" NVARCHAR2(4), 
	"CODIGO_UBICACION" NVARCHAR2(1), 
	"TASA_MES" NUMBER(5,5), 
	"TASA_ANUAL" NUMBER(5,5)
   );
  GRANT UPDATE ON "GIFOLE"."VEH_TARIFA" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_TARIFA" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_TARIFA" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_TARIFA" TO "APP_GIFOLE";
--------------------------------------------------------
--  DDL for Table VEH_TIPO
--------------------------------------------------------

  CREATE TABLE "GIFOLE"."VEH_TIPO" 
   (	"CODIGO" NVARCHAR2(7), 
	"NOMBRE" NVARCHAR2(50), 
	"ESTADO" CHAR(1), 
	"CREACION" TIMESTAMP (6), 
	"EDICION" TIMESTAMP (6)
   );
  GRANT UPDATE ON "GIFOLE"."VEH_TIPO" TO "APP_GIFOLE";
  GRANT SELECT ON "GIFOLE"."VEH_TIPO" TO "APP_GIFOLE";
  GRANT INSERT ON "GIFOLE"."VEH_TIPO" TO "APP_GIFOLE";
  GRANT DELETE ON "GIFOLE"."VEH_TIPO" TO "APP_GIFOLE";

COMMIT;