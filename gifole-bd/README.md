# Base de Datos Oracle GIFOLE

Esta carpeta contiene la configuración Docker para la base de datos Oracle del proyecto GIFOLE.

## Estructura de Archivos

```
gifole-bd/
├── docker-compose.yml          # Configuración de servicios Docker
├── init-scripts/               # Scripts de inicialización GIFOLE
│   ├── 01-tablespace.sql      # Creación de tablespaces
│   ├── 02-usuarios.sql        # Creación de usuarios y esquemas
│   ├── 03-tablas.sql          # Creación de todas las tablas GIFOLE
│   └── 04-datos.sql           # Inserción de datos iniciales (136K+ registros)
└── README.md                   # Este archivo
```

## Configuración de la Base de Datos

### Credenciales

- **Host**: localhost
- **Puerto**: 1523 (mapeado desde 1521 interno)
- **SID**: XE
- **PDB**: XEPDB1
- **Usuario Administrador**: SYSTEM
- **Contraseña Administrador**: gifole123
- **Usuario Aplicación**: gifole_user
- **Contraseña Aplicación**: gifole_pass
- **Base de Datos**: GIFOLE

### Cadenas de Conexión

**Para aplicaciones Java (JDBC):**
```
***********************************
```

**Para aplicaciones con PDB GIFOLE:**
```
***************************************
```

**SQL*Plus:**
```bash
sqlplus gifole_user/gifole_pass@localhost:1523/GIFOLE
```

## Uso

### Iniciar la Base de Datos

```bash
cd gifole-bd
docker-compose up -d
```

### Verificar el Estado

```bash
docker-compose ps
docker-compose logs -f
```

### Detener la Base de Datos

```bash
docker-compose down
```

### Detener y Eliminar Datos

```bash
docker-compose down -v
```

## Conectarse a la Base de Datos

### Usando SQL*Plus desde el contenedor

```bash
docker exec -it oracle-gifole-bd sqlplus gifole_user/gifole_pass@localhost:1521/GIFOLE
```

### Usando SQL*Plus como SYSTEM

```bash
docker exec -it oracle-gifole-bd sqlplus / as sysdba
```

### Verificar la Inicialización

```sql
-- Conectar como gifole_user
SELECT * FROM usuarios;
SELECT COUNT(*) FROM usuarios;
```

## Objetos Creados

### Tablespaces
- **GIFOLE_DATA**: Tablespace principal para datos de GIFOLE (500MB)
- **GIFOLE_INDEX**: Tablespace para índices de GIFOLE (200MB)
- **APP_GIFOLE_DATA**: Tablespace para datos de aplicación (100MB)
- **APP_GIFOLE_INDEX**: Tablespace para índices de aplicación (50MB)
- **GIFPRI_DATA**: Tablespace para datos de GIFPRI (100MB)
- **GIFPRI_INDEX**: Tablespace para índices de GIFPRI (50MB)
- **APP_CAFUAP_DATA**: Tablespace para datos de CAFUAP (100MB)
- **APP_CAFUAP_INDEX**: Tablespace para índices de CAFUAP (50MB)

### Usuarios/Esquemas
- **GIFOLE**: Usuario principal del esquema GIFOLE
- **APP_GIFOLE**: Usuario de aplicación para GIFOLE
- **GIFPRI**: Usuario del esquema GIFPRI
- **APP_GIFPRI**: Usuario de aplicación para GIFPRI
- **APP_CAFUAP**: Usuario de aplicación para CAFUAP

### Tablas del Sistema GIFOLE
- **AFILIACION_EECC_FFMM**: Afiliaciones EECC/FFMM
- **CUPONES_RASPA_GANA**: Sistema de cupones y sorteos
- **EXT_VEH_AUTOMAS**: Catálogo de vehículos
- **VEH_TARIFA**: Tarifas vehiculares
- **TC_COTIZACION**: Cotizaciones de tarjetas de crédito
- **SEGUROS_VEHICULAR**: Seguros vehiculares
- **PRESTAMOS**: Sistema de préstamos
- **AUDITORIA**: Sistema de auditoría
- Y más de 50 tablas adicionales del sistema GIFOLE

### Datos Iniciales
- **136,685 registros** insertados automáticamente
- Datos de prueba y configuración del sistema
- Catálogos de vehículos, tarifas, y parámetros

## Persistencia de Datos

Los datos se almacenan en un volumen Docker llamado `oracle_data` que persiste entre reinicios del contenedor.

## Monitoreo

La base de datos incluye un healthcheck que verifica el estado cada 30 segundos.

## Solución de Problemas

### La base de datos no inicia
```bash
docker-compose logs oracle-gifole
```

### Verificar conectividad
```bash
docker exec oracle-gifole-db tnsping localhost:1521
```

### Reiniciar completamente
```bash
docker-compose down -v
docker-compose up -d
```

## Notas Importantes

- La primera inicialización puede tomar varios minutos
- Los scripts en `init-scripts/` se ejecutan automáticamente en la primera creación
- Para cambios en los scripts de inicialización, elimina el volumen y recrea el contenedor
- Esta configuración está optimizada para desarrollo, no para producción
