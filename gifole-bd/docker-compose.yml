version: '3.8'

services:
  oracle-gifole:
    image: gvenzl/oracle-xe:21-slim
    container_name: oracle-gifole-bd
    ports:
      - "1523:1521"
    environment:
      # Database configuration
      - ORACLE_PASSWORD=gifole123
      - ORACLE_DATABASE=GIFOLE
      # User configuration
      - APP_USER=gifole_user
      - APP_USER_PASSWORD=gifole_pass
    volumes:
      - oracle_data:/opt/oracle/oradata
      - ./init-scripts:/container-entrypoint-initdb.d
    networks:
      - gifole-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  oracle_data:
    driver: local

networks:
  gifole-network:
    driver: bridge
