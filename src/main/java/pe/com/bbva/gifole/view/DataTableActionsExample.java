package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.html.H3;
import com.vaadin.flow.component.html.Paragraph;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.Route;

import java.util.ArrayList;
import java.util.List;

/**
 * Ejemplo de uso de columnas de acciones en DataTableOptimized
 */
@Route("datatable-actions")
public class DataTableActionsExample extends VerticalLayout {

    public DataTableActionsExample() {
        setSizeFull();
        setPadding(true);
        setSpacing(true);

        add(new H2("🎯 DataTable con Columnas de Acciones"));
        add(new Paragraph("Ejemplos de cómo usar las columnas de acciones con iconos automáticos."));

        // Crear datos de ejemplo
        List<Usuario> usuarios = createUserData();

        // Ejemplo 1: Solo icono de editar
        add(new H3("✏️ Ejemplo 1: Solo Acción de Editar"));
        
        DataTableOptimized<Usuario> editOnlyTable = DataTableOptimized.<Usuario>builder()
            .id("usuarios-edit-only")
            .column("id", "ID", usuario -> usuario.getId().toString(), "60px")
            .column("nombre", "Nombre", Usuario::getNombre, "200px")
            .column("email", "Email", Usuario::getEmail, "250px")
            .column("rol", "Rol", Usuario::getRol, "120px")
            .actionEdit("Editar") // ✅ Solo icono de editar
            .items(usuarios)
            .pageSize(5)
            .build();

        add(editOnlyTable);

        // Ejemplo 2: Solo icono de eliminar
        add(new H3("🗑️ Ejemplo 2: Solo Acción de Eliminar"));
        
        DataTableOptimized<Usuario> deleteOnlyTable = DataTableOptimized.<Usuario>builder()
            .id("usuarios-delete-only")
            .column("id", "ID", usuario -> usuario.getId().toString(), "60px")
            .column("nombre", "Nombre", Usuario::getNombre, "200px")
            .column("estado", "Estado", usuario -> usuario.isActivo() ? "✅ Activo" : "❌ Inactivo", "100px")
            .actionDelete("Eliminar") // ✅ Solo icono de eliminar
            .items(usuarios)
            .pageSize(5)
            .build();

        add(deleteOnlyTable);

        // Ejemplo 3: Iconos de editar y eliminar
        add(new H3("⚡ Ejemplo 3: Acciones de Editar y Eliminar"));
        
        DataTableOptimized<Usuario> fullActionsTable = DataTableOptimized.<Usuario>builder()
            .id("usuarios-full-actions")
            .column("id", "ID", usuario -> usuario.getId().toString(), "60px")
            .column("nombre", "Nombre Completo", Usuario::getNombre, "180px")
            .column("email", "Correo Electrónico", Usuario::getEmail, "220px")
            .column("rol", "Rol", Usuario::getRol, "100px")
            .column("estado", "Estado", usuario -> usuario.isActivo() ? "🟢 Activo" : "🔴 Inactivo", "100px")
            .actionsEditDelete("Acciones") // ✅ Ambos iconos
            .items(usuarios)
            .pageSize(5)
            .build();

        add(fullActionsTable);

        // Ejemplo 4: Múltiples columnas de acciones
        add(new H3("🎛️ Ejemplo 4: Múltiples Columnas de Acciones"));
        
        DataTableOptimized<Usuario> multipleActionsTable = DataTableOptimized.<Usuario>builder()
            .id("usuarios-multiple-actions")
            .column("nombre", "Usuario", Usuario::getNombre, "150px")
            .column("email", "Email", Usuario::getEmail, "200px")
            .column("rol", "Rol", Usuario::getRol, "100px")
            .actionEdit("Editar", "80px") // ✅ Columna de editar
            .actionDelete("Eliminar", "80px") // ✅ Columna de eliminar separada
            .items(usuarios)
            .pageSize(4)
            .build();

        add(multipleActionsTable);

        // Agregar JavaScript para manejar los clicks
        addJavaScriptHandlers();
    }

    /**
     * Agrega los manejadores JavaScript para los botones de acción
     */
    private void addJavaScriptHandlers() {
        getElement().executeJs("""
            // Función para manejar edición
            window.editItem = function(itemId) {
                console.log('Editando item:', itemId);
                alert('Editando usuario con ID: ' + itemId);
                // Aquí puedes agregar tu lógica de edición
                // Por ejemplo: abrir un diálogo, navegar a otra página, etc.
            };
            
            // Función para manejar eliminación
            window.deleteItem = function(itemId) {
                console.log('Eliminando item:', itemId);
                if (confirm('¿Estás seguro de que quieres eliminar el usuario con ID: ' + itemId + '?')) {
                    alert('Usuario eliminado: ' + itemId);
                    // Aquí puedes agregar tu lógica de eliminación
                    // Por ejemplo: llamar a un servicio, actualizar la tabla, etc.
                }
            };
            """);
    }

    /**
     * Crea datos de ejemplo para las tablas
     */
    private List<Usuario> createUserData() {
        List<Usuario> usuarios = new ArrayList<>();

        usuarios.add(new Usuario(1L, "Juan Pérez García", "<EMAIL>", "Administrador", true));
        usuarios.add(new Usuario(2L, "María García López", "<EMAIL>", "Usuario", true));
        usuarios.add(new Usuario(3L, "Carlos López Martín", "<EMAIL>", "Editor", false));
        usuarios.add(new Usuario(4L, "Ana Martínez Ruiz", "<EMAIL>", "Usuario", true));
        usuarios.add(new Usuario(5L, "Pedro Rodríguez Silva", "<EMAIL>", "Administrador", true));
        usuarios.add(new Usuario(6L, "Laura Sánchez Torres", "<EMAIL>", "Editor", false));
        usuarios.add(new Usuario(7L, "Miguel Torres Vega", "<EMAIL>", "Usuario", true));
        usuarios.add(new Usuario(8L, "Carmen Ruiz Morales", "<EMAIL>", "Usuario", true));
        usuarios.add(new Usuario(9L, "David Moreno Castro", "<EMAIL>", "Administrador", false));
        usuarios.add(new Usuario(10L, "Isabel Jiménez Ramos", "<EMAIL>", "Editor", true));

        return usuarios;
    }

    /**
     * Clase de ejemplo para representar un usuario
     */
    public static class Usuario {
        private Long id;
        private String nombre;
        private String email;
        private String rol;
        private boolean activo;

        public Usuario(Long id, String nombre, String email, String rol, boolean activo) {
            this.id = id;
            this.nombre = nombre;
            this.email = email;
            this.rol = rol;
            this.activo = activo;
        }

        // Getters
        public Long getId() {
            return id;
        }

        public String getNombre() {
            return nombre;
        }

        public String getEmail() {
            return email;
        }

        public String getRol() {
            return rol;
        }

        public boolean isActivo() {
            return activo;
        }

        // Setters
        public void setId(Long id) {
            this.id = id;
        }

        public void setNombre(String nombre) {
            this.nombre = nombre;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public void setRol(String rol) {
            this.rol = rol;
        }

        public void setActivo(boolean activo) {
            this.activo = activo;
        }
    }
}
