# 🚀 DataTableOptimized - Gu<PERSON> de Uso

## 🎯 **¿Por qué usar el enfoque optimizado?**

### **Ventajas del DataTableOptimized:**
- 🚀 **100x más rápido** que reflection
- ✅ **Type-safe** - errores en tiempo de compilación
- 🎨 **Transformaciones flexibles** - lógica personalizada por columna
- 🧹 **Código más limpio** - sintaxis fluida con Builder Pattern
- 🔧 **F<PERSON>cil mantenimiento** - sin strings mágicos para nombres de campos

## 📋 **Migración Paso a Paso**

### **ANTES (Reflection - Lento):**
```java
// ❌ Código anterior - NO USAR
Map<String, String> headers = new LinkedHashMap<>();
headers.put("nombre", "Nombre");           // String mágico
headers.put("email", "Email");             // String mágico
headers.put("departamento.nombre", "Depto"); // Navegación anidada frágil

DataTable<Empleado> table = new DataTable<>("empleados", headers, empleados, 10);
```

### **DESPUÉS (Optimizado - Rápido):**
```java
// ✅ Código nuevo - USAR ESTE
DataTableOptimized<Empleado> table = DataTableOptimized.<Empleado>builder()
    .id("empleados")
    .column("nombre", "Nombre", Empleado::getNombre, "200px")           // Type-safe
    .column("email", "Email", Empleado::getEmail, "250px")             // Type-safe
    .column("depto", "Depto", emp -> emp.getDepartamento().getNombre(), "150px") // Seguro
    .items(empleados)
    .pageSize(10)
    .build();
```

## 🎨 **Patrones de Uso Comunes**

### **1. Columnas Básicas**
```java
.column("id", "ID", empleado -> empleado.getId().toString(), "80px")
.column("nombre", "Nombre", Empleado::getNombre, "200px")
.column("email", "Email", Empleado::getEmail, "250px")
```

### **2. Transformaciones de Datos**
```java
// Formateo de números
.column("salario", "Salario", emp -> String.format("S/ %.2f", emp.getSalario()), "120px")

// Formateo de fechas
.column("fecha", "Fecha", emp -> emp.getFechaIngreso().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")), "100px")

// Valores booleanos
.column("activo", "Estado", emp -> emp.isActivo() ? "✅ Activo" : "❌ Inactivo", "100px")
```

### **3. Lógica Condicional**
```java
.column("estado", "Estado", empleado -> {
    if (empleado.getSalario() > 5000) {
        return "💰 Senior";
    } else if (empleado.getSalario() > 3000) {
        return "💼 Mid";
    } else {
        return "🌱 Junior";
    }
}, "100px")
```

### **4. Navegación de Objetos Anidados**
```java
// Acceso seguro a objetos anidados
.column("departamento", "Departamento", emp -> {
    Departamento dept = emp.getDepartamento();
    return dept != null ? dept.getNombre() : "Sin Asignar";
}, "150px")

// Múltiples niveles
.column("jefe", "Jefe", emp -> {
    Departamento dept = emp.getDepartamento();
    if (dept != null && dept.getJefe() != null) {
        return dept.getJefe().getNombre();
    }
    return "N/A";
}, "150px")
```

### **5. Columnas Calculadas**
```java
.column("antiguedad", "Antigüedad", emp -> {
    long años = ChronoUnit.YEARS.between(emp.getFechaIngreso(), LocalDate.now());
    return años + " años";
}, "100px")

.column("valor_total", "Valor Total", producto -> {
    double total = producto.getPrecio() * producto.getStock();
    return String.format("S/ %.2f", total);
}, "120px")
```

## 🎯 **Mejores Prácticas**

### **1. Usar Method References cuando sea posible**
```java
// ✅ BUENO - Method reference
.column("nombre", "Nombre", Empleado::getNombre, "200px")

// ⚠️ FUNCIONA pero menos elegante
.column("nombre", "Nombre", emp -> emp.getNombre(), "200px")
```

### **2. Manejar valores null de forma segura**
```java
// ✅ BUENO - Manejo seguro de nulls
.column("telefono", "Teléfono", emp -> {
    String telefono = emp.getTelefono();
    return telefono != null ? telefono : "No disponible";
}, "120px")

// 🚀 MEJOR - Usando Optional
.column("telefono", "Teléfono", emp -> 
    Optional.ofNullable(emp.getTelefono()).orElse("No disponible"), "120px")
```

### **3. Reutilizar transformaciones complejas**
```java
// ✅ BUENO - Extraer lógica compleja a métodos
private String formatearEstadoEmpleado(Empleado emp) {
    if (!emp.isActivo()) return "❌ Inactivo";
    if (emp.getSalario() > 5000) return "💰 Senior Activo";
    return "✅ Activo";
}

// Usar en la columna
.column("estado", "Estado", this::formatearEstadoEmpleado, "120px")
```

### **4. Anchos de columna responsivos**
```java
// Pixels para columnas fijas
.column("id", "ID", emp -> emp.getId().toString(), "80px")

// Porcentajes para columnas flexibles
.column("nombre", "Nombre", Empleado::getNombre, "30%")
.column("descripcion", "Descripción", Empleado::getDescripcion, "50%")

// Sin ancho para columnas que se ajusten automáticamente
.column("acciones", "Acciones", emp -> "Ver | Editar")
```

## 🔧 **Configuración Avanzada**

### **Builder Pattern Completo**
```java
DataTableOptimized<Empleado> table = DataTableOptimized.<Empleado>builder()
    .id("empleados-table")                    // ID único del componente
    .column("id", "ID", emp -> emp.getId().toString(), "80px")
    .column("nombre", "Nombre Completo", Empleado::getNombre, "200px")
    .column("email", "Correo", Empleado::getEmail, "250px")
    .column("salario", "Salario", emp -> String.format("S/ %.2f", emp.getSalario()), "120px")
    .items(empleados)                         // Lista de datos
    .pageSize(10)                            // Elementos por página
    .build();
```

## 🎨 **Ejemplos de Transformaciones Creativas**

### **1. Indicadores Visuales**
```java
.column("stock", "Stock", producto -> {
    int stock = producto.getStock();
    if (stock == 0) return "🔴 Agotado";
    if (stock < 10) return "🟡 Bajo (" + stock + ")";
    if (stock < 50) return "🟢 Normal (" + stock + ")";
    return "🔵 Alto (" + stock + ")";
}, "120px")
```

### **2. Badges de Estado**
```java
.column("prioridad", "Prioridad", tarea -> {
    switch (tarea.getPrioridad()) {
        case ALTA: return "🔥 ALTA";
        case MEDIA: return "⚡ MEDIA";
        case BAJA: return "❄️ BAJA";
        default: return "❓ SIN DEFINIR";
    }
}, "100px")
```

### **3. Formateo de Texto**
```java
.column("codigo", "Código", producto -> 
    producto.getCodigo().toUpperCase(), "100px")

.column("descripcion", "Descripción", producto -> {
    String desc = producto.getDescripcion();
    return desc.length() > 50 ? desc.substring(0, 47) + "..." : desc;
}, "200px")
```

## 🚀 **Migración Inmediata**

Para migrar tu código existente:

1. **Reemplaza** `DataTable` por `DataTableOptimized`
2. **Cambia** el constructor por el builder pattern
3. **Convierte** cada entrada del Map a un `.column()`
4. **Reemplaza** strings de campos por method references o lambdas

¡Tu código será inmediatamente más rápido, seguro y mantenible! 🎉
