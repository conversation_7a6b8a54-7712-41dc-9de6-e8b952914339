package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.html.H3;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.Route;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Ejemplo de uso del DataTable optimizado con interfaces funcionales
 */
@Route("datatable-optimized")
public class DataTableOptimizedExample extends VerticalLayout {

    public DataTableOptimizedExample() {
        setSizeFull();
        setPadding(true);
        setSpacing(true);

        add(new H2("DataTable Optimizado - Comparación de Rendimiento"));

        // Crear datos de ejemplo
        List<Empleado> empleados = createEmployeeData();

        // Ejemplo 1: Usando el DataTable tradicional con reflection
        add(new H3("1. DataTable Tradicional (con Reflection)"));
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("id", "ID");
        headers.put("nombre", "Nombre");
        headers.put("email", "Email");
        headers.put("departamento.nombre", "Departamento");
        headers.put("salario", "Salario");

        DataTable<Empleado> traditionalTable = new DataTable<>(
            "empleados-traditional", headers, empleados, 5
        );
        add(traditionalTable);

        // Ejemplo 2: Usando el DataTable optimizado con interfaces funcionales
        add(new H3("2. DataTable Optimizado (sin Reflection)"));
        
        // Método 1: Constructor directo
        Map<String, DataTableOptimized.ColumnDefinition<Empleado>> optimizedColumns = new LinkedHashMap<>();
        optimizedColumns.put("id", new DataTableOptimized.ColumnDefinition<>("ID", emp -> emp.getId().toString(), "80px"));
        optimizedColumns.put("nombre", new DataTableOptimized.ColumnDefinition<>("Nombre Completo", Empleado::getNombre, "200px"));
        optimizedColumns.put("email", new DataTableOptimized.ColumnDefinition<>("Email", Empleado::getEmail, "250px"));
        optimizedColumns.put("departamento", new DataTableOptimized.ColumnDefinition<>("Departamento", emp -> emp.getDepartamento().getNombre(), "150px"));
        optimizedColumns.put("salario", new DataTableOptimized.ColumnDefinition<>("Salario", emp -> String.format("S/ %.2f", emp.getSalario()), "120px"));

        DataTableOptimized<Empleado> optimizedTable = new DataTableOptimized<>(
            "empleados-optimized", optimizedColumns, empleados, 5
        );
        add(optimizedTable);

        // Ejemplo 3: Usando el Builder Pattern (más elegante)
        add(new H3("3. DataTable con Builder Pattern"));
        
        DataTableOptimized<Empleado> builderTable = DataTableOptimized.<Empleado>builder()
            .id("empleados-builder")
            .column("id", "ID", emp -> emp.getId().toString(), "80px")
            .column("nombre", "Nombre", Empleado::getNombre, "200px")
            .column("email", "Correo Electrónico", Empleado::getEmail, "250px")
            .column("departamento", "Departamento", emp -> emp.getDepartamento().getNombre(), "150px")
            .column("salario", "Salario", emp -> String.format("S/ %.2f", emp.getSalario()), "120px")
            .column("activo", "Estado", emp -> emp.isActivo() ? "Activo" : "Inactivo", "100px")
            .items(empleados)
            .pageSize(5)
            .build();
        
        add(builderTable);
    }

    /**
     * Crea datos de ejemplo para las tablas
     */
    private List<Empleado> createEmployeeData() {
        List<Empleado> empleados = new ArrayList<>();

        // Crear departamentos
        Departamento sistemas = new Departamento("Sistemas");
        Departamento rrhh = new Departamento("Recursos Humanos");
        Departamento ventas = new Departamento("Ventas");
        Departamento marketing = new Departamento("Marketing");

        // Crear empleados
        empleados.add(new Empleado(1L, "Juan Pérez García", "<EMAIL>", sistemas, 5500.00, true));
        empleados.add(new Empleado(2L, "María García López", "<EMAIL>", rrhh, 4800.00, true));
        empleados.add(new Empleado(3L, "Carlos López Martín", "<EMAIL>", ventas, 4200.00, false));
        empleados.add(new Empleado(4L, "Ana Martínez Ruiz", "<EMAIL>", marketing, 4600.00, true));
        empleados.add(new Empleado(5L, "Pedro Rodríguez Silva", "<EMAIL>", sistemas, 5800.00, true));
        empleados.add(new Empleado(6L, "Laura Sánchez Torres", "<EMAIL>", rrhh, 4400.00, false));
        empleados.add(new Empleado(7L, "Miguel Torres Vega", "<EMAIL>", ventas, 4000.00, true));
        empleados.add(new Empleado(8L, "Carmen Ruiz Morales", "<EMAIL>", marketing, 4700.00, true));
        empleados.add(new Empleado(9L, "David Moreno Castro", "<EMAIL>", sistemas, 6200.00, false));
        empleados.add(new Empleado(10L, "Isabel Jiménez Ramos", "<EMAIL>", rrhh, 4300.00, true));
        empleados.add(new Empleado(11L, "Francisco Herrera Díaz", "<EMAIL>", ventas, 3900.00, true));
        empleados.add(new Empleado(12L, "Pilar Navarro Ortega", "<EMAIL>", marketing, 4500.00, false));

        return empleados;
    }

    /**
     * Clase de ejemplo para representar un empleado
     */
    public static class Empleado {
        private Long id;
        private String nombre;
        private String email;
        private Departamento departamento;
        private double salario;
        private boolean activo;

        public Empleado(Long id, String nombre, String email, Departamento departamento, double salario, boolean activo) {
            this.id = id;
            this.nombre = nombre;
            this.email = email;
            this.departamento = departamento;
            this.salario = salario;
            this.activo = activo;
        }

        // Getters
        public Long getId() {
            return id;
        }

        public String getNombre() {
            return nombre;
        }

        public String getEmail() {
            return email;
        }

        public Departamento getDepartamento() {
            return departamento;
        }

        public double getSalario() {
            return salario;
        }

        public boolean isActivo() {
            return activo;
        }

        // Setters
        public void setId(Long id) {
            this.id = id;
        }

        public void setNombre(String nombre) {
            this.nombre = nombre;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public void setDepartamento(Departamento departamento) {
            this.departamento = departamento;
        }

        public void setSalario(double salario) {
            this.salario = salario;
        }

        public void setActivo(boolean activo) {
            this.activo = activo;
        }
    }

    /**
     * Clase de ejemplo para representar un departamento
     */
    public static class Departamento {
        private String nombre;

        public Departamento(String nombre) {
            this.nombre = nombre;
        }

        public String getNombre() {
            return nombre;
        }

        public void setNombre(String nombre) {
            this.nombre = nombre;
        }
    }
}
