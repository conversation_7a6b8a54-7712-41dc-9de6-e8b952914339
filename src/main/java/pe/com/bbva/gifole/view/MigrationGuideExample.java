package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.html.H3;
import com.vaadin.flow.component.html.Paragraph;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.Route;

import java.util.ArrayList;
import java.util.List;

/**
 * Guía de migración paso a paso del DataTable tradicional al optimizado
 */
@Route("migration-guide")
public class MigrationGuideExample extends VerticalLayout {

    public MigrationGuideExample() {
        setSizeFull();
        setPadding(true);
        setSpacing(true);

        add(new H2("📋 Guía de Migración: DataTable → DataTableOptimized"));

        // Crear datos de ejemplo
        List<Producto> productos = createProductData();

        // PASO 1: Código tradicional (ANTES)
        add(new H3("❌ ANTES: Código Tradicional con Reflection"));
        add(new Paragraph("Este es el código que usabas antes (lento, no type-safe):"));
        
        showTraditionalApproach(productos);

        // PASO 2: Código optimizado (DESPUÉS)
        add(new H3("✅ DESPUÉS: Código Optimizado sin Reflection"));
        add(new Paragraph("Este es el nuevo código optimizado (rápido, type-safe):"));
        
        showOptimizedApproach(productos);

        // PASO 3: Casos de uso avanzados
        add(new H3("🚀 CASOS AVANZADOS: Transformaciones Complejas"));
        add(new Paragraph("Ejemplos de transformaciones que no eran posibles con reflection:"));
        
        showAdvancedUseCases(productos);
    }

    /**
     * Muestra el enfoque tradicional (comentado para referencia)
     */
    private void showTraditionalApproach(List<Producto> productos) {
        /*
        // ❌ CÓDIGO ANTERIOR (NO USAR)
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("codigo", "Código");
        headers.put("nombre", "Producto");
        headers.put("precio", "Precio");
        headers.put("categoria", "Categoría");
        headers.put("stock", "Stock");

        DataTable<Producto> traditionalTable = new DataTable<>(
            "productos-traditional", headers, productos, 5
        );
        add(traditionalTable);
        */
        
        add(new Paragraph("💡 El código anterior usaba reflection y era lento para tablas grandes."));
    }

    /**
     * Muestra el enfoque optimizado
     */
    private void showOptimizedApproach(List<Producto> productos) {
        // ✅ CÓDIGO NUEVO (USAR ESTE)
        DataTableOptimized<Producto> optimizedTable = DataTableOptimized.<Producto>builder()
            .id("productos-optimized")
            .column("codigo", "Código", Producto::getCodigo, "100px")
            .column("nombre", "Producto", Producto::getNombre, "250px")
            .column("precio", "Precio", producto -> String.format("S/ %.2f", producto.getPrecio()), "120px")
            .column("categoria", "Categoría", Producto::getCategoria, "150px")
            .column("stock", "Stock", producto -> producto.getStock().toString(), "80px")
            .items(productos)
            .pageSize(5)
            .build();

        add(optimizedTable);
    }

    /**
     * Muestra casos de uso avanzados
     */
    private void showAdvancedUseCases(List<Producto> productos) {
        DataTableOptimized<Producto> advancedTable = DataTableOptimized.<Producto>builder()
            .id("productos-advanced")
            .column("codigo", "Código", Producto::getCodigo, "80px")
            .column("nombre", "Producto", Producto::getNombre, "200px")
            
            // 🎯 Transformación condicional
            .column("precio", "Precio", producto -> {
                double precio = producto.getPrecio();
                if (precio > 1000) {
                    return String.format("💰 S/ %.2f", precio);
                } else if (precio > 500) {
                    return String.format("💵 S/ %.2f", precio);
                } else {
                    return String.format("💸 S/ %.2f", precio);
                }
            }, "120px")
            
            // 🎯 Categoría con emojis
            .column("categoria", "Categoría", producto -> {
                String categoria = producto.getCategoria();
                switch (categoria.toLowerCase()) {
                    case "electrónicos": return "📱 " + categoria;
                    case "muebles": return "🪑 " + categoria;
                    case "accesorios": return "🔌 " + categoria;
                    case "iluminación": return "💡 " + categoria;
                    default: return "📦 " + categoria;
                }
            }, "150px")
            
            // 🎯 Stock con indicadores visuales
            .column("stock", "Stock", producto -> {
                int stock = producto.getStock();
                if (stock == 0) {
                    return "🔴 Agotado";
                } else if (stock < 10) {
                    return "🟡 Bajo (" + stock + ")";
                } else if (stock < 50) {
                    return "🟢 Normal (" + stock + ")";
                } else {
                    return "🔵 Alto (" + stock + ")";
                }
            }, "120px")
            
            // 🎯 Columna calculada compleja
            .column("valor_total", "Valor Total", producto -> {
                double valorTotal = producto.getPrecio() * producto.getStock();
                return String.format("S/ %.2f", valorTotal);
            }, "120px")
            
            .items(productos)
            .pageSize(4)
            .build();

        add(advancedTable);
    }

    /**
     * Crea datos de ejemplo para las tablas
     */
    private List<Producto> createProductData() {
        List<Producto> productos = new ArrayList<>();

        productos.add(new Producto("P001", "Laptop Dell Inspiron 15", "Electrónicos", 2499.99, 15));
        productos.add(new Producto("P002", "Mouse Inalámbrico Logitech", "Accesorios", 89.90, 0));
        productos.add(new Producto("P003", "Teclado Mecánico RGB", "Accesorios", 299.99, 5));
        productos.add(new Producto("P004", "Monitor 24\" Full HD", "Electrónicos", 899.99, 8));
        productos.add(new Producto("P005", "Silla Ergonómica Oficina", "Muebles", 599.99, 12));
        productos.add(new Producto("P006", "Escritorio de Madera", "Muebles", 799.99, 3));
        productos.add(new Producto("P007", "Auriculares Bluetooth", "Accesorios", 199.99, 30));
        productos.add(new Producto("P008", "Tablet Samsung Galaxy", "Electrónicos", 1299.99, 20));
        productos.add(new Producto("P009", "Cargador USB-C", "Accesorios", 49.99, 100));
        productos.add(new Producto("P010", "Lámpara LED Escritorio", "Iluminación", 129.99, 18));

        return productos;
    }

    /**
     * Clase de ejemplo para representar un producto
     */
    public static class Producto {
        private String codigo;
        private String nombre;
        private String categoria;
        private double precio;
        private int stock;

        public Producto(String codigo, String nombre, String categoria, double precio, int stock) {
            this.codigo = codigo;
            this.nombre = nombre;
            this.categoria = categoria;
            this.precio = precio;
            this.stock = stock;
        }

        // Getters
        public String getCodigo() {
            return codigo;
        }

        public String getNombre() {
            return nombre;
        }

        public String getCategoria() {
            return categoria;
        }

        public double getPrecio() {
            return precio;
        }

        public Integer getStock() {
            return stock;
        }

        // Setters
        public void setCodigo(String codigo) {
            this.codigo = codigo;
        }

        public void setNombre(String nombre) {
            this.nombre = nombre;
        }

        public void setCategoria(String categoria) {
            this.categoria = categoria;
        }

        public void setPrecio(double precio) {
            this.precio = precio;
        }

        public void setStock(int stock) {
            this.stock = stock;
        }
    }
}
