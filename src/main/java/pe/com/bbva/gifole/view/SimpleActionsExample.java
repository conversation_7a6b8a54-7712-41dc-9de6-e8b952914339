package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.html.H3;
import com.vaadin.flow.component.html.Paragraph;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.Route;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Ejemplo simple de uso de columnas de acciones
 * Demuestra cómo usar action-edit, action-delete y actions-edit-delete
 */
@Route("simple-actions")
public class SimpleActionsExample extends VerticalLayout {

    public SimpleActionsExample() {
        setSizeFull();
        setPadding(true);
        setSpacing(true);

        add(new H2("🎯 Columnas de Acciones - Ejemplo Simple"));
        add(new Paragraph("Demuestra el uso de action-edit, action-delete y actions-edit-delete"));

        // Crear datos de ejemplo
        List<Producto> productos = createProductData();

        // Ejemplo usando el DataTable tradicional con las nuevas funcionalidades
        add(new H3("📋 Tabla con Columnas de Acciones"));

        // Crear tabla usando el constructor tradicional pero con funcionalidades de acciones
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("codigo", "Código");
        headers.put("nombre", "Producto");
        headers.put("precio", "Precio");
        headers.put("stock", "Stock");
        headers.put("actions-edit-delete", "Acciones"); // ✅ Columna de acciones

        DataTable<Producto> productTable = new DataTable<>("productos-actions", headers, productos, 5);
        add(productTable);

        // Ejemplo con solo editar
        add(new H3("✏️ Solo Acción de Editar"));
        Map<String, String> editHeaders = new LinkedHashMap<>();
        editHeaders.put("codigo", "Código");
        editHeaders.put("nombre", "Producto");
        editHeaders.put("precio", "Precio");
        editHeaders.put("action-edit", "Editar"); // ✅ Solo editar

        DataTable<Producto> editTable = new DataTable<>("productos-edit", editHeaders, productos, 5);
        add(editTable);

        // Ejemplo con solo eliminar
        add(new H3("🗑️ Solo Acción de Eliminar"));
        Map<String, String> deleteHeaders = new LinkedHashMap<>();
        deleteHeaders.put("codigo", "Código");
        deleteHeaders.put("nombre", "Producto");
        deleteHeaders.put("stock", "Stock");
        deleteHeaders.put("action-delete", "Eliminar"); // ✅ Solo eliminar

        DataTable<Producto> deleteTable = new DataTable<>("productos-delete", deleteHeaders, productos, 5);
        add(deleteTable);

        // Agregar JavaScript para manejar los clicks
        addActionHandlers();
    }

    /**
     * Agrega los manejadores JavaScript para las acciones
     */
    private void addActionHandlers() {
        getElement().executeJs("""
            // Función para manejar edición
            window.editItem = function(itemId) {
                console.log('Editando item:', itemId);
                alert('🔧 Editando producto con ID: ' + itemId);
                // Aquí puedes agregar tu lógica de edición
            };
            
            // Función para manejar eliminación
            window.deleteItem = function(itemId) {
                console.log('Eliminando item:', itemId);
                if (confirm('🗑️ ¿Estás seguro de eliminar el producto con ID: ' + itemId + '?')) {
                    alert('✅ Producto eliminado: ' + itemId);
                    // Aquí puedes agregar tu lógica de eliminación
                }
            };
            """);
    }

    /**
     * Crea datos de ejemplo
     */
    private List<Producto> createProductData() {
        List<Producto> productos = new ArrayList<>();

        productos.add(new Producto("P001", "Laptop Dell", 2499.99, 15));
        productos.add(new Producto("P002", "Mouse Logitech", 89.90, 50));
        productos.add(new Producto("P003", "Teclado RGB", 299.99, 25));
        productos.add(new Producto("P004", "Monitor 24\"", 899.99, 8));
        productos.add(new Producto("P005", "Silla Ergonómica", 599.99, 12));
        productos.add(new Producto("P006", "Escritorio", 799.99, 5));
        productos.add(new Producto("P007", "Auriculares", 199.99, 30));
        productos.add(new Producto("P008", "Tablet Samsung", 1299.99, 20));

        return productos;
    }

    /**
     * Clase de ejemplo para productos
     */
    public static class Producto {
        private String codigo;
        private String nombre;
        private double precio;
        private int stock;

        public Producto(String codigo, String nombre, double precio, int stock) {
            this.codigo = codigo;
            this.nombre = nombre;
            this.precio = precio;
            this.stock = stock;
        }

        // Getters
        public String getCodigo() {
            return codigo;
        }

        public String getNombre() {
            return nombre;
        }

        public double getPrecioNumerico() {
            return precio;
        }

        public String getPrecio() {
            return String.format("S/ %.2f", precio);
        }

        public int getStock() {
            return stock;
        }

        // Método getId() para las acciones
        public String getId() {
            return codigo;
        }

        // Setters
        public void setCodigo(String codigo) {
            this.codigo = codigo;
        }

        public void setNombre(String nombre) {
            this.nombre = nombre;
        }

        public void setPrecio(double precio) {
            this.precio = precio;
        }

        public void setStock(int stock) {
            this.stock = stock;
        }
    }
}
