package pe.com.bbva.gifole.view;

import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.html.H3;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.Route;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Ejemplo de uso del DataTable con anchos de columna
 */
@Route("datatable-usage")
public class DataTableUsageExample extends VerticalLayout {

    public DataTableUsageExample() {
        setSizeFull();
        setPadding(true);
        setSpacing(true);

        add(new H2("DataTable - Ejemplos de Uso"));

        // Crear datos de ejemplo
        List<Producto> productos = createProductData();

        // Ejemplo 1: Tabla básica sin anchos específicos
        add(new H3("1. Tabla Básica (sin anchos específicos)"));
        Map<String, String> basicHeaders = new LinkedHashMap<>();
        basicHeaders.put("codigo", "Código");
        basicHeaders.put("nombre", "Producto");
        basicHeaders.put("categoria", "Categoría");
        basicHeaders.put("precio", "Precio");
        basicHeaders.put("stock", "Stock");

        DataTable<Producto> basicTable = new DataTable<>(
            "productos-basic", basicHeaders, productos, 5
        );
        add(basicTable);

        // Ejemplo 2: Tabla con anchos específicos
        add(new H3("2. Tabla con Anchos de Columna Específicos"));
        Map<String, DataTable.ColumnConfig> columnsWithWidth = new LinkedHashMap<>();
        columnsWithWidth.put("codigo", new DataTable.ColumnConfig("codigo", "Código", "100px"));
        columnsWithWidth.put("nombre", new DataTable.ColumnConfig("nombre", "Nombre del Producto", "300px"));
        columnsWithWidth.put("categoria", new DataTable.ColumnConfig("categoria", "Categoría", "150px"));
        columnsWithWidth.put("precio", new DataTable.ColumnConfig("precio", "Precio (S/)", "120px"));
        columnsWithWidth.put("stock", new DataTable.ColumnConfig("stock", "Stock", "80px"));

        DataTable<Producto> tableWithWidths = DataTable.withColumns(
            "productos-with-widths", columnsWithWidth, productos, 5
        );
        add(tableWithWidths);

        // Ejemplo 3: Tabla con anchos en porcentajes
        add(new H3("3. Tabla con Anchos en Porcentajes"));
        Map<String, DataTable.ColumnConfig> columnsWithPercentages = new LinkedHashMap<>();
        columnsWithPercentages.put("codigo", new DataTable.ColumnConfig("codigo", "Código", "10%"));
        columnsWithPercentages.put("nombre", new DataTable.ColumnConfig("nombre", "Producto", "40%"));
        columnsWithPercentages.put("categoria", new DataTable.ColumnConfig("categoria", "Categoría", "20%"));
        columnsWithPercentages.put("precio", new DataTable.ColumnConfig("precio", "Precio", "15%"));
        columnsWithPercentages.put("stock", new DataTable.ColumnConfig("stock", "Stock", "15%"));

        DataTable<Producto> tableWithPercentages = DataTable.withColumns(
            "productos-percentages", columnsWithPercentages, productos, 5
        );
        add(tableWithPercentages);
    }

    /**
     * Crea datos de ejemplo para las tablas
     */
    private List<Producto> createProductData() {
        List<Producto> productos = new ArrayList<>();

        productos.add(new Producto("P001", "Laptop Dell Inspiron 15", "Electrónicos", 2499.99, 15));
        productos.add(new Producto("P002", "Mouse Inalámbrico Logitech", "Accesorios", 89.90, 50));
        productos.add(new Producto("P003", "Teclado Mecánico RGB", "Accesorios", 299.99, 25));
        productos.add(new Producto("P004", "Monitor 24\" Full HD", "Electrónicos", 899.99, 8));
        productos.add(new Producto("P005", "Silla Ergonómica Oficina", "Muebles", 599.99, 12));
        productos.add(new Producto("P006", "Escritorio de Madera", "Muebles", 799.99, 5));
        productos.add(new Producto("P007", "Auriculares Bluetooth", "Accesorios", 199.99, 30));
        productos.add(new Producto("P008", "Tablet Samsung Galaxy", "Electrónicos", 1299.99, 20));
        productos.add(new Producto("P009", "Cargador USB-C", "Accesorios", 49.99, 100));
        productos.add(new Producto("P010", "Lámpara LED Escritorio", "Iluminación", 129.99, 18));
        productos.add(new Producto("P011", "Webcam HD 1080p", "Accesorios", 179.99, 22));
        productos.add(new Producto("P012", "Impresora Multifuncional", "Electrónicos", 399.99, 7));

        return productos;
    }

    /**
     * Clase de ejemplo para representar un producto
     */
    public static class Producto {
        private String codigo;
        private String nombre;
        private String categoria;
        private double precio;
        private int stock;

        public Producto(String codigo, String nombre, String categoria, double precio, int stock) {
            this.codigo = codigo;
            this.nombre = nombre;
            this.categoria = categoria;
            this.precio = precio;
            this.stock = stock;
        }

        // Getters
        public String getCodigo() {
            return codigo;
        }

        public String getNombre() {
            return nombre;
        }

        public String getCategoria() {
            return categoria;
        }

        public double getPrecioNumerico() {
            return precio;
        }

        public String getPrecio() {
            return String.format("S/ %.2f", precio);
        }

        public int getStock() {
            return stock;
        }

        // Setters
        public void setCodigo(String codigo) {
            this.codigo = codigo;
        }

        public void setNombre(String nombre) {
            this.nombre = nombre;
        }

        public void setCategoria(String categoria) {
            this.categoria = categoria;
        }

        public void setPrecio(double precio) {
            this.precio = precio;
        }

        public void setStock(int stock) {
            this.stock = stock;
        }
    }
}
