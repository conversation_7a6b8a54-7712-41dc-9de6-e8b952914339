/* Estilos para los botones de acción del DataTable Optimizado */

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
  padding: 6px;
}

.action-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.action-btn:active {
  transform: scale(0.95);
}

/* Botón de editar */
.edit-btn {
  color: #004481; /* Azul BBVA */
  background-color: rgba(0, 68, 129, 0.1);
}

.edit-btn:hover {
  background-color: rgba(0, 68, 129, 0.2);
  color: #003366;
}

.edit-btn:focus {
  outline: 2px solid #004481;
  outline-offset: 2px;
}

/* Botón de eliminar */
.delete-btn {
  color: #d32f2f; /* Rojo para eliminar */
  background-color: rgba(211, 47, 47, 0.1);
}

.delete-btn:hover {
  background-color: rgba(211, 47, 47, 0.2);
  color: #b71c1c;
}

.delete-btn:focus {
  outline: 2px solid #d32f2f;
  outline-offset: 2px;
}

/* Iconos SVG */
.action-btn svg {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

/* Responsive */
@media (max-width: 768px) {
  .action-btn {
    width: 28px;
    height: 28px;
    padding: 4px;
  }
  
  .action-btn svg {
    width: 14px;
    height: 14px;
  }
  
  .action-buttons {
    gap: 4px;
  }
}

/* Estados de accesibilidad */
.action-btn:focus-visible {
  outline: 2px solid #004481;
  outline-offset: 2px;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Animaciones suaves */
@keyframes buttonPress {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

.action-btn:active {
  animation: buttonPress 0.1s ease;
}

/* Tooltips mejorados */
.action-btn[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}

/* Integración con el tema BBVA */
.bbva-custom-datatable .action-buttons {
  font-family: 'BBVA Web Book', Arial, sans-serif;
}

.bbva-custom-datatable .action-btn {
  font-family: inherit;
}

/* Estados hover para toda la fila */
.data-table tbody tr:hover .action-btn {
  opacity: 1;
  visibility: visible;
}

.data-table tbody tr .action-btn {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.data-table tbody tr:hover .action-btn:hover {
  opacity: 1;
}
