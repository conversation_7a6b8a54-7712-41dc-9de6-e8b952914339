# WildFly EAP 8 con Datasource Oracle GIFOLE

Este proyecto configura automáticamente WildFly EAP 8 con un datasource Oracle conectado a la base de datos GIFOLE.

## ✅ Configuración Automática

### Datasource Oracle Configurado
- **Nombre**: `GifoleDS`
- **JNDI**: `java:jboss/datasources/GifoleDS`
- **Driver**: Oracle JDBC 23.3
- **URL**: `**********************************************`
- **Usuario**: `GIFOLE`
- **Contraseña**: `GIFOLE`

### Características
- ✅ **Driver Oracle JDBC** descargado e instalado automáticamente
- ✅ **Datasource configurado** mediante CLI de WildFly
- ✅ **Conexión verificada** automáticamente al inicio
- ✅ **Pool de conexiones** optimizado (5-20 conexiones)
- ✅ **Validación de conexiones** habilitada
- ✅ **Red compartida** con base de datos GIFOLE

## 🚀 Uso

### Iniciar WildFly con Datasource
```bash
docker-compose up -d
```

### Verificar Estado
```bash
docker-compose ps
docker-compose logs -f
```

### Acceder a Consola de Administración
- **URL**: http://localhost:9990
- **Usuario**: admin
- **Contraseña**: admin123

## 🔧 Configuración Técnica

### Archivos de Configuración
- `Dockerfile` - Imagen personalizada con Oracle JDBC
- `configure-datasource.cli` - Script CLI para configurar datasource
- `startup-with-datasource.sh` - Script de inicio con configuración automática
- `module.xml` - Módulo Oracle JDBC para WildFly

### Puertos Expuestos
- **8080** - Aplicaciones web
- **9990** - Consola de administración

### Red Docker
- **Red**: `gifole-bd_gifole-network`
- **Conecta con**: Base de datos Oracle GIFOLE

## 📋 Requisitos Previos

1. **Base de datos GIFOLE ejecutándose**:
   ```bash
   cd ../gifole-bd
   docker-compose up -d
   ```

2. **Docker y Docker Compose** instalados

## ✅ Verificación del Datasource

### Mediante CLI
```bash
curl -s http://localhost:9990/management \
  --user admin:admin123 --digest \
  -H "Content-Type: application/json" \
  -d '{"operation":"test-connection-in-pool","address":["subsystem","datasources","data-source","GifoleDS"]}'
```

### Mediante Consola Web
1. Acceder a http://localhost:9990
2. Login: admin/admin123
3. Configuration → Subsystems → Datasources & Drivers
4. Verificar "GifoleDS" está habilitado

## 🔄 Comandos Útiles

### Reconstruir y Reiniciar
```bash
docker-compose down
docker-compose build
docker-compose up -d
```

### Ver Logs en Tiempo Real
```bash
docker-compose logs -f
```

### Acceder al Contenedor
```bash
docker exec -it jboss-eap-8-server bash
```

## 📝 Uso en Aplicaciones Java

### Ejemplo de Lookup JNDI
```java
@Resource(lookup = "java:jboss/datasources/GifoleDS")
private DataSource gifoleDataSource;
```

### Ejemplo de persistence.xml
```xml
<persistence-unit name="gifole-pu">
    <jta-data-source>java:jboss/datasources/GifoleDS</jta-data-source>
    <!-- ... -->
</persistence-unit>
```

## 🎯 Estado del Sistema

- ✅ **WildFly EAP 8** ejecutándose en puerto 8080
- ✅ **Datasource GifoleDS** configurado y conectado
- ✅ **Base de datos GIFOLE** con 80 tablas y 136,685+ registros
- ✅ **Conexión verificada** automáticamente

**¡El sistema está listo para desplegar aplicaciones GIFOLE!**
