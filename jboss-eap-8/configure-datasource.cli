# WildFly CLI script to configure Oracle datasource for GIFOLE
# This script configures the Oracle JDBC driver and creates the GIFOLE datasource
# Note: This script runs against a running WildFly instance

# Add Oracle JDBC driver module
/subsystem=datasources/jdbc-driver=oracle:add(driver-name=oracle,driver-module-name=com.oracle.ojdbc11,driver-class-name=oracle.jdbc.OracleDriver)

# Add GIFOLE datasource
data-source add \
    --name=GifoleDS \
    --jndi-name=java:jboss/datasources/GifoleDS \
    --driver-name=oracle \
    --connection-url=********************************************** \
    --user-name=GIF<PERSON><PERSON> \
    --password=GIFOLE \
    --validate-on-match=true \
    --background-validation=false \
    --valid-connection-checker-class-name=org.jboss.jca.adapters.jdbc.extensions.oracle.OracleValidConnectionChecker \
    --exception-sorter-class-name=org.jboss.jca.adapters.jdbc.extensions.oracle.OracleExceptionSorter \
    --min-pool-size=5 \
    --max-pool-size=20 \
    --pool-prefill=true \
    --transaction-isolation=TRANSACTION_READ_COMMITTED \
    --check-valid-connection-sql="SELECT 1 FROM DUAL"

# Enable the datasource
data-source enable --name=GifoleDS

# Test the connection
/subsystem=datasources/data-source=GifoleDS:test-connection-in-pool
