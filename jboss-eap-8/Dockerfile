# JBoss EAP 8 Docker Image (using Wild<PERSON>ly as development alternative)
FROM openjdk:21-jdk-slim

# Set environment variables
ENV JBOSS_HOME=/opt/jboss/wildfly
ENV PATH="${JBOSS_HOME}/bin:${PATH}"

# Install packages, create user, download and install WildFly 28.x
# Note: For production, you would need actual JBoss EAP 8 binaries with proper licensing
RUN apt-get update && \
    apt-get install -y curl wget && \
    rm -rf /var/lib/apt/lists/* && \
    groupadd -r jboss && \
    useradd -r -g jboss -d "${JBOSS_HOME}" -s /bin/false jboss && \
    mkdir -p /opt/jboss && \
    curl -L https://github.com/wildfly/wildfly/releases/download/28.0.1.Final/wildfly-28.0.1.Final.tar.gz \
    | tar xz -C /opt/jboss && \
    mv /opt/jboss/wildfly-28.0.1.Final /opt/jboss/wildfly && \
    mkdir -p "${JBOSS_HOME}/standalone/deployments" && \
    chown -R jboss:jboss "${JBOSS_HOME}"

# Download Oracle JDBC driver
ADD https://download.oracle.com/otn-pub/otn_software/jdbc/233/ojdbc11.jar /tmp/ojdbc11.jar

# Copy module.xml for Oracle driver
COPY module.xml "${JBOSS_HOME}/modules/system/layers/base/com/oracle/ojdbc11/main/"

# Copy CLI configuration scripts and startup script
COPY configure-datasource.cli "${JBOSS_HOME}/"
COPY startup-with-datasource.sh "${JBOSS_HOME}/"

# Create management user, setup Oracle JDBC driver and set permissions
RUN "${JBOSS_HOME}/bin/add-user.sh" -u admin -p admin123 -s && \
    mkdir -p "${JBOSS_HOME}/modules/system/layers/base/com/oracle/ojdbc11/main" && \
    cp /tmp/ojdbc11.jar "${JBOSS_HOME}/modules/system/layers/base/com/oracle/ojdbc11/main/" && \
    chown -R jboss:jboss "${JBOSS_HOME}/modules" && \
    chown jboss:jboss "${JBOSS_HOME}/configure-datasource.cli" && \
    chown jboss:jboss "${JBOSS_HOME}/startup-with-datasource.sh" && \
    chmod +x "${JBOSS_HOME}/startup-with-datasource.sh"

# Switch to jboss user
USER jboss

# Expose ports
EXPOSE 8080 9990

# Set working directory
WORKDIR "${JBOSS_HOME}"

# Default command - use startup script that configures datasource
CMD ["./startup-with-datasource.sh"]