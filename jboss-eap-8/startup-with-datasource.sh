#!/bin/bash

# Startup script for <PERSON><PERSON><PERSON> with automatic datasource configuration
echo "Starting WildFly with GIFOLE datasource configuration..."

# Start WildFly in background
$JBOSS_HOME/bin/standalone.sh -b 0.0.0.0 -bmanagement 0.0.0.0 &
WILDFLY_PID=$!

# Wait for <PERSON><PERSON><PERSON> to start
echo "Waiting for <PERSON><PERSON><PERSON> to start..."
sleep 30

# Check if <PERSON><PERSON><PERSON> is running
if ! kill -0 $WILDFLY_PID 2>/dev/null; then
    echo "WildFly failed to start"
    exit 1
fi

# Configure datasource using CLI
echo "Configuring GIFOLE datasource..."
$JBOSS_HOME/bin/jboss-cli.sh --connect --controller=localhost:9990 --user=admin --password=admin123 --file=$JBOSS_HOME/configure-datasource.cli

if [ $? -eq 0 ]; then
    echo "Datasource configured successfully"
else
    echo "Failed to configure datasource"
fi

# Keep WildFly running in foreground
wait $WILDFLY_PID
